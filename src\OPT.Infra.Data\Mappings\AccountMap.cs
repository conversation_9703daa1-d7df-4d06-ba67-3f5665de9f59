﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class AccountMap : Mapping<Account>
    {
        public override void Map(EntityTypeBuilder<Account> builder)
        {
            builder.ToTable("Accounts");

            builder.HasKey(x => x.ID);

            builder.HasOne(x => x.Store).WithMany().HasForeignKey(x => x.StoreID).OnDelete(DeleteBehavior.NoAction);

            builder.HasMany(x => x.AccountVehicleRegistrations).WithOne().HasForeign<PERSON>ey(x => x.AccountId);
            builder.HasMany(x => x.AccountBalances).WithOne().HasForeignKey(x => x.AccountId);

            base.Map(builder);
        }
    }
}
