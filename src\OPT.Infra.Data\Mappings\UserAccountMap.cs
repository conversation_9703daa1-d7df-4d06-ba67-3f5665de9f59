﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class UserAccountMap : Mapping<UserAccount>
    {
        public override void Map(EntityTypeBuilder<UserAccount> builder)
        {
            builder.ToTable("UserAccounts");

            builder.Has<PERSON>ey(x => x.ID);

            builder.HasOne(x => x.Account).WithMany().HasForeignKey(x => x.AccountID).OnDelete(DeleteBehavior.NoAction);

            base.Map(builder);
        }
    }
}
