﻿using OPT.Domain.Models;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class AccountValidSpecification : ISpecificationConfiguration<Account>
    {

        private readonly ApiKey apiKey;

        public AccountValidSpecification(ApiKey apiKey)
        {
            this.apiKey = apiKey;
        }

        public ISpecification<Account> Map(ISpecification<Account> builder)
        {
            builder.SetStopOnFirstFailure(true);

            builder.IsSatisfiedBy(x => x != null, "Account does not exist!", 404);
            
            builder.IsSatisfiedBy(x => x.StoreID.Equals(apiKey.StoreID), "You are not authorized to get this account!", 401);

            return builder;
        }
    }
}
