﻿namespace OPT.Domain.Models
{
    public class ParameterConfig
    {
        public int ID { get; set; }
        public string InputType { get; set; }
        public string? DataType { get; set; }
        public string PID { get; set; }
        public string Title { get; set; }
        public int? Length { get; set; }
        public bool Required { get; set; }
        public string? Defaultvalue { get; set; }
        public string? Description { get; set; }
        public string? Select { get; set; }
        public int Order { get; set; }
        public bool Active { get; set; }
        public string Group { get; set; }
    }
}
