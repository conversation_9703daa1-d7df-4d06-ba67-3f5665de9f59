﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class AccountVehicleRegistrationMap : Mapping<AccountVehicleRegistration>
    {
        public override void Map(EntityTypeBuilder<AccountVehicleRegistration> builder)
        {
            builder.ToTable("AccountVehicleRegistrations");

            builder.HasKey(x => x.ID);

            //builder.HasOne(x => x.Account).WithMany().HasForeignKey(x => x.AccountId).OnDelete(DeleteBehavior.NoAction);
            builder.HasOne(x => x.VehicleRegistration).WithMany().HasForeignKey(x => x.VehicleRegistrationId).OnDelete(DeleteBehavior.NoAction);

            base.Map(builder);
        }
    }
}
