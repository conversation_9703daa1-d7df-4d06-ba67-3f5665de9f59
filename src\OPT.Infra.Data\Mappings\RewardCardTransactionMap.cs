﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class RewardCardTransactionMap : Mapping<RewardCardTransaction>
    {
        public override void Map(EntityTypeBuilder<RewardCardTransaction> builder)
        {
            builder.ToTable("RewardCardTransactions");

            builder.<PERSON><PERSON><PERSON>(x => x.ID);

            builder.HasOne(x => x.Kiosk).WithMany().HasForeignKey(x => x.KioskId).OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(x => x.RewardCardDiscount).WithMany().HasForeignKey(x => x.RewardCardDiscountID).OnDelete(DeleteBehavior.NoAction);

            base.Map(builder);
        }
    }
}
