﻿using OPT.Domain.Models;

namespace OPT.Domain.Interfaces.Services
{
    public interface ITransactionAuditService : IDisposable
    {
        void Add(TransactionAudit model);

        List<List<TransactionAudit>> GetByFilter(Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId);

        (int, List<List<TransactionAudit>>) GetByFilterWithPagination(Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId, int offset, int limit);
    }
}
