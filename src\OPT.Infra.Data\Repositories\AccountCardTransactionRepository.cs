﻿using Microsoft.EntityFrameworkCore;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Models;
using OPT.Infra.Data.Context;

namespace OPT.Infra.Data.Repositories
{
    public class AccountCardTransactionRepository : Repository<AccountCardTransaction>, IAccountCardTransactionRepository
    {
        public AccountCardTransactionRepository(OPTContext context) : base(context)
        {
        }

        public AccountCardTransaction? GetByAuthCodeSTANAndTerminalID(string authCode, long STAN, string terminalId)
        {
            return _dbset
                .AsNoTracking()
                .Include(x => x.AccountCard)
                .FirstOrDefault(x => x.STAN == STAN && x.TerminalID == terminalId && x.AuthCode == authCode);
        }
    }
}
