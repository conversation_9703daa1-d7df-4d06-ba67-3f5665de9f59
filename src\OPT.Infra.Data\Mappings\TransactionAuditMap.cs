﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class TransactionAuditMap : Mapping<TransactionAudit>
    {
        public override void Map(EntityTypeBuilder<TransactionAudit> builder)
        {
            builder.ToTable("TransactionAudits");

            builder.<PERSON>Key(x => x.ID);

            builder.Property(x => x.AuditDate).ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);

            builder.HasOne(x => x.Kiosk).WithMany().HasForeignKey(x => x.KioskID).OnDelete(DeleteBehavior.NoAction);

            base.Map(builder);
        }
    }
}
