﻿using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;

namespace OPT.Domain.Services
{
    public class ApiKeyService : IApiKeyService
    {
        private readonly IApiKeyRepository _repository;

        public ApiKeyService(IApiKeyRepository repository)
        {
            _repository = repository;
        }

        public void Add(ApiKey model, string accessToken)
        {
            model.AccessToken = accessToken;
            model.TokenDateTime = DateTime.Now;
            model.Active = true;
            _repository.Add(model);
        }
        
        public void UpdateAccessToken(Guid id, string accessToken)
        {
            var current = _repository.GetById(id);
            if (current != null)
            {
                current.AccessToken = accessToken;
                current.TokenDateTime = DateTime.Now;
                _repository.Update(current);
            }
        }
        
        public void UpdateActive(Guid id, bool ative)
        {
            var current = _repository.GetById(id);
            if (current != null)
            {
                current.Active = ative;
                _repository.Update(current);
            }
        }

        public List<ApiKey> GetByStores(List<Guid> storesId)
        {
            return _repository.GetByStores(storesId);
        }

        public ApiKey? GetByAccessToken(string accessToken)
        {
            return _repository.Find(x => x.AccessToken == accessToken).FirstOrDefault();
        }

        public void Remove(Guid id)
        {
            _repository.Remove(id);
        }

        public void Dispose()
        {
            _repository.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
