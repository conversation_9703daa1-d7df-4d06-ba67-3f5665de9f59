﻿using OPT.Domain.Models;


namespace OPT.Domain.Interfaces.Repositories
{
    public interface ITransactionRepository : IRepository<Transaction>
    {
        
        List<Transaction> GetByUser(string userId);
        List<Transaction> GetByFilter(Guid? accountId, Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId);
        List<Transaction> GetByFilterWithPagination(Guid? accountId, Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId, int offset, int limit);
        int GetTotalByFilter(Guid? accountId, Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId);
        List<FuelTransaction> GetTotalGradeByFilter(Guid? accountId, Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId);
        List<Transaction> GetUnitedToExport(Guid tenantID);
        void SetUnitedExported(IEnumerable<Guid> ids);
        Transaction GetByTransaction(int TransactionId);
        Transaction? GetByTransactionIdAndKiosk(int transactionId, Guid kioskId);
        Transaction? GetByTransactionIdAndTransactionDate(int transactionId, DateTime transactionDate);
        Task SendEmail(string email, string subject, string htmlMessage, params AttachmentModel[] attachments);

    }
}
