﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Configuration;
using Microsoft.Extensions.Logging.EventLog;
using OPT.Scheduler.Configuration;
using OPT.Scheduler.Workers;

/**
 * Install services
 * 
 * sc create OPTScheduler binpath="<fullpath>\OPT.Scheduler.exe"
 * 
 */

HostApplicationBuilder builder = Host.CreateApplicationBuilder(args);
builder.Services.AddWindowsService(options =>
{
    options.ServiceName = "OPT Scheduler";
});

if (OperatingSystem.IsWindows())
{
    LoggerProviderOptions.RegisterProviderOptions<
    EventLogSettings, EventLogLoggerProvider>(builder.Services);
}

builder.Services.AddIdentitySetup(builder.Configuration);
builder.Services.AddAutomapperSetup();
builder.Services.AddDIConfiguration();

builder.Services.AddHostedService<UnitedImporterWorker>();
builder.Services.AddHostedService<UnitedExporterWorker>();

builder.Logging.AddConfiguration(
    builder.Configuration.GetSection("Logging"));

if (OperatingSystem.IsWindows())
{
    builder.Services.Configure<EventLogSettings>(config =>
    {
        if (OperatingSystem.IsWindows())
        {
            config.LogName = "OPT Scheduler Service";
            config.SourceName = "OPT Scheduler Source";
        }
    });
}

IHost host = builder.Build();
host.Run();