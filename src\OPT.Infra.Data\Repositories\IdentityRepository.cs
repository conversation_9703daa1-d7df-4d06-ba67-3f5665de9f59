﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.EntityFrameworkCore;
using OPT.Domain.Exceptions;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Models;
using OPT.Infra.Data.Helper;
using OPT.Infra.Data.Identity.Context;
using OPT.Infra.Data.Identity.Model;
using System.Text;

namespace OPT.Infra.Data.Repositories
{
    public class IdentityRepository : IIdentityRepository
    {
        private readonly UserManager<OPTUser> _userManager;
        private readonly UserStoreCustom _userStore;
        private readonly IEmailSenderHelper _emailSender;

        public IdentityRepository(UserManager<OPTUser> userManager, UserStoreCustom userStore, IEmailSenderHelper emailSender)
        {
            _userManager = userManager;
            _userStore = userStore;
            _emailSender = emailSender;
        }

        public async Task<User?> GetByName(string username)
        {
            return GetUserModel(await _userManager.FindByNameAsync(username));
        }

        public async Task<User?> GetById(Guid id)
        {
            return GetUserModel(await _userManager.FindByIdAsync(id.ToString()));
        }
        
        public async Task<User?> GetByEmail(string email)
        {
            return GetUserModel(await _userManager.FindByEmailAsync(email));
        }
        
        public async Task<User?> GetByPhoneNumber(string phoneNumber)
        {
            return GetUserModel(await _userStore.Users.FirstOrDefaultAsync(x => x.PhoneNumber == phoneNumber));
        }

        public async Task<Guid?> Add(User model)
        {
            OPTUser user = new()
            {
                FirstName = model.FirstName,
                LastName = model.LastName,
                PhoneNumber = model.PhoneNumber,
                Active = true,
                CompanyHash = model.CompanyHash,
                UserName = model.UserName,
                Email = model.UserName,
                EmailConfirmed = false
            };
            IdentityResult result = await _userManager.CreateAsync(user);
            if (result.Succeeded)
            {
                await _userManager.AddToRoleAsync(user, model.Role);
                //var code = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                //code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
                return new Guid(user.Id);
            }
            else
            {
                throw new BusinessException(string.Join("\n", result.Errors.Select(x => x.Description).ToArray()));
            }
        }
        
        public async Task Update(User model)
        {
            OPTUser user = await _userManager.FindByIdAsync(model.Id.ToString());
            user.FirstName = model.FirstName;
            user.LastName = model.LastName;
            user.PhoneNumber = model.PhoneNumber;
            user.Active = model.Active;

            IdentityResult result = await _userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                throw new BusinessException(string.Join("\n", result.Errors.Select(x => x.Description).ToArray()));
            }
        }

        public async Task<string> ConfirmEmail(string email)
        {
            var user = await _userManager.FindByEmailAsync(email.Trim());
            if (user == null)
            {
                throw new BusinessException($"Unable to load user with email '{email}'.");
            }

            user.EmailConfirmed = true;
            user.CompanyHash = string.Empty;
            var result = await _userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                throw new BusinessException("Error confirming your email.");
            }
            else
            {
                var passCode = await _userManager.GeneratePasswordResetTokenAsync(user);
                return WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(passCode));
            }
        }

        public async Task ResetPassword(Guid? id, string? email, string passToken, string newPassword)
        {
            OPTUser user = null;
            if(id != null)
                user = await _userManager.FindByIdAsync(id.ToString());
            else if (!string.IsNullOrEmpty(email))
                user = await _userManager.FindByEmailAsync(email);

            if (user == null)
            {
                throw new BusinessException("User not found.");
            }

            var result = await _userManager.ResetPasswordAsync(user, Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(passToken)), newPassword);
            if (!result.Succeeded)
            {
                foreach (var error in result.Errors)
                {
                    throw new BusinessException(string.Join("\n", result.Errors.Select(x => x.Description).ToArray()));
                }
            }
        }

        public async Task Remove(Guid id)
        {
            OPTUser user = await _userManager.FindByIdAsync(id.ToString());
            if (user != null)
            {
                IdentityResult result = await _userManager.DeleteAsync(user);
                if (!result.Succeeded)
                    throw new BusinessException(string.Join("\n", result.Errors.Select(x => x.Description).ToArray()));
            }
        }

        public async Task<List<User>> GetUsersByIds(List<string> ids)
        {
            var users = await _userManager.Users.Where(x => ids.Contains(x.Id)).ToListAsync();
            return users.Select(x => GetUserModel(x)).ToList();
        }

        public async Task<User?> Authenticate(string username, string password)
        {
            var user = await _userManager.FindByNameAsync(username);
            if (user != null && await _userManager.CheckPasswordAsync(user, password))
            {
                if(!user.Active)
                    throw new BusinessException("This user is not active.");
                if (!user.EmailConfirmed)
                    throw new BusinessException("Please, follow the instructions received in your email account to confirm your email.");
                return GetUserModel(user);
            }

            throw new BusinessException("Invalid login attempt.");
        }

        public async Task<bool> ForgotPassword(string userName, string companyHash)
        {
            var user = await _userManager.FindByEmailAsync(userName);
            if (user != null)
            {
                user.CompanyHash = companyHash;
                var result = await _userManager.UpdateAsync(user);
                if (result.Succeeded)
                {
                    return true;
                }
            }
            throw new BusinessException("Error confirming your email.");
        }
        
        /*public IEnumerable<User> GetAll()
        {
            return _userManager.Users.ToList().Select(x => GetUserModel(x));
        }

        public async Task UpdateRole(Guid id, string newrole)
        {
            var user = await _userManager.FindByIdAsync(id.ToString());
            if (user != null)
            {
                var roles = await _userManager.GetRolesAsync(user);
                bool isInRole = false;
                foreach (var role in roles)
                {
                    if (role != newrole)
                        await _userManager.RemoveFromRoleAsync(user, role);
                    else
                        isInRole = true;
                }
                if (!isInRole)
                    await _userManager.AddToRoleAsync(user, newrole);
            }
        }*/

        public async Task SendEmail(string email, string subject, string htmlMessage)
        {
            await _emailSender.SendEmailAsync(
                    email,
                    subject,
                    htmlMessage);
        }

        private User? GetUserModel(OPTUser identity)
        {
            if (identity != null)
            {
                return new User()
                {
                    Id = new Guid(identity.Id),
                    FirstName = identity.FirstName,
                    LastName = identity.LastName,
                    PhoneNumber = identity.PhoneNumber,
                    CompanyHash = identity.CompanyHash,
                    Active = identity.Active,
                    UserName = identity.UserName,
                    EmailConfirmed = identity.EmailConfirmed,
                    Email = identity.Email,
                    NormalizedUserName = identity.NormalizedEmail,
                    Role = _userManager.GetRolesAsync(identity).Result[0]
                };
            }
            else
                return null;
        }

        public void Dispose()
        {
            GC.SuppressFinalize(this);
        }
    }
}
