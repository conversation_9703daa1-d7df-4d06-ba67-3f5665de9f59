﻿using AutoMapper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.TransactionAudit;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Interfaces.UoW;
using OPT.Domain.Models;

namespace OPT.Application
{
    public class TransactionAuditAppService : ApplicationService, ITransactionAuditAppService
    {
        private readonly ITransactionAuditService _service;
        private readonly IAccountCardTransactionService _accountCardTransactionService;
        private readonly IMapper _mapper;

        public TransactionAuditAppService(IUnitOfWork unitOfWork,
                                IMapper mapper,
                                ITransactionAuditService service,
                                IAccountCardTransactionService accountCardTransactionService) : base(unitOfWork)
        {
            _service = service;
            _mapper = mapper;
            _accountCardTransactionService = accountCardTransactionService;
        }

        public void Add(TransactionAuditAddViewModel model, Kiosk kiosk)
        {
            var tra = _mapper.Map<TransactionAudit>(model);
            tra.KioskID = kiosk.ID;

            if(model.AuthorizationID != null && model.STAN != null && model.TID != null)
            {
                var accctrx = _accountCardTransactionService.GetByAuthCodeSTANAndTerminalID(model.AuthorizationID.PadLeft(6, '0'), Convert.ToInt64(model.STAN), model.TID);
                if (accctrx != null)
                {
                    tra.Processor = "WC";
                    tra.AccountID = accctrx.AccountCard?.AccountId;
                    tra.AccountCardID = accctrx.AccountCard?.ID;
                }
            }
            _service.Add(tra);
            Commit();
        }

        public List<GroupTransactionAuditViewModel> GetByFilter(TransactionAuditFilterViewModel filter)
        {
            List<List<TransactionAudit>> t = _service.GetByFilter(filter.KioskId, filter.DateFrom, filter.DateTo, filter.StoresId);
            var tvms = _mapper.Map<List<List<TransactionAuditViewModel>>>(t);

            return tvms.Select(x => new GroupTransactionAuditViewModel(x)).ToList();
        }

        public TransactionAuditPagination GetByFilterWithPagination(TransactionAuditFilterV2ViewModel filter)
        {
            (int total, List<List<TransactionAudit>> t) = _service.GetByFilterWithPagination(filter.KioskId, filter.DateFrom, filter.DateTo, filter.StoresId, filter.Offset, filter.Limit);
            var tvms = _mapper.Map<List<List<TransactionAuditViewModel>>>(t);

            return new TransactionAuditPagination
            {
                TotalRecords = total,
                Offset = filter.Offset,
                PageRecords = tvms.Count,
                List = tvms.Select(x => new GroupTransactionAuditViewModel(x)).ToList()
            };
        }

        public void Dispose()
        {
            _service.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
