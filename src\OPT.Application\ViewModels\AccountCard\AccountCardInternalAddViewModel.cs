﻿using OPT.Application.ViewModels.RewardCard;
using OPT.Application.ViewModels.Store;

namespace OPT.Application.ViewModels.AccountCard
{
    public class AccountCardInternalAddViewModel
    {
        public required string PAN { get; set; }

        public required string ExpiryDate { get; set; }

        public bool Active { get; set; }

        public decimal? TransactionLimit { get; set; }

        public string? PIN { get; set; }

        public Guid? AccountId { get; set; }

        public string? PAN19 { get; set; }

        public string? BarCode { get; set; }

        public string? HolderName { get; set; }

        public char Type { get; set; }

        public string? Status { get; set; }

        public string? VehicleOdometer { get; set; }

        public StoreViewModel? Store { get; set; }

        public List<RewardCardDiscountViewModel>? Discounts { get; set; }

        public List<AccountCardRestrictionViewModel>? Restrictions { get; set; }
    }
}
