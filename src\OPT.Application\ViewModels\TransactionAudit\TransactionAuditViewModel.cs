﻿
using OPT.Application.ViewModels.Kiosk;

namespace OPT.Application.ViewModels.TransactionAudit
{

    public class GroupTransactionAuditViewModel
    {
        public DateTime TransactionCreatedDate => this.Transactions.First().TransactionCreatedDate;
        public long KioskTransactionNumber => this.Transactions.First().KioskTransactionNumber;
        public string Processor => this.Transactions.Any(x => x.Processor == "WC") ? "WC" : this.Transactions.First().Processor;
        public string? VehicleRegistration => this.Transactions.FirstOrDefault(x => !string.IsNullOrEmpty(x.VehicleRegistration))?.VehicleRegistration;
        public string? VehicleOdometer => this.Transactions.FirstOrDefault(x => !string.IsNullOrEmpty(x.VehicleOdometer))?.VehicleOdometer;
        public string? PumpNumber => this.Transactions.First().PumpNumber;

        public KioskTransactionAuditViewModel Kiosk => this.Transactions.First().Kiosk;

        public List<TransactionAuditViewModel> Transactions { get; }

        public GroupTransactionAuditViewModel(List<TransactionAuditViewModel> transactions) 
        { 
            this.Transactions = transactions.OrderBy(x => x.AuditDate).ToList();
        }
    }

    public class TransactionAuditViewModel
    {
        public DateTime AuditDate { get; set; }
        public DateTime TransactionCreatedDate { get; set; }
        public long KioskTransactionNumber { get; set; }
        public string? CardSignature { get; set; }
        public string? CardExpiryDate { get; set; }
        public required string Processor { get; set; }
        public string? TransactionStatus { get; set; }
        public decimal? TransactionAmount { get; set; }
        public decimal? TransactionFinalAmount { get; set; }
        public string? TransactionResponseCode { get; set; }
        public string? TransactionResponse { get; set; }
        public string? VehicleRegistration { get; set; }
        public string? VehicleOdometer { get; set; }
        public string? PumpNumber { get; set; }
        public string? FuelGrade { get; set; }
        public string? FuelGradeName { get; set; }
        public KioskTransactionAuditViewModel Kiosk { get; set; }
    }
}
