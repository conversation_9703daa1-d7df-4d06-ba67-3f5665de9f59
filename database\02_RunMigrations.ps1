# =============================================
# OPTdb Entity Framework Migrations Script
# Description: Runs EF Core migrations to create database schema
# =============================================

param(
    [string]$Environment = "Development",
    [switch]$Force = $false,
    [switch]$Verbose = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$SolutionRoot = Split-Path -Parent $ScriptDir

Write-Host "=== OPTdb Database Migration Script ===" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "Solution Root: $SolutionRoot" -ForegroundColor Yellow

try {
    # Change to solution directory
    Set-Location $SolutionRoot
    
    # Check if dotnet CLI is available
    if (-not (Get-Command "dotnet" -ErrorAction SilentlyContinue)) {
        throw "dotnet CLI is not installed or not in PATH"
    }
    
    # Check if Entity Framework tools are installed
    Write-Host "Checking Entity Framework tools..." -ForegroundColor Cyan
    $efTools = dotnet tool list --global | Select-String "dotnet-ef"
    if (-not $efTools) {
        Write-Host "Installing Entity Framework tools..." -ForegroundColor Yellow
        dotnet tool install --global dotnet-ef
    }
    
    # Set environment variable
    $env:ASPNETCORE_ENVIRONMENT = $Environment
    Write-Host "Set ASPNETCORE_ENVIRONMENT to: $Environment" -ForegroundColor Cyan
    
    # Navigate to the Data project directory
    $DataProjectPath = Join-Path $SolutionRoot "src\OPT.Infra.Data"
    if (-not (Test-Path $DataProjectPath)) {
        throw "Data project not found at: $DataProjectPath"
    }
    
    Set-Location $DataProjectPath
    Write-Host "Changed to Data project directory: $DataProjectPath" -ForegroundColor Cyan
    
    # Check current migration status
    Write-Host "Checking current migration status..." -ForegroundColor Cyan
    if ($Verbose) {
        dotnet ef migrations list --startup-project ..\OPT.API\OPT.API.csproj --verbose
    } else {
        dotnet ef migrations list --startup-project ..\OPT.API\OPT.API.csproj
    }
    
    # Apply migrations to database
    Write-Host "Applying migrations to database..." -ForegroundColor Cyan
    $migrationArgs = @(
        "ef", "database", "update",
        "--startup-project", "..\OPT.API\OPT.API.csproj"
    )
    
    if ($Verbose) {
        $migrationArgs += "--verbose"
    }
    
    if ($Force) {
        Write-Host "Force flag is set - this will overwrite existing database!" -ForegroundColor Red
        $confirmation = Read-Host "Are you sure you want to continue? (y/N)"
        if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
            Write-Host "Migration cancelled by user." -ForegroundColor Yellow
            exit 0
        }
    }
    
    & dotnet $migrationArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "=== Database migrations completed successfully! ===" -ForegroundColor Green
        
        # Apply Identity migrations
        Write-Host "Applying Identity migrations..." -ForegroundColor Cyan
        dotnet ef database update --context IdentityOPTContext --startup-project ..\OPT.API\OPT.API.csproj
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "=== Identity migrations completed successfully! ===" -ForegroundColor Green
        } else {
            Write-Host "Identity migrations failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            exit $LASTEXITCODE
        }
    } else {
        Write-Host "Database migrations failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
    
} catch {
    Write-Host "Error occurred during migration: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.Exception.StackTrace)" -ForegroundColor Red
    exit 1
} finally {
    # Return to original directory
    Set-Location $SolutionRoot
}

Write-Host "=== Migration script completed ===" -ForegroundColor Green
