﻿using OPT.Application.ViewModels.VehicleRegistration;
using OPT.Domain.Models;

namespace OPT.Application.Interfaces
{
    public interface IVehicleRegistrationAppService : IDisposable
    {
        void Add(VehicleRegistrationAddViewModel viewModel);
        Guid? Add(VehicleRegistrationAddViewModel viewModel, ApiKey apiKey);
        List<VehicleRegistrationViewModel> ListByAccount(Guid accountId);
        List<VehicleRegistrationViewModel> ListByAccount(Guid accountId, ApiKey apiKey);

        VehicleRegistrationViewModel? GetById(Guid id);
        VehicleRegistrationViewModel? GetById(Guid id, ApiKey apiKey);
        void Update(VehicleRegistrationViewModel viewModel);
        void Update(Guid id, VehicleRegistrationAddViewModel viewModel, ApiKey apiKey);
        void Delete(Guid cardId);
        void Delete(Guid cardId, Api<PERSON><PERSON> apiKey);
    }
}
