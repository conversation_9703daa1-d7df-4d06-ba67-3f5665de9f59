﻿using OPT.Application.ViewModels.Account;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class AccountAddSpecification : ISpecificationConfiguration<AccountAddViewModel>
    {

        public AccountAddSpecification()
        {
        }

        public ISpecification<AccountAddViewModel> Map(ISpecification<AccountAddViewModel> builder)
        {
            builder.IsSatisfiedBy(x => !string.IsNullOrWhiteSpace(x.Name), "Name is required!", 407);

            return builder;
        }
    }
}
