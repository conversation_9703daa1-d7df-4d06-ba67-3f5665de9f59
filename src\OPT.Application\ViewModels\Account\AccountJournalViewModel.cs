﻿using OPT.Domain.Models.Enumerations;

namespace OPT.Application.ViewModels.Account
{
    public class AccountJournalViewModel
    {
        public DateTime JournalDate { get; set; }
        public Guid AccountId { get; set; }
        public JournalType JournalTypeId { get; set; }
        public decimal Amount { get; set; }
        public string POSMasterUserId { get; set; }
        public string? Reason { get; set; }
    }
}
