﻿{
  "$schema": "https://json.schemastore.org/launchsettings.json",
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      //"applicationUrl": "http://*************:49512",
      "applicationUrl": "http://localhost:49512",
      "sslPort": 44374
    }
  },
  "profiles": {
    "OPT.API": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      //"applicationUrl": "http://*************:7225;http://*************:5225",
      "applicationUrl": "http://localhost:7225;http://localhost:5225",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
