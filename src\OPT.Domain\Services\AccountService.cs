﻿using OPT.Domain.Exceptions;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;

namespace OPT.Domain.Services
{
    public class AccountService : IAccountService
    {
        private readonly IAccountRepository _repository;

        public AccountService(IAccountRepository repository)
        {
            _repository = repository;
        }

        public Account? GetById(Guid id)
        {
            return _repository.GetById(id);
        }

        public void Add(Account model)
        {
            var s = _repository.GetById(model.ID);
            if (s == null)
                _repository.Add(model);
            else
                _repository.Update(s, model);
        }
        
        public void Add(Account model, Guid storeId)
        {
            model.StoreID = storeId;
            _repository.Add(model);
        }
        
        public void Update(Guid id,Account model, Guid storeId)
        {
            var s = _repository.GetById(id);
            if (!s.StoreID.Equals(storeId))
                throw new BusinessException("You are not authorized to change this account", 401);
            model.ID = id;
            model.StoreID = storeId;
            _repository.Update(s, model);
        }

        public void AddOrUpdateUser(Guid userId, UserAccount entity)
        {
            entity.UserID = userId.ToString();
            _repository.AddOrUpdateUser(entity);
        }

        public void RemoveUser(Guid userId)
        {
            _repository.RemoveUser(userId);
        }

        public List<UserAccount> GetUsers(Guid id)
        {
            return _repository.GetUsers(id);
        }

        public Account? GetByUser(Guid userId)
        {
            return _repository.GetByUser(userId);
        }

        public List<Account> GetByStores(List<Guid>? storesId, List<Guid>? tenantsId)
        {
            return _repository.GetByStores(storesId, tenantsId);
        }
        
        public (int, List<Account>) GetByStoresWithPagination(List<Guid>? storesId, List<Guid>? tenantsId, int offset, int limit)
        {
            var total = _repository.GetTotalByStores(storesId, tenantsId);
            return (total, _repository.GetByStoresWithPagination(storesId, tenantsId, offset, limit));
        }

        public AccountBalance? GetBalance(Guid accountId)
        {
            return _repository.GetBalance(accountId);
        }
        
        public Account? GetByCode(string code)
        {
            return _repository.GetByCode(code);
        }

        public void addJournal(AccountJournal accountJournal)
        {
            if (accountJournal.JournalTypeId == Models.Enumerations.JournalType.NegativeAdjustment
                || accountJournal.JournalTypeId == Models.Enumerations.JournalType.Fee)
                accountJournal.Amount *= -1; 
            _repository.UpdateBalance(accountJournal.AccountId, accountJournal.Amount);
            _repository.addJournal(accountJournal);
        }

        public List<AccountJournal> GetJournalsByDate(Guid accountId, DateTime dateFrom, DateTime dateTo)
        {
            return _repository.GetJournalsByDate(accountId, dateFrom, dateTo);
        }

        public void UpdateBalance(Guid accountId, decimal amount)
        {
            _repository.UpdateBalance(accountId, amount);
        }

        public void Dispose()
        {
            _repository.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
