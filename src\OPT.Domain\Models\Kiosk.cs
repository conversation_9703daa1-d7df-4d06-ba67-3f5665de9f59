﻿namespace OPT.Domain.Models
{
    public class Kiosk
    {
        public Guid ID { get; set; }
        public string? Name { get; set; }
        public string? ValidateHash { get; set; }
        public string? DeviceID { get; set; }
        public bool Active { get; set; }
        public Guid StoreID { get; set; }
        public Store Store { get; set; }
        public List<KioskConfig> Configs { get; set; }
        public KioskInfo Info { get; set; }
    }
}
