﻿using OPT.Domain.Models;

namespace OPT.Domain.Interfaces.Repositories
{
    public interface IAccountCardRepository : IRepository<AccountCard>
    {
        List<AccountCard> GetByAccount(Guid accountId);

        List<AccountCard> GetByStores(List<Guid>? storeIds, List<Guid>? tenantsId);

        int GetTotalByStores(List<Guid>? storeIds, List<Guid>? tenantsId);

        List<AccountCard> GetByStoresWithPagination(List<Guid>? storeIds, List<Guid>? tenantsId, int offset, int limit);

        AccountCard? GetByPANOrBarCode(string PANOrBarcode);

        AccountCard? GetByPAN(string PAN);

        AccountCard? GetByPAN19(string PAN);

        AccountCard? GetByIdExtended(params object[] ids);

        AccountCard? GetByDiscountId(Guid discountId);

        List<AccountCardType> GetAccountCardTypes();

        bool Valid(Guid id, Guid storeId);
    }
}
