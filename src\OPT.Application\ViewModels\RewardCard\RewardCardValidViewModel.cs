﻿namespace OPT.Application.ViewModels.RewardCard
{
    public class RewardCardValidViewModel
    {
        public required string Track2 { get; set; }

        public string GetPANOrBarCode()
        {
            if (Track2 == null)
                return "";

            if (Track2.Length != 0 && (Track2.Length <= 16 || Track2.Length == 19))
            {
                return Track2;
            }

            int len = Track2.IndexOf('=');
            if (len < 0)
            {
                len = Track2.IndexOf('D');
                if (len < 0 && Track2.Length == 38)
                    len = 19;
                else if (len < 0)
                    return "";
            }

            if ((len < 13) || (len > 19))
                return "";
            return Track2.Substring(0, len);
        }
    }
}
