﻿using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;

namespace OPT.Domain.Services
{
    public class TransactionAuditService : ITransactionAuditService
    {
        private readonly ITransactionAuditRepository _repository;

        public TransactionAuditService(ITransactionAuditRepository repository)
        {
            _repository = repository;
        }

        public void Add(TransactionAudit model)
        {
            _repository.Add(model);
        }

        public List<List<TransactionAudit>> GetByFilter(Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId)
        {
            return _repository.GetByFilter(kioskId, dateFrom, dateTo, storesId);
        }
        
        public (int, List<List<TransactionAudit>>) GetByFilterWithPagination(Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId, int offset, int limit)
        {
            return _repository.GetByFilterWithPagination(kioskId, dateFrom, dateTo, storesId, offset, limit);
        }

        public void Dispose()
        {
            _repository.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
