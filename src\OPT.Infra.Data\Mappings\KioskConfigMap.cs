﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class KioskConfigMap : Mapping<KioskConfig>
    {
        public override void Map(EntityTypeBuilder<KioskConfig> builder)
        {
            builder.ToTable("KioskConfigs");

            builder.HasKey(x => x.ID);

            base.Map(builder);
        }
    }
}
