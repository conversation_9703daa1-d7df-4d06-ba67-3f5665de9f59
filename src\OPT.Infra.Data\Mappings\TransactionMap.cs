﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    
    public class TransactionMap : Mapping<Transaction>
    {
        public override void Map(EntityTypeBuilder<Transaction> builder)
        {
            builder.ToTable("Transactions");

            builder.HasKey(x => x.Id);
            builder.HasOne(x => x.Store).WithMany().HasForeignKey(x=>x.StoreId);
            builder.HasOne(x => x.Kiosk).WithMany().HasForeign<PERSON>ey(x => x.KioskId);
            builder.HasOne(x => x.Account).WithMany().HasForeignKey(x => x.AccountId);
            builder.HasOne(x => x.FuelTransaction).WithMany().HasForeignKey(x => x.FuelTransactionId);
            builder.HasMany(x => x.RewardCardTransactions).WithOne().HasForeignKey(x => x.TransactionID).OnDelete(DeleteBehavior.NoAction);
            builder.HasOne(x => x.AccountCard).WithMany().HasForeignKey(x => x.AccountCardId).OnDelete(DeleteBehavior.NoAction);
            base.Map(builder);
        }
    }
}
