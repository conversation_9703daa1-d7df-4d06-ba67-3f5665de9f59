﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class KioskInfoMap : Mapping<KioskInfo>
    {
        public override void Map(EntityTypeBuilder<KioskInfo> builder)
        {
            builder.ToTable("KioskInfos");

            builder.HasKey(x => x.ID);

            base.Map(builder);
        }
    }
}
