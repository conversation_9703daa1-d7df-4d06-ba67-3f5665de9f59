﻿using OPT.Domain.Models.Enumerations;

namespace OPT.Application.ViewModels.TransactionAudit
{
    public class TransactionAuditAddViewModel
    {
        public DateTime TransactionCreatedDate { get; set; }
        public long KioskTransactionNumber { get; set; }
        public string? CardSignature { get; set; }
        public string? CardExpiryDate { get; set; }
        public required string Processor { get; set; }
        public TransactionStatus? TransactionStatus { get; set; }
        public decimal? TransactionAmount { get; set; }
        public decimal? TransactionFinalAmount { get; set; }
        public string? TransactionResponseCode { get; set; }
        public string? TransactionResponse { get; set; }
        public string? VehicleRegistration { get; set; }
        public string? VehicleOdometer { get; set; }
        public string? PumpNumber { get; set; }
        public string? FuelGrade { get; set; }
        public string? FuelGradeName { get; set; }
        public string? AuthorizationID { get; set; }
        public string? TID { get; set; }
        public int? STAN { get; set; }
    }
}
