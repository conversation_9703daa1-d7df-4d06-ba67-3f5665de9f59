﻿using OPT.Domain.Exceptions;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;

namespace OPT.Domain.Services
{
    public class VehicleRegistrationService : IVehicleRegistrationService
    {
        private readonly IVehicleRegistrationRepository _repository;

        public VehicleRegistrationService(IVehicleRegistrationRepository repository)
        {
            _repository = repository;
        }

        public void Add(VehicleRegistration vehicleRegistration, Guid accountId)
        {
            vehicleRegistration.Active = true;
            _repository.Add(vehicleRegistration);
            _repository.AddAccount(new AccountVehicleRegistration
            {
                AccountId = accountId,
                VehicleRegistrationId = vehicleRegistration.ID
            });

        }

        public void Update(VehicleRegistration vehicleRegistration)
        {
            var update = _repository.GetById(vehicleRegistration.ID);
            if (update != null)
            {
                update.RegistrationNumber = vehicleRegistration.RegistrationNumber;
                update.Active = vehicleRegistration.Active;
                _repository.Update(update);
            }
            else
            {
                throw new BusinessException("Vehicle Registration does not exist.", 404);
            }
        }

        public void Update(VehicleRegistration vehicleRegistration, Guid storeId)
        {
            if (_repository.Valid(vehicleRegistration.ID, storeId))
            {
                Update(vehicleRegistration);
            }
            else
            {
                throw new BusinessException("You are not authorized to update this vehicle!", 401);
            }
        }
        
        public void Delete(Guid id)
        {
            var accounts = _repository.GetAccountByVehicleRegistration(id);
            foreach (var account in accounts) { 
                _repository.RemoveAccount(account);
            }
            _repository.Remove(id);
        }

        public void Delete(Guid id, Guid storeId)
        {
            if(_repository.Valid(id, storeId))
            {
                Delete(id);
            }
            else
            {
                throw new BusinessException("You are not authorized to delete this vehicle!", 401);
            }
        }

        public List<VehicleRegistration> GetByAccount(Guid accountId)
        {
            return _repository.GetByAccount(accountId);
        }
        
        public VehicleRegistration? GetById(Guid id)
        {
            return _repository.GetById(id);
        }
        
        public VehicleRegistration? GetById(Guid id, Guid storeId)
        {
            if (_repository.Valid(id, storeId))
            {
                return _repository.GetById(id);
            }
            else
            {
                throw new BusinessException("You are not authorized to get this vehicle!", 401);
            }
        }

        public VehicleRegistration? GetByRegistrationNumber(string registrationNumber)
        {
            return _repository.GetByRegistrationNumber(registrationNumber);
        }

        public void Dispose()
        {
            _repository.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
