﻿using OPT.Application.ViewModels.Account;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class JournalSpecification : ISpecificationConfiguration<JournalViewModel>
    {

        public JournalSpecification()
        {
        }

        public ISpecification<JournalViewModel> Map(ISpecification<JournalViewModel> builder)
        {
            builder.IsSatisfiedBy(x => x.Amount > 0, "Amount must be greater than zero!", 412);

            return builder;
        }
    }
}
