﻿using OPT.Application.ViewModels.Account;
using OPT.Application.ViewModels.FuelTransaction;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OPT.Application.ViewModels.Transaction
{
    public class TransactionViewModel
    {
        public int TerminalTransactionId { get; set; }
        public DateTime TransactionDateTime { get; set; }
        public int Type { get; set; }
        public int Status { get; set; }
        public decimal AuthorisedAmount { get; set; }
        public decimal FinalAmount { get; set; }
        public decimal GSTAmount { get; set; }
        public string CardSignature { get; set; }
        public string VehicleRegistration { get; set; }
        public string VehicleOdometer { get; set; }
        public string AccountType { get; set; }
        public string ApplicationId { get; set; }
        public int STAN { get; set; }
        public int ResponseCode { get; set; }
        public string HostResponse { get; set; }
        public int AuthorizationID { get; set; }
        public int PanSeqNo { get; set; }
        public string CVM { get; set; }
        public string Processor { get; set; }
        public int TerminalId { get; set; }
        public Guid StoreId { get; set; }
        public Guid KioskId { get; set; }
        public Guid AccountId { get; set; }
        public string UserId { get; set; }
        public FuelTransactionAddViewModel FuelTransaction { get;set;}
        
    }
}
