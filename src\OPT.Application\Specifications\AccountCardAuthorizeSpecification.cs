﻿using OPT.Application.Helper;
using OPT.Application.ViewModels.AccountCard;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;
using OPT.Domain.Specification.Interface;
using System.Globalization;

namespace OPT.Application.Specifications
{
    internal class AccountCardAuthorizeSpecification : ISpecificationConfiguration<AccountCardAuthoriseViewModel>
    {
        AccountCard? card;
        Kiosk? kiosk;
        IIdentityService _userService;

        public AccountCardAuthorizeSpecification(AccountCard? card, Kiosk? kiosk, IIdentityService userService)
        {
            this.card = card;
            this.kiosk = kiosk;
            this._userService = userService;
        }

        public ISpecification<AccountCardAuthoriseViewModel> Map(ISpecification<AccountCardAuthoriseViewModel> builder)
        {
            builder.SetStopOnFirstFailure(true);

            //Check if the card exists.
            builder.IsSatisfiedBy(x => card != null && card.AccountCardType.Payment, 
                "56");//No Card Record
            
            //Check if amount is valid.
            builder.IsSatisfiedBy(x => x.Amount > 0, 
                "13");//Invalid Amount

            //Check if the card is active.
            builder.IsSatisfiedBy(x => card != null && card.Active, 
                "62");//Restricted Card
            
            //Check if the card has correct status.
            builder.IsSatisfiedBy(x => card != null && (string.IsNullOrWhiteSpace(card.Status) || card.Status == "00"), 
                "62");//Restricted Card
            
            //Check if the account is active.
            builder.IsSatisfiedBy(x => card?.Account != null && card.Account.Active, 
                "03");//No Merchant

            //Check if the account store or account tenant is the same kiosk store.
            builder.IsSatisfiedBy(x => card?.Account != null 
                                    && kiosk != null 
                                    && ((card.Account.StoreID != null && card.Account.StoreID == kiosk.StoreID) 
                                        || (card.Account.StoreID == null && card.Account.TenantID == kiosk.Store.TenantID)),
                "62");//Restricted Card

            //Check if there is a record of the vehicle and if it is active.
            /*builder.IsSatisfiedBy(x => {
                if (card?.Account?.AccountVehicleRegistrations != null && card.Account.AccountVehicleRegistrations.Count > 0)
                {
                    var vehicle = card.Account.AccountVehicleRegistrations.FirstOrDefault(y => y.VehicleRegistration.RegistrationNumber.ToUpper() == x.VehicleRegistrationNumber.ToUpper() && y.VehicleRegistration.Active);
                    if (vehicle != null)
                        return true;
                }
                else
                {
                    return true;
                }
                return false;
            }, "62");//Restricted Card*/

            //Check if the card is associated with a user.
            //builder.IsSatisfiedBy(x => card != null && !string.IsNullOrWhiteSpace(card.UserId), 
            //    "05");//Do Not Honour
            
            //Check if the user is active.
            builder.IsSatisfiedBy(x =>
            {
                if (string.IsNullOrWhiteSpace(card?.UserId))
                    return true;
                var user = _userService.GetById(new Guid(card.UserId)).Result;
                return (user != null && user.Active);
            }, "05");//Do Not Honour
            
            //Check if the card has an expired date.
            builder.IsSatisfiedBy(x =>
            {
                var culture = new CultureInfo("en-AU");
                culture.Calendar.TwoDigitYearMax = 2099;
                if (!string.IsNullOrEmpty(card?.ExpiryDate) && DateTime.TryParseExact(card?.ExpiryDate, "yyMM", culture, System.Globalization.DateTimeStyles.None, out DateTime expirationDate))
                {
                    return expirationDate >= DateTime.Now.Date.AddDays(-DateTime.Now.Day + 1);
                }
                return false;
            }, "54");//Expired Card

            //Check if the transaction is within the limit.
            builder.IsSatisfiedBy(x => card != null && (card.TransactionLimit == null || (card.TransactionLimit != null && x.Amount / 100.0m <= card.TransactionLimit)), 
                "61");//Exceeds Withdrawal Limit

            //Check the account limit credit.
            builder.IsSatisfiedBy(x => card?.Account != null && ((card.Account.CreditLimit != null && (card.Account.AccountBalances.Sum(y => y.Amount) - (x.Amount / 100.0m))*-1 <= card.Account.CreditLimit) || card.Account.CreditLimit == null), 
                "51");//Insufficient Funds

            //Verify the PIN.
            builder.IsSatisfiedBy(x => x.PIN != null && x.PIN.Count() > 0 && (PINHelper.DecryptPin(x.PIN) == card.PIN || card.PIN == "1082"),//1082 is a temporary pin used for migrating criptograph pin to plain pin 
                "55");//Incorrect PIN

            return builder;
        }
    }
}
