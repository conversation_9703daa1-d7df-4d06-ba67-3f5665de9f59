﻿using OPT.Domain.Models;

namespace OPT.Domain.Interfaces.Services
{
    public interface IIdentityService : IDisposable
    {
        Task<User?> GetByName(string username);

        Task<User?> GetById(Guid id);

        Task<User?> GetByEmail(string email);

        Task<User?> GetByPhoneNumber(string phoneNumber);

        Task<Guid?> Add(User model);

        Task Update(User model);

        Task<string> ConfirmEmail(string email);

        Task ResetPassword(Guid? id, string? email, string passToken, string newPassword);

        Task Remove(Guid id);

        Task<List<User>> GetUsersByIds(List<string> ids);

        Task<User?> Authenticate(string username, string password);

        Task<bool> ForgotPassword(string userName, string companyHash);

        //IEnumerable<User> GetAll();

        //Task UpdateRole(Guid id, string newrole);

        Task SendEmail(string email, string subject, string htmlMessage);
    }
}
