﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.Specifications;
using OPT.Application.ViewModels.ApiKey;
using OPT.Domain.Exceptions;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Interfaces.UoW;
using OPT.Domain.Models;
using OPT.Domain.Specification;

namespace OPT.Application
{
    public class ApiKeyAppService : ApplicationService, IApiKeyAppService
    {
        private readonly IApiKeyService _service;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;

        public ApiKeyAppService(IUnitOfWork unitOfWork,
                                IMapper mapper,
                                IConfiguration configuration,
                                IApiKeyService service) : base(unitOfWork)
        {
            _service = service;
            _mapper = mapper;
            _configuration = configuration;
        }

        public void Add(ApiKeyAddViewModel viewModel)
        {
            if (viewModel.IsValid<ApiKeyAddViewModel, ApiKeyAddSpecification>())
            {
                _service.Add(_mapper.Map<ApiKey>(viewModel), CryptographyHelper.Encrypt(RandomHelper.RandomString(6)));
                Commit();
            }
        }

        public string UpdateAccessToken(Guid id)
        {
            string accessToken = CryptographyHelper.Encrypt(RandomHelper.RandomString(6));
            _service.UpdateAccessToken(id, accessToken);
            Commit();
            return accessToken;
        }
        
        public void UpdateActive(Guid id, bool ative)
        {
            _service.UpdateActive(id, ative);
            Commit();
        }

        public List<ApiKeyViewModel> GetByStores(List<Guid> storesId)
        {
            return _mapper.Map<List<ApiKeyViewModel>>(_service.GetByStores(storesId));
        }

        public void Remove(Guid id)
        {
            _service.Remove(id);
            Commit();
        }

        public string Authenticate(ApiKeyAuthenticateViewModel model)
        {
            var apikey = _service.GetByAccessToken(model.AccessToken);
            if (apikey != null)
            {
                if (!apikey.Active)
                {
                    throw new BusinessException("Access Token is invalid.");
                }
                return TokenHelper.GetToken(apikey, _configuration);
            }
            throw new BusinessException("Access Token not found.");
        }

        public void Dispose()
        {
            _service.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
