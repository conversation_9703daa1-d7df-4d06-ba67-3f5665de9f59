﻿using OPT.Application.ViewModels.AccountCard;
using OPT.Application.ViewModels.FuelTransaction;
using OPT.Application.ViewModels.RewardCard;
using OPT.Application.ViewModels.Store;

namespace OPT.Application.ViewModels.Transaction
{
    public class UnitedTransactionFileViewModel
    {
        public int TerminalTransactionId { get; set; }
        public DateTime TransactionDateTime { get; set; }
        public string? CardSignature { get; set; }
        public decimal FinalAmount { get; set; }
        public int? STAN { get; set; }
        public string VehicleOdometer { get; set; }
        public StoreViewModel Store { get; set; }
        public AccountCardInternalViewModel AccountCard { get; set; }
        public FuelTransactionAccountViewModel FuelTransaction { get;set;}
        public List<RewardCardDiscountTransactionViewModel> RewardCardTransactions { get; set; }

        public override string ToString()
        {
            char type;
            string cardNumber;
            if (AccountCard != null) {
                type = AccountCard.Type;
                cardNumber = AccountCard.PAN;
            }
            else if(RewardCardTransactions != null && RewardCardTransactions.Any()) {
                type = RewardCardTransactions.First().Type;
                cardNumber = RewardCardTransactions.First().PAN;
            }
            else
            {
                type = '0';
                cardNumber = CardSignature ?? "";
            }
            if (type == 'R' || type == 'S')
                cardNumber = cardNumber.Substring(6);
            else if (type == '0')
                cardNumber = cardNumber.Replace("*", "");
            else
                cardNumber = cardNumber.Substring(8);

            var ret = $"1{type}{cardNumber.PadRight(15)}{TransactionDateTime:ddMMyyHHmm}{FixOdometerValue(VehicleOdometer)}";

            for (int i = 0; i < UnitedAccountCardFileViewModel.ProductOrder.Length - 2; i++)
            {
                if (FuelTransaction.FuelGradeId == UnitedAccountCardFileViewModel.ProductOrder[i])
                    ret += $"{(FuelTransaction.FuelVolume / 100.0):0000.00}{(FinalAmount):0000.00}";
                else
                    ret += "0000.000000.00";
            }

            int hose = 0;
            string transactionId = $"{TerminalTransactionId:********}";
            transactionId = transactionId.Substring(transactionId.Length - 5);

            ret += $"{(FinalAmount):0000000.00}{Store.Code3Digits?.PadLeft(3,'0') ?? "000"}{Store.Code3Digits?.PadLeft(3,'0') ?? "000"}{FuelTransaction.PumpNumber:00}{hose}{transactionId}{(STAN ?? 0):*************}";

            return ret;
        }

        private string FixOdometerValue(string input)
        {
            if (input == null)
                return "000000";
            if (input.Length >= 6)
                return input.Substring(input.Length - 6);
            else
                return input.PadLeft(6, '0');
        }

    }
}
