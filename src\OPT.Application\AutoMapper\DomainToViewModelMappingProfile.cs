﻿using AutoMapper;
using OPT.Application.Helper;
using OPT.Application.ViewModels.Account;
using OPT.Application.ViewModels.AccountCard;
using OPT.Application.ViewModels.ApiKey;
using OPT.Application.ViewModels.FuelTransaction;
using OPT.Application.ViewModels.Kiosk;
using OPT.Application.ViewModels.RewardCard;
using OPT.Application.ViewModels.Store;
using OPT.Application.ViewModels.Transaction;
using OPT.Application.ViewModels.TransactionAudit;
using OPT.Application.ViewModels.User;
using OPT.Application.ViewModels.VehicleRegistration;
using OPT.Domain.Models;
using OPT.Domain.Models.Enumerations;

namespace OPT.Application.AutoMapper
{
    public class DomainToViewModelMappingProfile : Profile
    {
        public DomainToViewModelMappingProfile()
        {
            CreateMap<User, UserViewModel>();
            CreateMap<User, UserAccountViewModel>();
            CreateMap<User, UserCardAccountViewModel>();
            CreateMap<User, UserKioskViewModel>()
                .ForMember(x => x.UserName, x => x.MapFrom(y => y.Active ? y.UserName : null))
                .ForMember(x => x.FirstName, x => x.MapFrom(y => y.Active ? y.FirstName : null))
                .ForMember(x => x.LastName, x => x.MapFrom(y => y.Active ? y.LastName : null));

            CreateMap<Account, AccountInternalViewModel>();
            CreateMap<Account, AccountViewModel>();
            CreateMap<Account, AccountUserViewModel>();

            CreateMap<Store, StoreViewModel>();
            CreateMap<Store, StoreAddressViewModel>();

            CreateMap<Kiosk, KioskViewModel>()
                .ForMember(x => x.Validated, x => x.MapFrom(y => !string.IsNullOrWhiteSpace(y.DeviceID)));
            CreateMap<Kiosk, KioskValidViewModel>()
                .ForMember(x => x.ValidCode, x => x.MapFrom(y => !string.IsNullOrWhiteSpace(y.ValidateHash) ? CryptographyHelper.Decrypt(y.ValidateHash) : string.Empty));
            CreateMap<Kiosk, KioskValidResponseViewModel>();
            CreateMap<KioskConfig, KioskConfigViewModel>();
            CreateMap<KioskInfo, KioskInfoViewModel>();
            CreateMap<Kiosk, KioskNameViewModel>();

            CreateMap<Transaction, TransactionUserViewModel>()
                .ForMember(x => x.StoreName, x => x.MapFrom(y => y.Store.Name))
                .ForMember(x => x.KioskName, x => x.MapFrom(y => y.Kiosk.Name));
            CreateMap<Transaction, TransactionAccountViewModel>()
                .ForMember(x => x.StoreName, x => x.MapFrom(y => y.Store.Name))
                .ForMember(x => x.StoreCode, x => x.MapFrom(y => y.Store.Code4Digits))
                .ForMember(x => x.KioskName, x => x.MapFrom(y => y.Kiosk.Name))
                .ForMember(x => x.AccountName, x => x.MapFrom(y => y.Account != null ? y.Account.Name : ""));
            CreateMap<FuelTransaction, FuelTransactionViewModel>();
            CreateMap<FuelTransaction, FuelTransactionAccountViewModel>();
            CreateMap<FuelTransaction, FuelTransactionDisplayViewModel>()
                .ForMember(x=>x.FuelVolume, x=>x.MapFrom(src=> ((decimal)src.FuelVolume/100).ToString("0.00")))
                .ForMember(x => x.FuelPrice, x => x.MapFrom(src => ((decimal)src.FuelPrice / 10).ToString("#.#")));
            CreateMap<Transaction, TransactionReceiptViewModel>()
                .ForMember(x=>x.TransactionDateTime,opt=>opt.MapFrom(src=>src.TransactionDateTime.ToString("yyyy-MM-dd hh:mm:ss tt")))
                .ForMember(x => x.Type, x => x.MapFrom(y => y.Type == 0 ? "PURCHASE" : "REFUND"))
                .ForMember(x => x.ResponseCode, x => x.MapFrom(y => y.ResponseCode == "00" ? "APPROVED" : "DECLINED"))
                .ForMember(x => x.Currency, x => x.MapFrom(y => CurrencyHelper.LookupByNumber(y.Currency).Code));
            CreateMap<Transaction, TransactionViewModel>();
            CreateMap<Transaction, UnitedTransactionFileViewModel>();

            CreateMap<ParameterConfig, ParameterConfigViewModel>();

            CreateMap<AccountCard, AccountCardInternalViewModel>();
            CreateMap<AccountCard, AccountCardViewModel>();
            CreateMap<AccountCardRestriction, AccountCardRestrictionViewModel>();

            CreateMap<VehicleRegistration, VehicleRegistrationViewModel>();
            CreateMap<AccountBalance, AccountBalanceViewModel>();
            CreateMap<AccountJournal, AccountStatementViewModel>()
                .ForMember(x => x.Date, opt => opt.MapFrom(src => src.JournalDate))
                .ForMember(x => x.Provider, opt => opt.MapFrom(src => src.JournalTypeId.GetDescription()));
            
            CreateMap<AccountCardTransaction, AccountStatementViewModel>()
                .ForMember(x => x.Date, opt => opt.MapFrom(src => src.FinalizedDate))
                .ForMember(x => x.Amount, opt => opt.MapFrom(src => src.AmountCaptured / -100.0m))
                .ForMember(x => x.Provider, opt => opt.MapFrom(src => "Fuel supply"));
            CreateMap<TransactionAudit, TransactionAuditViewModel>()
                .ForMember(x => x.TransactionStatus, opt => opt.MapFrom(src => src.TransactionStatus.GetDescription()));
            CreateMap<Kiosk, KioskTransactionAuditViewModel>()
                .ForMember(x => x.StoreName, opt => opt.MapFrom(src => src.Store.Name));

            CreateMap<AccountCard, RewardCardViewModel>();
            CreateMap<AccountCard, RewardCardItemViewModel>();
            CreateMap<AccountCard, AccountCardTransactionAccountViewModel>();
            CreateMap<RewardCardTransaction, RewardCardTransactionInternalViewModel>();
            CreateMap<RewardCardTransaction, RewardCardTransactionViewModel>()
                .ForMember(x => x.FuelGrade, x => x.MapFrom(y => y.RewardCardDiscount.FuelGrade));
            CreateMap<RewardCardTransaction, RewardCardDiscountTransactionViewModel>()
                .ForMember(x => x.PAN, opt => opt.MapFrom(src => src.RewardCardDiscount.AccountCard.PAN))
                .ForMember(x => x.Type, opt => opt.MapFrom(src => src.RewardCardDiscount.AccountCard.Type));
            CreateMap<RewardCardDiscount, RewardCardDiscountViewModel>();
            CreateMap<AccountCardType, AccountCardTypeViewModel>();

            CreateMap<ApiKey, ApiKeyViewModel>()
                .ForMember(x => x.StoreName, x => x.MapFrom(y => y.Store.Name));

        }
    }
}