﻿using OPT.Domain.Models;
using OPT.Domain.Specification.Interface;
using System.Globalization;

namespace OPT.Application.Specifications
{
    internal class RewardCardValidSpecification : ISpecificationConfiguration<AccountCard>
    {
        Kiosk? kiosk;

        public RewardCardValidSpecification(Kiosk? kiosk)
        {
            this.kiosk = kiosk;
        }

        public ISpecification<AccountCard> Map(ISpecification<AccountCard> builder)
        {
            builder.SetStopOnFirstFailure(true);

            //Check if the card is active.
            builder.IsSatisfiedBy(x => x.Active,
                "Reward card not active.");
            
            //Check if the card is valid.
            builder.IsSatisfiedBy(x => x.AccountCardType.Reward,
                "Reward card not valid.");

            //Validating Status - Quick Fuel Rules.
            builder.IsSatisfiedBy(x => x.Status != "01",
                "Card stopped.");
            
            //Validating Status - Quick Fuel Rules.
            builder.IsSatisfiedBy(x => x.Status != "02",
                "Account stopped.");
            
            //Validating Status - Quick Fuel Rules.
            builder.IsSatisfiedBy(x => x.Status != "03",
                "Error 3.");
            
            //Validating Status - Quick Fuel Rules.
            builder.IsSatisfiedBy(x => x.Status != "04",
                "Error 4.");
            
            //Validating Status - Quick Fuel Rules.
            builder.IsSatisfiedBy(x => x.Status != "05",
                "Card over monthly limit.");
            
            //Validating Status - Quick Fuel Rules.
            builder.IsSatisfiedBy(x => x.Status != "06",
                "Card already used today.");
            
            //Validating Status - Quick Fuel Rules.
            builder.IsSatisfiedBy(x => x.Status != "07",
                "Card over day value.");
            
            //Validating Status - Quick Fuel Rules.
            builder.IsSatisfiedBy(x => x.Status != "08",
                "Card over day liters.");
            
            //Validating Status - Quick Fuel Rules.
            builder.IsSatisfiedBy(x => x.Status != "09",
                "Before contract starts.");
            
            //Validating Status - Quick Fuel Rules.
            builder.IsSatisfiedBy(x => x.Status != "10",
                "After contract ends.");
            
            //Validating Status - Quick Fuel Rules.
            builder.IsSatisfiedBy(x => x.Status != "11",
                "Last payment not yet cleared.");
            
            //Check if the card has correct status.
            builder.IsSatisfiedBy(x => string.IsNullOrWhiteSpace(x.Status) || x.Status == "00",
                "Reward card cannot be used.");

            //Check if the store is the same kiosk store.
            builder.IsSatisfiedBy(x => kiosk != null && (x.StoreID == null || (x.StoreID != null && x.StoreID == kiosk.StoreID)),
                "Reward card does not belong to this store.");

            //Check if the store is the same kiosk store.
            builder.IsSatisfiedBy(x => kiosk != null 
                                        && (x.AccountId == null 
                                            || (x.AccountId != null 
                                                && ((x.Account.StoreID != null 
                                                    && x.Account.StoreID == kiosk.StoreID) 
                                                    || (x.Account.StoreID == null 
                                                    && x.Account.TenantID == kiosk.Store.TenantID)))),
                "Reward card does not belong to this store.");

            //Check if the card has an expired date.
            builder.IsSatisfiedBy(x =>
            {
                var culture = new CultureInfo("en-AU");
                culture.Calendar.TwoDigitYearMax = 2099;
                if (!string.IsNullOrEmpty(x.ExpiryDate) && DateTime.TryParseExact(x.ExpiryDate, "yyMM", culture, DateTimeStyles.None, out DateTime expirationDate))
                {
                    return expirationDate >= DateTime.Now.Date.AddDays(-DateTime.Now.Day + 1);
                }
                return false;
            }, "Reward card is expired");

            return builder;
        }
    }
}
