﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class AccountCardTypeMap : Mapping<AccountCardType>
    {
        public override void Map(EntityTypeBuilder<AccountCardType> builder)
        {
            builder.ToTable("AccountCardTypes");

            builder.HasKey(x => x.ID);

            base.Map(builder);
        }
    }
}
