﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels.Transaction;

namespace OPT.Scheduler.Workers
{
    public class UnitedExporterWorker : BackgroundService
    {
        private readonly ILogger<UnitedExporterWorker> _logger;
        private readonly IConfiguration _configuration;
        private readonly ITransactionAppService _transactionAppService;

        public UnitedExporterWorker(ILogger<UnitedExporterWorker> logger, 
                                        IConfiguration configuration,
                                        ITransactionAppService transactionAppService)
        {
            _logger = logger;
            _configuration = configuration;
            _transactionAppService = transactionAppService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("UnitedExporterWorker running at: {time}", DateTimeOffset.Now);
                try
                {
                    Proccess();
                }
                catch (Exception ex)
                {
                    _logger.LogError("UnitedExporterWorker running at: {time}\nException: {exception}", DateTimeOffset.Now, ex);
                }
                await Task.Delay(int.Parse(_configuration["UnitedSettings:ExportPeriod"]), stoppingToken);
            }
        }

        private void Proccess()
        {
            var transactions = _transactionAppService.GetUnitedToExport(new Guid(_configuration["UnitedSettings:TenantID"]));

            if (transactions != null && transactions.Count > 0)
            {

                using (var ftpClient = new FTPClientHelper(_configuration["FTPSettings:Host"],
                                                            int.Parse(_configuration["FTPSettings:Port"]),
                                                            _configuration["FTPSettings:User"],
                                                            _configuration["FTPSettings:Password"],
                                                            _configuration["FTPSettings:InFolder"],
                                                            _configuration["FTPSettings:OutFolder"]))
                {

                    var fileName = "unitedtxns." + DateTime.Now.ToString("yyyyMMddHHmm");
                    ftpClient.UploadText(GetTransactionFileFormat(transactions), fileName);
                    _logger.LogInformation("New exported file created: {0}", fileName);
                }
            }
        }

        private string GetTransactionFileFormat(List<UnitedTransactionFileViewModel> transactions)
        {
            var ret = $"H{DateTime.Now:ddMMyyyyHHmm}{DateTime.Now.Ticks:000000000000}\n";
            ret += string.Join("\n", transactions);
            ret += $"\nT{transactions.Count}";

            return ret;
        }
    }
}
