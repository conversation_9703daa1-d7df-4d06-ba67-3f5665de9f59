﻿using OPT.Domain.Models;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class RewardCardPublicValidSpecification : ISpecificationConfiguration<AccountCard>
    {

        private readonly ApiKey apiKey;

        public RewardCardPublicValidSpecification(ApiKey apiKey)
        {
            this.apiKey = apiKey;
        }

        public ISpecification<AccountCard> Map(ISpecification<AccountCard> builder)
        {
            builder.SetStopOnFirstFailure(true);

            builder.IsSatisfiedBy(x => x != null, "RewardCard does not exist!", 404);
            
            builder.IsSatisfiedBy(x => x.StoreID.Equals(apiKey.StoreID), "You are not authorized to get this RewardCard!", 401);

            return builder;
        }
    }
}
