﻿using System.Linq.Expressions;

namespace OPT.Domain.Interfaces.Repositories
{
    public interface IRepository<TEntity> : IDisposable where TEntity : class
    {
        void Add(TEntity entity);
        void Add(IEnumerable<TEntity> entity);
        TEntity? GetById(params object[] ids);
        IEnumerable<TEntity> GetAll();
        void Update(TEntity entity);
        void Update(IEnumerable<TEntity> entities);
        void Update(TEntity current, TEntity updated);
        void Remove(params object[] ids);
        void Remove(TEntity entity);
        void Remove(IEnumerable<TEntity> entities);
        IEnumerable<TEntity> Find(Expression<Func<TEntity, bool>> predicate);

        int SaveChanges();
    }
}
