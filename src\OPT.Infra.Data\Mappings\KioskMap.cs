﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class KioskMap : Mapping<Kiosk>
    {
        public override void Map(EntityTypeBuilder<Kiosk> builder)
        {
            builder.ToTable("Kiosks");

            builder.HasKey(x => x.ID);

            builder.HasOne(x => x.Store).WithMany().HasForeignKey(x => x.StoreID).OnDelete(DeleteBehavior.NoAction);

            builder.HasMany(x => x.Configs).WithOne().HasForeignKey(x => x.KioskID).OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(x => x.Info).WithOne().HasForeignKey<KioskInfo>(x => x.KioskID).OnDelete(DeleteBehavior.Cascade);

            base.Map(builder);
        }
    }
}
