﻿using Microsoft.EntityFrameworkCore;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Models;
using OPT.Infra.Data.Context;

namespace OPT.Infra.Data.Repositories
{
    public class KioskRepository : Repository<Kiosk>, IKioskRepository
    {
        public KioskRepository(OPTContext context) : base(context)
        {
        }

        public new Kiosk? GetById(params object[] ids)
        {
            return _dbset.Include(x => x.Configs).Include(x => x.Store).Include(x => x.Info).AsNoTracking().FirstOrDefault(x => ids.Contains(x.ID));
        }
        
        public Kiosk? GetByDeviceId(string deviceId)
        {
            return _dbset.Include(x => x.Store).AsNoTracking().FirstOrDefault(x => x.DeviceID == deviceId);
        }
        
        public Kiosk? GetByHash(string validHash)
        {
            return _dbset.AsNoTracking().FirstOrDefault(x => x.ValidateHash == validHash);
        }

        public new void Add(Kiosk entity)
        {
            if (entity.Store != null)
            {
                var store = _context.Set<Store>().AsNoTracking().Where(x => x.ID == entity.Store.ID).FirstOrDefault();
                if (store == null)
                    _context.Entry(entity.Store).State = EntityState.Added;
                else
                    _context.Entry(entity.Store).State = EntityState.Modified;
            }
            _dbset.Add(entity);
        }
        
        public new void Update(Kiosk entity)
        {
            if (entity.Store != null)
            {
                var store = _context.Set<Store>().AsNoTracking().Where(x => x.ID == entity.Store.ID).FirstOrDefault();
                if (store == null)
                    _context.Entry(entity.Store).State = EntityState.Added;
                else
                    _context.Entry(entity.Store).State = EntityState.Modified;
            }
            _dbset.Update(entity);
        }

        public List<Kiosk> GetByStores(List<Guid> storesId)
        {
            return _dbset.Where(x => storesId.Contains(x.StoreID)).Include(x => x.Store).Include(x => x.Info).AsNoTracking().ToList();
        }

        public void AddInfo(KioskInfo info)
        {
            var dbset = _context.Set<KioskInfo>();
            var obj = dbset.FirstOrDefault(x => x.KioskID.Equals(info.KioskID));
            if(obj != null)
            {
                info.ID = obj.ID;
                _context.Entry(obj).CurrentValues.SetValues(info);
                _context.Entry(obj).Property(x => x.ID).IsModified = false;
                _context.Entry(obj).State = EntityState.Modified;
            }
            else
            {
                dbset.Add(info);
            }
        }

        public new void Update(Kiosk current, Kiosk updated)
        {
            foreach (var item in current.Configs)
            {
                _context.Entry(item).State = EntityState.Deleted;
            }
            foreach (var item in updated.Configs)
            {
                _context.Entry(item).State = EntityState.Added;
            }
            if (updated.Store != null)
            {
                var store = _context.Set<Store>().AsNoTracking().Where(x => x.ID == updated.Store.ID).FirstOrDefault();
                if (store == null)
                    _context.Entry(updated.Store).State = EntityState.Added;
                else
                    _context.Entry(updated.Store).State = EntityState.Modified;
            }
            _context.Entry(current).CurrentValues.SetValues(updated);
            _context.Entry(current).State = EntityState.Modified;
        }
    }
}
