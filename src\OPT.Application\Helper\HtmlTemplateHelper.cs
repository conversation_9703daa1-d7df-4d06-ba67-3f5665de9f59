﻿using OPT.Application.ViewModels.Transaction;

namespace OPT.Application.Helper
{
    internal class HtmlTemplateHelper
    {
        public static string GetWelcomeEmailTemplate(string email, string companyCode)
        {
            var html = File.ReadAllText("wwwroot\\html\\WelcomeEmailTemplate.html");
            return string.Format(html, email, companyCode);
        }
        
        public static string GetForgotPasswordTemplate(string companyCode)
        {
            var html = File.ReadAllText("wwwroot\\html\\ForgotPasswordTemplate.html");
            return string.Format(html, companyCode);
        }

        public static string GetTransactionReceiptTemplate(TransactionReceiptViewModel receiptData)
        {
            /*
            00 receiptData.Store.Name
            01 receiptData.Store.Address
            02 receiptData.Store.Suburb
            03 receiptData.Store.State
            04 receiptData.Store.PostCode
            05 receiptData.Store.ABN
            06 receiptData.Kiosk.Name
            07 receiptData.TerminalTransactionId
            08 receiptData.TransactionDateTime
            09 receiptData.FuelTransaction.FuelGradeName
            10 receiptData.FuelTransaction.FuelVolume
            11 receiptData.FinalAmount
            12 receiptData.FuelTransaction.PumpNo
            13 receiptData.FuelTransaction.FuelPrice
            14 receiptData.FinalAmount
            15 receiptData.GSTAmount
            16 receiptData.ApplicationLabel,
            17 receiptData.CardSignature,
            18 receiptData.PanSeqNo,
            19 receiptData.ApplicationId,
            20 receiptData.CardATC,
            21 receiptData.Type,
            22 receiptData.TID,
            23 receiptData.Currency,
            24 receiptData.FuelTransaction.TransactionSeqNumber, 
            25 receiptData.AuthorizationID,
            26 receiptData.ResponseCode
            27 Account info
            28 InvoiceFooter
            
            Account info
            00 receiptData.Account.Name
            01 receiptData.Account.Email
            02 receiptData.VehicleRegistration
            03 receiptData.VehicleOdometer
            04 receiptData.User.UserName
             */

            var accountHtml = string.Empty;
            if (receiptData.Account != null)
            {
                var htmlacc = File.ReadAllText("wwwroot\\html\\TransactionReceiptEmailTemplate.html");
                accountHtml = string.Format(htmlacc,
                    receiptData.Account != null ? receiptData.Account.Name : string.Empty,
                    receiptData.Account != null ? receiptData.Account.Email : string.Empty,
                    receiptData.VehicleRegistration,
                    receiptData.VehicleOdometer,
                    receiptData.User != null ? (receiptData.User.FirstName + " " + receiptData.User.LastName) : string.Empty);
            }

            var html = File.ReadAllText("wwwroot\\html\\TransactionReceiptEmailWithoutAccountTemplate.html");
            return string.Format(html, 
                receiptData.Store.Name, 
                receiptData.Store.Address, 
                receiptData.Store.Suburb, 
                receiptData.Store.State, 
                receiptData.Store.PostCode, 
                receiptData.Store.ABN, 
                receiptData.Kiosk.Name, 
                receiptData.TerminalTransactionId, 
                receiptData.TransactionDateTime,
                receiptData.FuelTransaction.FuelGradeName,
                receiptData.FuelTransaction.FuelVolume + " L",
                "$ " + receiptData.FinalAmount, 
                receiptData.FuelTransaction.PumpNumber,
                receiptData.FuelTransaction.FuelPrice + " &cent;/L",
                "$ " + receiptData.FinalAmount,
                "$ " + receiptData.GSTAmount,
                receiptData.ApplicationLabel,
                receiptData.CardSignature,
                receiptData.PanSeqNo,
                receiptData.ApplicationId,
                receiptData.CardATC,
                receiptData.Type,
                receiptData.TID,
                receiptData.Currency,
                receiptData.FuelTransaction.TransactionSeqNumber, 
                receiptData.AuthorizationID,
                receiptData.ResponseCode,
                accountHtml,
                "Thank You!");
            
        }
        
        public static string GetTransactionReceiptTemplate(CardReceiptViewModel cardReceipt)
        {
            var accountHtml = string.Empty;
            if (!string.IsNullOrEmpty(cardReceipt.AccountName))
            {
                var htmlacc = File.ReadAllText("wwwroot\\html\\TransactionReceiptEmailTemplate.html");
                accountHtml = string.Format(htmlacc,
                    cardReceipt.AccountName,
                    cardReceipt.AccountEmailAddress,
                    cardReceipt.VehicleRegistration,
                    cardReceipt.VehicleOdometer,
                    cardReceipt.AccountUser);
            }

            string reward = string.Empty;
            if (!string.IsNullOrWhiteSpace(cardReceipt.RewardTotalDiscount))
            {
                reward = $@"<tr>
					            <td>Reward</td>
					            <td style=""text-align: center;"">{cardReceipt.RewardDiscountPerLiter}</td>
					            <td style=""text-align: right;"">{cardReceipt.RewardTotalDiscount}</td>
				            </tr>";
            }

            var html = File.ReadAllText("wwwroot\\html\\TransactionReceiptEmailWithoutAccountTemplate.html");
            return string.Format(html,
                cardReceipt.StoreName,
                cardReceipt.StoreAddress1,
                cardReceipt.StoreAddress2,
                cardReceipt.StoreState,
                cardReceipt.StorePostalCode,
                cardReceipt.StoreABN,
                cardReceipt.KioskName,
                cardReceipt.TerminalTransactionId,
                cardReceipt.TransactionDateTime,
                cardReceipt.FuelGradeName,
                cardReceipt.FuelVolume,
                cardReceipt.TransactionPartialAmount,
                cardReceipt.FuelPumpNumber,
                cardReceipt.FuelPricePerLitre,
                cardReceipt.TransactionFinalAmount,
                cardReceipt.TransactionGSTAmount,
                cardReceipt.ApplicationLabel,
                cardReceipt.CardSignature,
                cardReceipt.PanSeqNo,
                cardReceipt.ApplicationId,
                cardReceipt.CardATC,
                cardReceipt.TransactionType,
                cardReceipt.TID,
                cardReceipt.TransactionCurrency,
                cardReceipt.FuelTransactionNumber,
                cardReceipt.AuthorisationId,
                $"{cardReceipt.HostResponse}({cardReceipt.HostResponseCode})",
                accountHtml,
                cardReceipt.InvoiceFooter,
                reward);
            
        }
    }
}
