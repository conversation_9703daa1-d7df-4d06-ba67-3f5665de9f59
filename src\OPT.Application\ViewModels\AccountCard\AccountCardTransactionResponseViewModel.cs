﻿using OPT.Domain.Models;

namespace OPT.Application.ViewModels.AccountCard
{
    public class AccountCardTransactionResponseViewModel
    {
        public string AuthCode { get; set; }
        public string HostResponse { get; set; }
        public string MerchantId { get; set; }
        public long STAN { get; set; }
        public string RRN { get; set; }

        public void LoadTransaction(AccountCardTransaction trans)
        {
            AuthCode = trans.AuthCode;
            HostResponse = trans.HostResponse;
            STAN = trans.STAN;
            RRN = trans.RRN;
        }
    }
}
