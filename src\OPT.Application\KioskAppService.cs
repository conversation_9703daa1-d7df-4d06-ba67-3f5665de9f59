﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.Specifications;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.Kiosk;
using OPT.Domain.Exceptions;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Interfaces.UoW;
using OPT.Domain.Models;
using OPT.Domain.Specification;
using System.Collections.Generic;

namespace OPT.Application
{
    public class KioskAppService : ApplicationService, IKioskAppService
    {
        private readonly IKioskService _service;
        private readonly IParameterConfigService _parameterConfigService;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;

        public KioskAppService(IUnitOfWork unitOfWork,
                                IMapper mapper,
                                IConfiguration configuration,
                                IKioskService service,
                                IParameterConfigService parameterConfigService) : base(unitOfWork)
        {
            _service = service;
            _mapper = mapper;
            _configuration = configuration;
            _parameterConfigService = parameterConfigService;
        }

        public void AddOrUpdate(KioskAddViewModel viewModel)
        {
            if (viewModel.IsValid<KioskAddViewModel, KioskAddSpecification>())
            {
                _service.AddOrUpdate(_mapper.Map<Kiosk>(viewModel), CryptographyHelper.Encrypt(RandomHelper.RandomString(6)));
                Commit();
            }
        }

        public KioskValidResponseViewModel Valid(KioskValidViewModel viewModel)
        {
            if (viewModel.IsValid<KioskValidViewModel, KioskValidSpecification>())
            {
                var k = _service.GetById(viewModel.ID);
                if (k == null)
                {
                    throw new BusinessException("Kiosk is invalid.");
                }
                if (!string.IsNullOrWhiteSpace(k.DeviceID) && k.DeviceID != viewModel.DeviceID)
                {
                    throw new BusinessException("Kiosk cannot be validated because an associated device already exists.");
                }
                var kvm = _mapper.Map<KioskValidViewModel>(k);
                if (kvm.ValidCode != viewModel.ValidCode || !k.Active)
                {
                    throw new BusinessException("Kiosk is invalid.");
                }

                k.DeviceID = viewModel.DeviceID;

                return _mapper.Map<KioskValidResponseViewModel>(k);
            }
            return null;
        }

        public KioskValidResponseViewModel Valid(string deviceId)
        {
            if(string.IsNullOrWhiteSpace(deviceId))
            {
                throw new BusinessException("Kiosk is invalid.");
            }

            var k = _service.GetByDeviceId(deviceId);
            if (k != null)
            {
                if (!k.Active)
                {
                    throw new BusinessException("Kiosk is invalid.");
                }

                return _mapper.Map<KioskValidResponseViewModel>(k);
            }
            else
            {
                Kiosk kiosk = new Kiosk()
                {
                    Active = false,
                    DeviceID = deviceId,
                    Name = "New device " + deviceId,
                    StoreID = Guid.Empty
                };
                _service.AddOrUpdate(kiosk, CryptographyHelper.Encrypt(RandomHelper.RandomString(6)));
                Commit();

                throw new BusinessException($"This kiosk needs to be configured in POSMaster. (Device ID: {deviceId})");
            }
        }

        public List<KioskConfigViewModel> GetConfigsByKiosk(Kiosk kiosk)
        {
            var kioskStored = _service.GetById(kiosk.ID);
            if (kioskStored != null)
            {
                var param = _parameterConfigService.GetAll();

                bool validConfigs = kioskStored.Configs.Select(a => a.Name).Order()
                       .SequenceEqual(param.Select(b => b.PID).Order());
                if(!validConfigs)
                    throw new BusinessException("The kiosk settings are invalid, please check the settings in the POSMaster.");

                ResolveVariables(kioskStored);
                return _mapper.Map<List<KioskConfigViewModel>>(kioskStored.Configs);
            }
            else
            {
                throw new BusinessException("Kiosk is invalid.");
            }
        }

        private void ResolveVariables(Kiosk model)
        {
            if (model != null && model.Configs != null && model.Configs.Count > 0)
            {
                KioskConfig? variables;
                if ((variables = model.Configs.FirstOrDefault(x => x.Name == "variables")) != null)
                {
                    string[] var = variables.Value.Split("\r\n");
                    foreach (string s in var)
                    {
                        if (!s.StartsWith("#"))
                        {
                            string[] var2 = s.Split("=");
                            if (var2.Length == 2)
                            {
                                foreach (var conf in model.Configs)
                                {
                                    if (conf != variables)
                                    {
                                        if (conf.Value != null && conf.Value.Contains($"{{{var2[0]}}}"))
                                        {
                                            conf.Value = conf.Value.Replace($"{{{var2[0]}}}", var2[1]);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    model.Configs.Remove(variables);
                }
            }
        }

        public string Authenticate(KioskAuthenticateViewModel viewModel)
        {
            var kiosk = _service.GetByHash(viewModel.Hash);
            if (kiosk != null)
            {
                if (!kiosk.Active)
                {
                    throw new BusinessException("Kiosk is invalid.");
                }
                return TokenHelper.GetToken(kiosk, _configuration);
            }
            throw new BusinessException("Kiosk not found.");
        }

        public List<KioskViewModel> GetByStores(List<Guid> storesId)
        {
            return _mapper.Map<List<KioskViewModel>>(_service.GetByStores(storesId));
        }
        
        public List<KioskNameViewModel> GetByStores(ApiKey apiKey)
        {
            return _mapper.Map<List<KioskNameViewModel>>(_service.GetByStores(new List<Guid> { apiKey.StoreID }));
        }

        public void UpdateActive(Guid id, bool ative)
        {
            _service.UpdateActive(id, ative);
            Commit();
        }

        public KioskViewModel? GetById(Guid id)
        {
            return _mapper.Map<KioskViewModel>(_service.GetById(id));
        }

        public List<ParameterConfigViewModel> GetAllParamaterConfigs()
        {
            return _mapper.Map<List<ParameterConfigViewModel>>(_parameterConfigService.GetAll());
        }

        public async Task UploadFile(IFormFile? file, Kiosk kiosk, bool deleteOld)
        {
            if (file == null || file.Length <= 0)
            {
                throw new BusinessException("File not exists.");
            }

            var path = Path.Combine(_configuration["Upload:KiosPath"], kiosk.ID.ToString());
            var info = new DirectoryInfo(path);
            if (!info.Exists)
                info.Create();

            if (deleteOld)
            {
                var files = info.GetFiles();
                if (files.Any())
                    files.All(x =>
                    {
                        x.Delete();
                        return true;
                    });
            }

            string filePath = Path.Combine(path, file.FileName);
            using (Stream fileStream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(fileStream);
            }
        }

        public void AddInfo(KioskInfoViewModel info, Kiosk kiosk)
        {
            var ki = _mapper.Map<KioskInfo>(info);
            ki.KioskID = kiosk.ID;
            _service.AddInfo(ki);
            Commit();
        }

        public void Dispose()
        {
            _service.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
