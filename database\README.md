# OPTdb Local Database Setup

This directory contains scripts and documentation for setting up the OPTdb database on your local MS SQL Server instance for development purposes.

## Prerequisites

Before setting up the local database, ensure you have the following installed:

1. **SQL Server** (LocalDB, Express, or full version)
   - SQL Server LocalDB (recommended for development)
   - SQL Server Management Studio (SSMS) or Azure Data Studio (optional but recommended)

2. **.NET SDK** (version 6.0 or later)
   - Required for Entity Framework CLI tools

3. **Entity Framework Core Tools**
   - Will be automatically installed by the migration scripts if not present

## Quick Setup

Follow these steps to set up your local OPTdb database:

### Step 1: Create the Database
Run the database creation script using SQL Server Management Studio or sqlcmd:

```bash
# Using sqlcmd (if available)
sqlcmd -S "(localdb)\MSSQLLocalDB" -i "01_CreateDatabase.sql"

# Or execute the script in SSMS
```

### Step 2: Run Entity Framework Migrations
Choose one of the following options:

**Option A: Using PowerShell (Recommended)**
```powershell
# Navigate to the database directory
cd database

# Run the PowerShell script
.\02_RunMigrations.ps1

# For verbose output
.\02_RunMigrations.ps1 -Verbose

# For production environment
.\02_RunMigrations.ps1 -Environment Production
```

**Option B: Using Batch File**
```cmd
# Navigate to the database directory
cd database

# Run the batch script
02_RunMigrations.bat

# For different environment
02_RunMigrations.bat Production
```

**Option C: Manual EF Commands**
```bash
# Navigate to the Data project
cd src\OPT.Infra.Data

# Set environment
set ASPNETCORE_ENVIRONMENT=Development

# Install EF tools (if not installed)
dotnet tool install --global dotnet-ef

# Run migrations
dotnet ef database update --startup-project ..\OPT.API\OPT.API.csproj

# Run Identity migrations
dotnet ef database update --context IdentityOPTContext --startup-project ..\OPT.API\OPT.API.csproj
```

### Step 3: Seed Initial Data
Run the seed data script to populate essential tables:

```bash
# Using sqlcmd
sqlcmd -S "(localdb)\MSSQLLocalDB" -d "OPTdb" -i "03_SeedData.sql"

# Or execute the script in SSMS against the OPTdb database
```

## Configuration

The local database configuration is set in `src/OPT.API/appsettings.Development.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=(localdb)\\MSSQLLocalDB;Initial Catalog=OPTdb;Integrated Security=True;TrustServerCertificate=True"
  }
}
```

### Alternative Connection Strings

**For SQL Server Express:**
```json
"DefaultConnection": "Data Source=.\\SQLEXPRESS;Initial Catalog=OPTdb;Integrated Security=True;TrustServerCertificate=True"
```

**For SQL Server with SQL Authentication:**
```json
"DefaultConnection": "Data Source=(localdb)\\MSSQLLocalDB;Initial Catalog=OPTdb;User ID=OPTUser;Password=*********;TrustServerCertificate=True"
```

## Database Schema

The database includes the following main tables:

### Core Tables
- **Stores** - Store information
- **Accounts** - Customer accounts
- **Kiosks** - Fuel pump kiosks
- **Transactions** - Transaction records
- **FuelTransactions** - Fuel-specific transaction data

### Card Management
- **AccountCards** - Payment/reward cards
- **AccountCardTypes** - Card type definitions
- **AccountCardRestrictions** - Card usage restrictions
- **AccountCardTransactions** - Card transaction history

### Vehicle Management
- **VehicleRegistrations** - Vehicle registration data
- **AccountVehicleRegistrations** - Account-vehicle associations

### System Tables
- **ParameterConfigs** - System configuration parameters
- **KioskConfigs** - Kiosk-specific configurations
- **KioskInfos** - Kiosk status information
- **TransactionAudits** - Transaction audit logs
- **APIKeys** - API authentication keys

### Identity Tables
- **AspNetUsers** - User accounts
- **AspNetRoles** - User roles
- **AspNetUserRoles** - User-role associations
- (Other standard ASP.NET Identity tables)

## Troubleshooting

### Common Issues

1. **LocalDB not found**
   - Install SQL Server LocalDB from Microsoft
   - Verify installation: `sqllocaldb info`

2. **Permission denied**
   - Run scripts as Administrator
   - Check SQL Server service is running

3. **Migration fails**
   - Ensure connection string is correct
   - Check if database exists and is accessible
   - Verify Entity Framework tools are installed

4. **Seed data fails**
   - Ensure migrations have been applied first
   - Check for foreign key constraint violations

### Useful Commands

```bash
# Check LocalDB instances
sqllocaldb info

# Start LocalDB instance
sqllocaldb start MSSQLLocalDB

# Connect to LocalDB
sqlcmd -S "(localdb)\MSSQLLocalDB"

# List EF migrations
dotnet ef migrations list --startup-project ..\OPT.API\OPT.API.csproj

# Generate new migration
dotnet ef migrations add MigrationName --startup-project ..\OPT.API\OPT.API.csproj

# Remove last migration
dotnet ef migrations remove --startup-project ..\OPT.API\OPT.API.csproj
```

## Development Notes

- The database is configured for development with simplified recovery model
- Auto-close and auto-shrink are disabled for better performance
- Sample data includes test accounts, cards, and kiosks for development
- All GUIDs in seed data are generated at runtime to avoid conflicts

## Security Notes

- The default configuration uses Windows Authentication (Integrated Security)
- For production, use SQL Server Authentication with strong passwords
- Ensure proper firewall rules if accessing from remote machines
- Consider using Azure SQL Database for cloud deployments

## Support

If you encounter issues with the database setup:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Ensure SQL Server service is running
4. Check connection strings match your SQL Server configuration
