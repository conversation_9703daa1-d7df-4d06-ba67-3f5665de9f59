﻿using OPT.Domain.Models;

namespace OPT.Domain.Interfaces.Services
{
    public interface IAccountService : IDisposable
    {
        Account? GetById(Guid id);

        void Add(Account model);

        void Add(Account model, Guid storeId);

        void Update(Guid id, Account model, Guid storeId);

        void AddOrUpdateUser(Guid userId, UserAccount entity);

        void RemoveUser(Guid userId);

        List<UserAccount> GetUsers(Guid id);

        Account? GetByUser(Guid userId);

        List<Account> GetByStores(List<Guid>? storesId, List<Guid>? tenantsId);

        (int, List<Account>) GetByStoresWithPagination(List<Guid>? storesId, List<Guid>? tenantsId, int offset, int limit);

        AccountBalance? GetBalance(Guid accountId);

        Account? GetByCode(string code);

        void addJournal(AccountJournal accountJournal);

        List<AccountJournal> GetJournalsByDate(Guid accountId, DateTime dateFrom, DateTime dateTo);

        void UpdateBalance(Guid accountId, decimal amount);
    }
}
