﻿
using OPT.Application.ViewModels.User;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class ResetPasswordSpecification : ISpecificationConfiguration<ResetPasswordViewModel>
    {
        public ISpecification<ResetPasswordViewModel> Map(ISpecification<ResetPasswordViewModel> builder)
        {
            builder.IsSatisfiedBy(x => x.Id != null || !string.IsNullOrWhiteSpace(x.Email), "Must have a user identification.");
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.PassToken), "Error confirming your password.");
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.NewPassword), "New Password incorrect.");
            builder.IsSatisfiedBy(x => x.NewPassword.Length >= 6, "The new password must be at least 6 characters long.");

            return builder;
        }
    }
}
