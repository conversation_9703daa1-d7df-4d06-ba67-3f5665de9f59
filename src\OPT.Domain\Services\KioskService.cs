﻿using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;

namespace OPT.Domain.Services
{
    public class KioskService : IKioskService
    {
        private readonly IKioskRepository _repository;

        public KioskService(IKioskRepository repository)
        {
            _repository = repository;
        }

        public Kiosk? GetById(Guid id)
        {
            return _repository.GetById(id);
        }
        
        public Kiosk? GetByDeviceId(string deviceId)
        {
            return _repository.GetByDeviceId(deviceId);
        }
        
        public Kiosk? GetByHash(string validHash)
        {
            return _repository.GetByHash(validHash);
        }

        public void AddOrUpdate(Kiosk model, string validHash)
        {
            if (model.ID == Guid.Empty)
            {
                model.ValidateHash = validHash;
                _repository.Add(model);
            } else
            {
                var current = GetById(model.ID);
                if (current != null)
                {
                    model.ValidateHash = current.ValidateHash;
                    model.DeviceID = current.DeviceID;

                    model.Configs.All(x => { x.KioskID = current.ID; return true; });

                    _repository.Update(current, model);
                }
            }
        }
        
        public void Update(Kiosk model)
        {
            var current = GetById(model.ID);
            _repository.Update(current, model);
        }
        
        public void UpdateActive(Guid id, bool ative)
        {
            var current = GetById(id);
            if (current != null)
            {
                current.Active = ative;
                _repository.Update(current);
            }
        }

        public List<Kiosk> GetByStores(List<Guid> storesId)
        {
            return _repository.GetByStores(storesId);
        }

        public void AddInfo(KioskInfo info)
        {
            _repository.AddInfo(info);
        }

        public void Dispose()
        {
            _repository.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
