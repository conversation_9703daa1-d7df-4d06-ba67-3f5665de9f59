﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentEmail.Smtp" Version="3.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="7.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="7.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="7.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\OPT.Domain\OPT.Domain.csproj" />
  </ItemGroup>

</Project>
