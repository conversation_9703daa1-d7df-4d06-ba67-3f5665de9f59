﻿using ElmahCore;
using ElmahCore.Mvc;
using Microsoft.AspNetCore.Http.Features;
using OPT.API.Configuration;
using OPT.Application.Helper;
using System.Text.Json.Serialization;

namespace OPT.API
{
    /// <summary>
    /// Startup
    /// </summary>
    public class Startup
    {
        /// <summary>
        /// Startup Constructor
        /// </summary>
        /// <param name="configuration"></param>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        /// Configuration
        /// </summary>
        public IConfiguration Configuration { get; }


        /// <summary>
        /// // This method gets called by the runtime. Use this method to add services to the container.
        /// </summary>
        /// <param name="services"></param>
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddIdentitySetup(Configuration);

            services.AddJWTSetup(Configuration);

            services.AddElmah<XmlFileErrorLog>(options =>
            {
                var path = Configuration.GetSection("LoggerOptions")["Path"];

                if (!Directory.Exists(path))
                    Directory.CreateDirectory(path);

                options.LogPath = path;
                options.OnPermissionCheck = context => context.GetUser() != null;
            });
            services.AddControllers().AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.DefaultIgnoreCondition
                       = JsonIgnoreCondition.WhenWritingDefault;
            });
            services.AddHttpContextAccessor();
            services.AddSwaggerSetup();
            services.AddAutomapperSetup();
            services.AddDIConfiguration();
            services.AddHealthCheckSetup(Configuration);

            services.Configure<FormOptions>(o =>
            {
                o.ValueLengthLimit = int.MaxValue;
                o.MultipartBodyLengthLimit = int.MaxValue;
                o.MemoryBufferThreshold = int.MaxValue;
            });

            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy",
                    builder => builder.AllowAnyOrigin()
                    .AllowAnyMethod()
                    .AllowAnyHeader());
            });
        }

        /// <summary>
        /// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "OPT API v1"));
            }

            app.UseSwagger(c =>
            {
                c.RouteTemplate = "public/swagger/{documentName}/swagger.json";
            });
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/public/swagger/public/swagger.json", "OPT API Reference v1");
                c.RoutePrefix = "public/swagger";
            });

            app.ConfigureExceptionHandler();

            app.UseCors("CorsPolicy");

            app.UseHttpsRedirection();

            app.UseRouting();

            app.UseAuthentication();

            app.UseAuthorization();

            app.UseElmah();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHealthCheckSetup();
            });
        }
    }
}
