﻿using OPT.Application.Helper;

namespace OPT.Application.ViewModels.Account
{
    public class UnitedAccountFileViewModel : FileReaderHelper
    {
        public UnitedAccountFileViewModel(string filePath) : base(filePath)
        {
        }

        public bool ReadNew()
        {
            if (ReadLine())
            {
                if (LineLength == 0)
                    return ReadNew();

                Code = Next(6);
                if (Code.StartsWith("H") || Code.StartsWith("T"))
                    return ReadNew();
                Name = Next(50);
                if (string.IsNullOrEmpty(Name))
                    Name = Code;
                CurrentBalance = -NextDecimal(11);
                CreditLimit = NextDecimal(7);
                Active = !NextBool(1).Value;
                Next(1);//Flag Pricing - Ignore
                FlagOdometer = Next(1).ToCharArray()[0];
                if (FlagOdometer == '0')
                    FlagOdometer = null;
                return true;
            }
            return false;
        }

        public string Name { get; set; }

        public decimal? CreditLimit { get; set; }

        public bool Active { get; set; }

        public string? Code { get; set; }

        public char? FlagOdometer { get; set; }

        public Guid? TenantID { get; set; }

        public decimal? CurrentBalance { get; set; }
    }
}
