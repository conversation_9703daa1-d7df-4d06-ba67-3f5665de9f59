﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using OPT.Application.ViewModels.AccountCard;
using OPT.Domain.Models;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace OPT.Application.Helper
{
    public static class TokenHelper
    {
        public static string GetToken(User user, IConfiguration _configuration)
        {
            var authSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JWT:Secret"]));

            var token = new JwtSecurityToken(
                issuer: _configuration["JWT:ValidIssuer"],
                audience: _configuration["JWT:ValidAudience"],
                expires: DateTime.Now.AddHours(8),
                claims: new List<Claim>
                    {
                        new Claim(ClaimTypes.Sid, user.Id.ToString()),
                        new Claim(ClaimTypes.Name, user.UserName),
                        new Claim(ClaimTypes.Role, user.Role),
                        //new Claim(ClaimTypes.GroupSid, user.TenantID.ToString()),
                    },
                signingCredentials: new SigningCredentials(authSigningKey, SecurityAlgorithms.HmacSha256)
                );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
        
        public static string GetToken(Kiosk kiosk, IConfiguration _configuration)
        {
            var authSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JWT:Secret"]));

            var token = new JwtSecurityToken(
                issuer: _configuration["JWT:ValidIssuer"],
                audience: _configuration["JWT:ValidAudience"],
                expires: DateTime.Now.AddHours(8),
                claims: new List<Claim>
                    {
                        new Claim(ClaimTypes.Sid, kiosk.ID.ToString()),
                        new Claim(ClaimTypes.Name, kiosk.Name),
                        new Claim(ClaimTypes.Role, Role.Kiosk),
                        //new Claim(ClaimTypes.GroupSid, user.TenantID.ToString()),
                    },
                signingCredentials: new SigningCredentials(authSigningKey, SecurityAlgorithms.HmacSha256)
                );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
        
        public static string GetToken(AccountCardAuthenticateViewModel viewModel, IConfiguration _configuration)
        {
            var authSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JWT:Secret"]));

            var token = new JwtSecurityToken(
                issuer: _configuration["JWT:ValidIssuer"],
                audience: _configuration["JWT:ValidAudience"],
                expires: DateTime.Now.AddMinutes(5),
                claims: new List<Claim>
                    {
                        new Claim(ClaimTypes.Sid, viewModel.KioskId.ToString()),
                        new Claim(ClaimTypes.Name, viewModel.TerminalId),
                        new Claim(ClaimTypes.Role, Role.Card),
                        //new Claim(ClaimTypes.GroupSid, user.TenantID.ToString()),
                    },
                signingCredentials: new SigningCredentials(authSigningKey, SecurityAlgorithms.HmacSha256)
                );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
        
        public static string GetToken(ApiKey model, IConfiguration _configuration)
        {
            var authSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JWT:Secret"]));

            var token = new JwtSecurityToken(
                issuer: _configuration["JWT:ValidIssuer"],
                audience: _configuration["JWT:ValidAudience"],
                expires: DateTime.Now.AddHours(1),
                claims: new List<Claim>
                    {
                        new Claim(ClaimTypes.Sid, model.ID.ToString()),
                        new Claim(ClaimTypes.Name, model.Name),
                        new Claim(ClaimTypes.GroupSid, model.StoreID.ToString()),
                        new Claim(ClaimTypes.Role, Role.API),
                        //new Claim(ClaimTypes.GroupSid, user.TenantID.ToString()),
                    },
                signingCredentials: new SigningCredentials(authSigningKey, SecurityAlgorithms.HmacSha256)
                );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        public static User GetUser(this HttpContext context)
        {
            try
            {
                var accessToken = context.Request.Headers["Authorization"];
                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadToken(accessToken[0].Substring(7));
                var tokenS = jsonToken as JwtSecurityToken;

                if (tokenS != null)
                {
                    return new User
                    {
                        Id = new Guid(tokenS.Claims.First(claim => claim.Type == ClaimTypes.Sid).Value),
                        UserName = tokenS.Claims.First(claim => claim.Type == ClaimTypes.Name).Value,
                        Role = tokenS.Claims.First(claim => claim.Type == ClaimTypes.Role).Value,
                        //TenantID = new Guid(tokenS.Claims.First(claim => claim.Type == ClaimTypes.GroupSid).Value)
                    };
                }
            }
            catch (Exception) { }
            throw new Exception("unable to access authentication data.");
        }
        
        public static Kiosk GetKiosk(this HttpContext context)
        {
            try
            {
                var accessToken = context.Request.Headers["Authorization"];
                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadToken(accessToken[0].Substring(7));
                var tokenS = jsonToken as JwtSecurityToken;

                if (tokenS != null)
                {
                    return new Kiosk
                    {
                        ID = new Guid(tokenS.Claims.First(claim => claim.Type == ClaimTypes.Sid).Value),
                        Name = tokenS.Claims.First(claim => claim.Type == ClaimTypes.Name).Value
                    };
                }
            }
            catch (Exception) { }
            throw new Exception("unable to access authentication data.");
        }

        public static AccountCardAuthenticateViewModel GetAccountCardAuthenticate(this HttpContext context)
        {
            try
            {
                var accessToken = context.Request.Headers["Authorization"];
                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadToken(accessToken[0].Substring(7));
                var tokenS = jsonToken as JwtSecurityToken;

                if (tokenS != null)
                {
                    return new AccountCardAuthenticateViewModel
                    {
                        KioskId = new Guid(tokenS.Claims.First(claim => claim.Type == ClaimTypes.Sid).Value),
                        TerminalId = tokenS.Claims.First(claim => claim.Type == ClaimTypes.Name).Value
                    };
                }
            }
            catch (Exception) { }
            throw new Exception("unable to access authentication data.");
        }
        
        public static ApiKey GetApiKey(this HttpContext context)
        {
            try
            {
                var accessToken = context.Request.Headers["Authorization"];
                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadToken(accessToken[0].Substring(7));
                var tokenS = jsonToken as JwtSecurityToken;

                if (tokenS != null)
                {
                    return new ApiKey
                    {
                        ID = new Guid(tokenS.Claims.First(claim => claim.Type == ClaimTypes.Sid).Value),
                        Name = tokenS.Claims.First(claim => claim.Type == ClaimTypes.Name).Value,
                        StoreID = new Guid(tokenS.Claims.First(claim => claim.Type == ClaimTypes.GroupSid).Value)
                    };
                }
            }
            catch (Exception) { }
            throw new Exception("unable to access authentication data.");
        }
    }
}
