﻿using AutoMapper;
using OPT.Application.Interfaces;
using OPT.Application.Specifications;
using OPT.Application.ViewModels.VehicleRegistration;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Interfaces.UoW;
using OPT.Domain.Models;
using OPT.Domain.Specification;

namespace OPT.Application
{
    public class VehicleRegistrationAppService : ApplicationService, IVehicleRegistrationAppService
    {
        private readonly IVehicleRegistrationService _service;
        private readonly IAccountService _accountService;
        private readonly IMapper _mapper;

        public VehicleRegistrationAppService(IUnitOfWork unitOfWork,
                                IMapper mapper,
                                IVehicleRegistrationService service,
                                IAccountService accountService) : base(unitOfWork)
        {
            _service = service;
            _accountService = accountService;
            _mapper = mapper;
        }

        public void Add(VehicleRegistrationAddViewModel viewModel)
        {
            var specification = new VehicleRegistrationAddedSpecification(_service);
            if (specification.IsValid(viewModel))
            {
                _service.Add(_mapper.Map<VehicleRegistration>(viewModel), viewModel.AccountId);
                Commit();
            }
        }
        
        public Guid? Add(VehicleRegistrationAddViewModel viewModel, ApiKey apiKey)
        {
            var account = _accountService.GetById(viewModel.AccountId);
            if (new AccountValidSpecification(apiKey).IsValid(account))
            {
                var specification = new VehicleRegistrationAddedSpecification(_service);
                if (specification.IsValid(viewModel))
                {
                    var vr = _mapper.Map<VehicleRegistration>(viewModel);
                    _service.Add(vr, viewModel.AccountId);
                    Commit();
                    return vr.ID;
                }
            }
            return null;
        }

        public void Update(VehicleRegistrationViewModel viewModel)
        {
            var specification = new VehicleRegistrationUpdateSpecification(_service);
            if (specification.IsValid(viewModel))
            {
                _service.Update(_mapper.Map<VehicleRegistration>(viewModel));
                Commit();
            }
        }
        
        public void Update(Guid id, VehicleRegistrationAddViewModel viewModel, ApiKey apiKey)
        {
            var account = _accountService.GetById(viewModel.AccountId);
            if (new AccountValidSpecification(apiKey).IsValid(account))
            {
                var specification = new VehicleRegistrationAddUpdateSpecification(_service, id);
                if (specification.IsValid(viewModel))
                {
                    var vehicle = _mapper.Map<VehicleRegistration>(viewModel);
                    vehicle.ID = id;
                    _service.Update(vehicle, apiKey.StoreID);
                    Commit();
                }
            }
        }

        public void Delete(Guid id)
        {
            _service.Delete(id);
            Commit();
        }
        
        public void Delete(Guid id, ApiKey apiKey)
        {
            _service.Delete(id, apiKey.StoreID);
            Commit();
        }

        public List<VehicleRegistrationViewModel> ListByAccount(Guid accountId)
        {
            return _mapper.Map<List<VehicleRegistrationViewModel>>(_service.GetByAccount(accountId));
        }
        
        public List<VehicleRegistrationViewModel> ListByAccount(Guid accountId, ApiKey apiKey)
        {
            var account = _accountService.GetById(accountId);
            if (new AccountValidSpecification(apiKey).IsValid(account))
            {
                return _mapper.Map<List<VehicleRegistrationViewModel>>(_service.GetByAccount(accountId));
            }
            return null;
        }

        public VehicleRegistrationViewModel? GetById(Guid id)
        {
            return _mapper.Map<VehicleRegistrationViewModel?>(_service.GetById(id));
        }
        
        public VehicleRegistrationViewModel? GetById(Guid id, ApiKey apiKey)
        {
            return _mapper.Map<VehicleRegistrationViewModel?>(_service.GetById(id, apiKey.StoreID));
        }

        public void Dispose()
        {
            _service.Dispose();
            _accountService.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
