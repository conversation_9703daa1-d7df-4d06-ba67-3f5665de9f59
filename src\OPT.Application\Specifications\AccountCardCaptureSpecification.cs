﻿using OPT.Application.ViewModels.AccountCard;
using OPT.Domain.Models;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class AccountCardCaptureSpecification : ISpecificationConfiguration<AccountCardTransactionViewModel>
    {
        private AccountCardTransaction? transaction;

        public AccountCardCaptureSpecification(AccountCardTransaction? transaction)
        {
            this.transaction = transaction;
        }

        public ISpecification<AccountCardTransactionViewModel> Map(ISpecification<AccountCardTransactionViewModel> builder)
        {
            builder.SetStopOnFirstFailure(true);

            //Check if the transaction exists.
            builder.IsSatisfiedBy(x => transaction != null, "12");//Invalid Transaction

            //Check if the transaction has been finalized.
            builder.IsSatisfiedBy(x => transaction != null && (transaction.AmountCaptured == null && transaction.AmountReversed == null), "12");//Invalid Transaction
            
            //Check if the transaction has been authorized.
            builder.IsSatisfiedBy(x => transaction != null && transaction.AmountAuthorized != null, "12");//Invalid Transaction

            //Check if the transaction has been authorized.
            builder.IsSatisfiedBy(x => transaction != null && transaction.AmountAuthorized >= x.Amount, "13");//Invalid Amount

            return builder;
        }
    }
}
