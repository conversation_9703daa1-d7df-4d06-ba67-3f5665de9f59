﻿using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.Transaction
{
	public class TransactionFilterV2ViewModel
    {
        [SwaggerSchema("The unique ID of the account for which transactions will be filtered.", Nullable = true)]
        public Guid? AccountId { get; set; }

        [SwaggerSchema("The unique ID of the kiosk (OPT Device) from which the transactions were conducted.", Nullable = true)]
        public Guid? KioskId { get; set; }

        [SwaggerSchema("The start date of the date range to filter transactions (format: yyyy-MM-dd).", Nullable = true)]
        public DateTime? DateFrom { get; set; }

        [SwaggerSchema("The end date of the date range to filter transactions (format: yyyy-MM-dd).", Nullable = true)]
        public DateTime? DateTo { get; set; }

        [SwaggerSchema(ReadOnly = true)]
        public List<Guid>? StoresId { get; set; }

        [SwaggerSchema("Offset to start.", Nullable = false)]
        public int Offset { get; set; }

        [SwaggerSchema("Number of transactions in the response.", Nullable = false)]
        public int Limit { get; set; }
    }
}
