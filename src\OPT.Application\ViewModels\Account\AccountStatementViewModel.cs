﻿using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.Account
{
    public class AccountStatementViewModel
    {
        [SwaggerSchema("Date of Statement", Nullable = false)]
        public DateTime Date { get; set; }

        [SwaggerSchema("Amount of statement. May be positive or negative.", Nullable = false)]
        public decimal Amount { get; set; }

        [SwaggerSchema("Specifies the origin of the statement.", Nullable = false)]
        public string Provider { get; set; }

        [SwaggerSchema("Specifies the reason for the statement.", Nullable = false)]
        public string? Reason { get; set; }
    }
}
