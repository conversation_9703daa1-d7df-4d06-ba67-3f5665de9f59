﻿using OPT.Application.ViewModels.Kiosk;

namespace OPT.Application.ViewModels.RewardCard
{
    public class RewardCardTransactionInternalViewModel
    {
        public DateTime CreatedDateTime { get; set; }

        public long TotalDiscountApplied { get; set; }

        public RewardCardDiscountViewModel RewardCardDiscount { get; set; }

        public KioskNameViewModel Kiosk { get; set; }
    }
}
