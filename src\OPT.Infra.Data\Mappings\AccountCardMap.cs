﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class AccountCardMap : Mapping<AccountCard>
    {
        public override void Map(EntityTypeBuilder<AccountCard> builder)
        {
            builder.ToTable("AccountCards");

            builder.<PERSON><PERSON><PERSON>(x => x.ID);

            builder.Ignore(x => x.User);

            builder.HasOne(x => x.Account).WithMany().HasForeignKey(x => x.AccountId).OnDelete(DeleteBehavior.NoAction);

            builder.HasMany(x => x.Restrictions).WithOne().HasForeignKey(x => x.AccountCardID).OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(x => x.AccountCardType).WithMany().HasForeignKey(x => x.Type).OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(x => x.Store).WithMany().HasForeignKey(x => x.StoreID).OnDelete(DeleteBehavior.NoAction);

            builder.HasMany(x => x.Discounts).WithOne(x => x.AccountCard).HasForeignKey(x => x.AccountCardID).OnDelete(DeleteBehavior.Cascade);

            base.Map(builder);
        }
    }
}
