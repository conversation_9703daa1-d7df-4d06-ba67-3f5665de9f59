﻿using Microsoft.AspNetCore.Mvc;
using OPT.API.Attributes;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.RewardCard;
using Swashbuckle.AspNetCore.Annotations;

namespace OPT.API.Controllers
{
    /// <summary>
    /// RewardCards are reward cards provided to selected users, offering discounts on fuel purchases. These discounts are given in cents per liter and may or may not have a liter limit for the discount to be applied. Additionally, the discount is specific to each type of fuel (FuelGrade).
    /// 
    /// ### Features:
    /// 1. Fuel Discounts:
    ///    - Each RewardCard provides discounts in cents per liter of fuel, offering significant savings to users.
    ///    
    /// 1. Discount Limits per Liter:
    ///    - Some RewardCards may have a limit in liters for the discount to be applied.This ensures that the discount is offered equitably.
    ///    
    /// 1. Specific to Fuel Type:
    ///    - The discount offered by the RewardCard applies to a specific type of fuel, corresponding to the FuelGrade category.
    ///    
    /// 1. Restriction to Bank Card Payment:
    ///    - The RewardCard discount can only be applied when payment is made using a bank card.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerControllerOrder(6)]
    public class RewardCardController : ControllerBase
    {
        private readonly IAccountCardAppService _appService;

        /// <summary>
        /// CardController Constructor
        /// </summary>
        /// <param name="appService"></param>
        public RewardCardController(IAccountCardAppService appService)
        {
            _appService = appService;
        }

        /// <summary>
        /// Valid RewardCard to be used
        /// </summary>
        /// <param name="viewModel"></param>
        /// <returns></returns>
        [Authorize(Role.Kiosk)]
        [HttpPost("valid")]
        [ApiExplorerSettings(GroupName = "v1")]
        public List<RewardCardDiscountViewModel> Valid(RewardCardValidViewModel viewModel)
        {
            return _appService.Valid(viewModel, HttpContext.GetKiosk());
        }

        /// <summary>
        /// Get Reward Transactions By Date
        /// </summary>
        /// <param name="rewardcardid"></param>
        /// <param name="datefrom"></param>
        /// <param name="dateto"></param>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [ApiExplorerSettings(GroupName = "v1")]
        [HttpGet("transaction/{rewardcardid}/{datefrom}/{dateto}")]
        public List<RewardCardTransactionInternalViewModel> GetTransactionsByDate(Guid rewardcardid, DateTime datefrom, DateTime dateto)
        {
            return _appService.GetTransactionsByDate(rewardcardid, datefrom, dateto);
        }

        #region Exposed Apis

        [SwaggerOperation(
            Summary = "Add new Reward Card",
            Description = "This endpoint allows the creation of a new reward card. The expiry date format must be in YYMM."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the succesfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpPost("v1")]
        public ResponseViewModel AddPublic(
            [SwaggerRequestBody("RewardCard object informations", Required = true)]
            RewardCardAddViewModel card)
        {
            var id = _appService.Add(card, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Card addded with success.", id);
        }

        [SwaggerOperation(
            Summary = "Update RewardCard",
            Description = "This endpoint allows for the update of information for an existing reward card based on the reward card ID. The expiry date format must be in YYMM."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the succesfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpPut("v1/{id}")]
        public ResponseViewModel UpdatePublic(
            [SwaggerParameter("RewardCard ID", Required = true)]
            Guid id,
            [SwaggerRequestBody("RewardCard object informations", Required = true)]
            RewardCardUpdateViewModel card)
        {
            _appService.Update(id, card, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Card updated with success.");
        }

        [SwaggerOperation(
            Summary = "Add a range of new Reward Cards",
            Description = "This endpoint allows the creation of a range of new reward cards. The expiry date format must be in YYMM."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the succesfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpPost("v1/range")]
        public ResponseViewModel AddPublic(
            [SwaggerRequestBody("List of RewardCards object informations", Required = true)]
            List<RewardCardAddViewModel> cards)
        {
            _appService.Add(cards, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Cards added with success.");
        }

        [SwaggerOperation(
            Summary = "List RewardCards",
            Description = "This endpoint allows for list all reward cards."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return a list of reward cards object.", typeof(List<RewardCardItemViewModel>))]
        [Authorize(Role.API)]
        [HttpGet("v1")]
        public List<RewardCardItemViewModel> ListPublic()
        {
            return _appService.ListByStores(HttpContext.GetApiKey());
        }

        [SwaggerOperation(
            Summary = "Get RewardCard By ID",
            Description = "This endpoint allows for get information for an existing reward card based on the RewardCard ID."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the reward card object.", typeof(RewardCardViewModel))]
        [Authorize(Role.API)]
        [HttpGet("v1/{id}")]
        public RewardCardViewModel? GetPublic(
            [SwaggerParameter("RewardCard ID", Required = true)]
            Guid id)
        {
            return _appService.GetReward(id, HttpContext.GetApiKey());
        }

        /*[SwaggerOperation(
            Summary = "Delete a Reward Card",
            Description = "This endpoint allows remove a reward card based on the RwardCard ID."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the succesfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpDelete("v1/{id}")]
        public ResponseViewModel DeletePublic(
            [SwaggerParameter("RewardCard ID", Required = true)]
            Guid id)
        {
            _appService.Delete(id, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Card {id} delete with success.");
        }*/

        [SwaggerOperation(
            Summary = "Get RewardCard Transactions By Date",
            Description = @"This endpoint allows you to retrieve the transaction history for a specific reward card by date."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return a list of RewardCard Transactions object.", typeof(List<RewardCardTransactionViewModel>))]
        [Authorize(Role.API)]
        [HttpGet("v1/{id}/transaction/{datefrom}/{dateto}")]
        public List<RewardCardTransactionViewModel> GetTransactionsByDatePublic(
            [SwaggerParameter("RewardCard ID", Required = true)]
            Guid id,
            [SwaggerParameter("Starting date the list of transactions that will be returned. Format yyyy-MM-dd", Required = true)]
            DateTime datefrom,
            [SwaggerParameter("End date the list of transactions that will be returned. Format yyyy-MM-dd", Required = true)]
            DateTime dateto)
        {
            return _appService.GetTransactionsByDate(id, datefrom, dateto, HttpContext.GetApiKey());
        }

        #endregion

    }
}
