﻿using Microsoft.AspNetCore.Mvc;
using OPT.API.Attributes;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.Kiosk;
using Swashbuckle.AspNetCore.Annotations;

namespace OPT.API.Controllers
{
    /// <summary>
    /// Kiosks are OPT self-service devices strategically located in physical stores, designed to provide users with the convenience of refueling their vehicles quickly and efficiently. Their information and settings are centrally managed and stored through POSMaster.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class KioskController : ControllerBase
    {
        private readonly IKioskAppService _appService;

        /// <summary>
        /// KioskController Constructor
        /// </summary>
        /// <param name="appService"></param>
        public KioskController(IKioskAppService appService)
        {
            _appService = appService;
        }

        /// <summary>
        /// Get Kiosk
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpGet("{id}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public KioskViewModel? Get(Guid id)
        {
            return _appService.GetById(id);
        }
        
        /// <summary>
        /// Add New Kiosk or update it
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel AddOrUpdate(KioskAddViewModel kiosk)
        {
            _appService.AddOrUpdate(kiosk);
            return ResponseViewModel.Ok($"Kiosk {kiosk.Name} added with success.");
        }
        
        /// <summary>
        /// Valid Kiosk
        /// </summary>
        /// <returns></returns>
        [HttpPost("valid")]
        [ApiExplorerSettings(GroupName = "v1")]
        public KioskValidResponseViewModel Valid(KioskValidViewModel kiosk)
        {
            var kioskValid = _appService.Valid(kiosk);
            return kioskValid;
        }
        
        /// <summary>
        /// Valid Kiosk
        /// </summary>
        /// <returns></returns>
        [HttpGet("valid/{deviceid}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public KioskValidResponseViewModel Valid(string deviceid)
        {
            var kioskValid = _appService.Valid(deviceid);
            return kioskValid;
        }

        /// <summary>
        /// Get Configurations Kiosk
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Kiosk)]
        [HttpGet("config")]
        [ApiExplorerSettings(GroupName = "v1")]
        public List<KioskConfigViewModel> GetCongiurations()
        {
            var configs = _appService.GetConfigsByKiosk(HttpContext.GetKiosk());
            return configs;
        }
        
        /// <summary>
        /// list Kiosk by stores
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("stores")]
        [ApiExplorerSettings(GroupName = "v1")]
        public List<KioskViewModel> GetByStores(List<Guid> storesId)
        {
            return _appService.GetByStores(storesId);
        }

        /// <summary>
        /// Kiosk authentication
        /// </summary>
        /// <param name="model"></param>
        [HttpPost("authenticate")]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel Authenticate(KioskAuthenticateViewModel model)
        {
            return ResponseViewModel.Ok(_appService.Authenticate(model));
        }

        /// <summary>
        /// Change Kiosk activation
        /// </summary>
        /// <param name="id">Kiosk id</param>
        /// <param name="active">true ou false</param>
        [Authorize(Role.POSMaster)]
        [HttpPut("{id}/active/{active}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel UpdateActive(Guid id, bool active)
        {
            _appService.UpdateActive(id, active);
            return ResponseViewModel.Ok($"Kiosk {id} updated with success.");
        }
        
        /// <summary>
        /// Get Parameters Configs
        /// </summary>
        [Authorize(Role.POSMaster)]
        [HttpGet("parameters")]
        [ApiExplorerSettings(GroupName = "v1")]
        public List<ParameterConfigViewModel> GetParameterConfigs()
        {
            return _appService.GetAllParamaterConfigs();
        }

        /// <summary>
        /// Upload File from Kiosk
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [Authorize(Role.Kiosk)]
        [HttpPost("upload")]
        [DisableRequestSizeLimit]
        [ApiExplorerSettings(GroupName = "v1")]
        public async Task<ResponseViewModel> UploadFile(IFormFile file)
        {
            await _appService.UploadFile(file, HttpContext.GetKiosk(), true);
            return ResponseViewModel.Ok();
        }
        
        /// <summary>
        /// Upload Kiosk info
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Kiosk)]
        [HttpPost("info")]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel AddInfo(KioskInfoViewModel viewModel)
        {
            _appService.AddInfo(viewModel, HttpContext.GetKiosk());
            return ResponseViewModel.Ok();
        }

        #region Exposed Apis

        [SwaggerOperation(
            Summary = "Get Kisoks",
            Description = @"This endpoint allows you to retrieve all Kiosks available."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return a list of Kiosk available.", typeof(List<KioskNameViewModel>))]
        [Authorize(Role.API)]
        [HttpGet("v1")]
        public List<KioskNameViewModel> GetAllPublic()
        {
            return _appService.GetByStores(HttpContext.GetApiKey());
        }

        #endregion

    }
}
