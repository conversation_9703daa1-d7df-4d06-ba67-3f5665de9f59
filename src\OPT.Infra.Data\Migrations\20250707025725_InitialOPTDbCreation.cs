﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OPT.Infra.Data.Migrations
{
    /// <inheritdoc />
    public partial class InitialOPTDbCreation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AccountCardTypes",
                columns: table => new
                {
                    ID = table.Column<string>(type: "nvarchar(1)", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Payment = table.Column<bool>(type: "bit", nullable: false),
                    Reward = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccountCardTypes", x => x.ID);
                });

            migrationBuilder.CreateTable(
                name: "AccountDiscounts",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DiscountTypeId = table.Column<int>(type: "int", nullable: false),
                    DiscountValue = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Active = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccountDiscounts", x => x.ID);
                });

            migrationBuilder.CreateTable(
                name: "AccountJournals",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    JournalDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    AccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    JournalTypeId = table.Column<int>(type: "int", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    POSMasterUserId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Reason = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccountJournals", x => x.ID);
                });

            migrationBuilder.CreateTable(
                name: "FuelTransactions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TransactionSeqNumber = table.Column<long>(type: "bigint", nullable: false),
                    PumpNumber = table.Column<int>(type: "int", nullable: false),
                    FuelAmount = table.Column<long>(type: "bigint", nullable: false),
                    FuelVolume = table.Column<long>(type: "bigint", nullable: false),
                    FuelPrice = table.Column<long>(type: "bigint", nullable: false),
                    FuelGradeId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    FuelGradeName = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FuelTransactions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ParameterConfigs",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    InputType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DataType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PID = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Length = table.Column<int>(type: "int", nullable: true),
                    Required = table.Column<bool>(type: "bit", nullable: false),
                    Defaultvalue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Select = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Order = table.Column<int>(type: "int", nullable: false),
                    Active = table.Column<bool>(type: "bit", nullable: false),
                    Group = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ParameterConfigs", x => x.ID);
                });

            migrationBuilder.CreateTable(
                name: "Stores",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TenantID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ABN = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Address = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Suburb = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    PostCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Region = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    State = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Phone = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Type = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProductPromotions = table.Column<bool>(type: "bit", nullable: false),
                    CigarettePromotions = table.Column<bool>(type: "bit", nullable: false),
                    Suspended = table.Column<bool>(type: "bit", nullable: false),
                    Code3Digits = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Code4Digits = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Stores", x => x.ID);
                });

            migrationBuilder.CreateTable(
                name: "VehicleRegistrations",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RegistrationNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Active = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleRegistrations", x => x.ID);
                });

            migrationBuilder.CreateTable(
                name: "Accounts",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Email = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Active = table.Column<bool>(type: "bit", nullable: false),
                    MobileNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreditLimit = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FlagOdometer = table.Column<string>(type: "nvarchar(1)", nullable: true),
                    StoreID = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantID = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Accounts", x => x.ID);
                    table.ForeignKey(
                        name: "FK_Accounts_Stores_StoreID",
                        column: x => x.StoreID,
                        principalTable: "Stores",
                        principalColumn: "ID");
                });

            migrationBuilder.CreateTable(
                name: "APIKeys",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    AccessToken = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TokenDateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Active = table.Column<bool>(type: "bit", nullable: false),
                    StoreID = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_APIKeys", x => x.ID);
                    table.ForeignKey(
                        name: "FK_APIKeys_Stores_StoreID",
                        column: x => x.StoreID,
                        principalTable: "Stores",
                        principalColumn: "ID");
                });

            migrationBuilder.CreateTable(
                name: "Kiosks",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ValidateHash = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DeviceID = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Active = table.Column<bool>(type: "bit", nullable: false),
                    StoreID = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Kiosks", x => x.ID);
                    table.ForeignKey(
                        name: "FK_Kiosks_Stores_StoreID",
                        column: x => x.StoreID,
                        principalTable: "Stores",
                        principalColumn: "ID");
                });

            migrationBuilder.CreateTable(
                name: "AccountBalances",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    LastBalanceDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    AccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccountBalances", x => x.ID);
                    table.ForeignKey(
                        name: "FK_AccountBalances_Accounts_AccountId",
                        column: x => x.AccountId,
                        principalTable: "Accounts",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AccountCards",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PAN = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ExpiryDate = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    PIN = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Active = table.Column<bool>(type: "bit", nullable: false),
                    TransactionLimit = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    AccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    UserId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PAN19 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BarCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    HolderName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Type = table.Column<string>(type: "nvarchar(1)", nullable: false),
                    Status = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VehicleOdometer = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StoreID = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccountCards", x => x.ID);
                    table.ForeignKey(
                        name: "FK_AccountCards_AccountCardTypes_Type",
                        column: x => x.Type,
                        principalTable: "AccountCardTypes",
                        principalColumn: "ID");
                    table.ForeignKey(
                        name: "FK_AccountCards_Accounts_AccountId",
                        column: x => x.AccountId,
                        principalTable: "Accounts",
                        principalColumn: "ID");
                    table.ForeignKey(
                        name: "FK_AccountCards_Stores_StoreID",
                        column: x => x.StoreID,
                        principalTable: "Stores",
                        principalColumn: "ID");
                });

            migrationBuilder.CreateTable(
                name: "AccountVehicleRegistrations",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    VehicleRegistrationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccountVehicleRegistrations", x => x.ID);
                    table.ForeignKey(
                        name: "FK_AccountVehicleRegistrations_Accounts_AccountId",
                        column: x => x.AccountId,
                        principalTable: "Accounts",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AccountVehicleRegistrations_VehicleRegistrations_VehicleRegistrationId",
                        column: x => x.VehicleRegistrationId,
                        principalTable: "VehicleRegistrations",
                        principalColumn: "ID");
                });

            migrationBuilder.CreateTable(
                name: "UserAccounts",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserID = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AccountID = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserAccounts", x => x.ID);
                    table.ForeignKey(
                        name: "FK_UserAccounts_Accounts_AccountID",
                        column: x => x.AccountID,
                        principalTable: "Accounts",
                        principalColumn: "ID");
                });

            migrationBuilder.CreateTable(
                name: "KioskConfigs",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    KioskID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Value = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KioskConfigs", x => x.ID);
                    table.ForeignKey(
                        name: "FK_KioskConfigs_Kiosks_KioskID",
                        column: x => x.KioskID,
                        principalTable: "Kiosks",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "KioskInfos",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    KioskID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Version = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastOnline = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastScreen = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KioskInfos", x => x.ID);
                    table.ForeignKey(
                        name: "FK_KioskInfos_Kiosks_KioskID",
                        column: x => x.KioskID,
                        principalTable: "Kiosks",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TransactionAudits",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AuditDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    TransactionCreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    KioskTransactionNumber = table.Column<long>(type: "bigint", nullable: false),
                    CardSignature = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CardExpiryDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Processor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TransactionStatus = table.Column<byte>(type: "tinyint", nullable: true),
                    TransactionAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TransactionFinalAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TransactionResponseCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TransactionResponse = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VehicleRegistration = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VehicleOdometer = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PumpNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FuelGrade = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FuelGradeName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    KioskID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AccountCardID = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    AccountID = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransactionAudits", x => x.ID);
                    table.ForeignKey(
                        name: "FK_TransactionAudits_Kiosks_KioskID",
                        column: x => x.KioskID,
                        principalTable: "Kiosks",
                        principalColumn: "ID");
                });

            migrationBuilder.CreateTable(
                name: "AccountCardRestrictions",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FuelGrade = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AccountCardID = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccountCardRestrictions", x => x.ID);
                    table.ForeignKey(
                        name: "FK_AccountCardRestrictions_AccountCards_AccountCardID",
                        column: x => x.AccountCardID,
                        principalTable: "AccountCards",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AccountCardTransactions",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TerminalID = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    STAN = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RRN = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TransactionReferece = table.Column<long>(type: "bigint", nullable: false),
                    PAN = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Track2 = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    PIN = table.Column<byte[]>(type: "varbinary(max)", nullable: true),
                    TransactionDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    FinalizedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    AuthCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AmountAuthorized = table.Column<long>(type: "bigint", nullable: true),
                    AmountCaptured = table.Column<long>(type: "bigint", nullable: true),
                    AmountReversed = table.Column<long>(type: "bigint", nullable: true),
                    HostResponse = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    KioskID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AccountCardID = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccountCardTransactions", x => x.ID);
                    table.ForeignKey(
                        name: "FK_AccountCardTransactions_AccountCards_AccountCardID",
                        column: x => x.AccountCardID,
                        principalTable: "AccountCards",
                        principalColumn: "ID");
                });

            migrationBuilder.CreateTable(
                name: "RewardCardDiscounts",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FuelGrade = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Discount = table.Column<long>(type: "bigint", nullable: false),
                    Cap = table.Column<long>(type: "bigint", nullable: true),
                    AccountCardID = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RewardCardDiscounts", x => x.ID);
                    table.ForeignKey(
                        name: "FK_RewardCardDiscounts_AccountCards_AccountCardID",
                        column: x => x.AccountCardID,
                        principalTable: "AccountCards",
                        principalColumn: "ID");
                });

            migrationBuilder.CreateTable(
                name: "Transactions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TerminalTransactionId = table.Column<int>(type: "int", nullable: false),
                    TransactionDateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    AuthorisedAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    FinalAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    GSTAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Currency = table.Column<int>(type: "int", nullable: false),
                    ApplicationId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ApplicationLabel = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CardSignature = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CardExpiryDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CardATC = table.Column<int>(type: "int", nullable: true),
                    VehicleRegistration = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VehicleOdometer = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AccountType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TID = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    STAN = table.Column<int>(type: "int", nullable: true),
                    ResponseCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    HostResponse = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AuthorizationID = table.Column<int>(type: "int", nullable: true),
                    PanSeqNo = table.Column<int>(type: "int", nullable: true),
                    CVM = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Processor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TerminalId = table.Column<int>(type: "int", nullable: false),
                    StoreId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    KioskId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    UserId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FuelTransactionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AccountCardId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    UnitedExported = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Transactions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Transactions_AccountCards_AccountCardId",
                        column: x => x.AccountCardId,
                        principalTable: "AccountCards",
                        principalColumn: "ID");
                    table.ForeignKey(
                        name: "FK_Transactions_Accounts_AccountId",
                        column: x => x.AccountId,
                        principalTable: "Accounts",
                        principalColumn: "ID");
                    table.ForeignKey(
                        name: "FK_Transactions_FuelTransactions_FuelTransactionId",
                        column: x => x.FuelTransactionId,
                        principalTable: "FuelTransactions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Transactions_Kiosks_KioskId",
                        column: x => x.KioskId,
                        principalTable: "Kiosks",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Transactions_Stores_StoreId",
                        column: x => x.StoreId,
                        principalTable: "Stores",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RewardCardTransactions",
                columns: table => new
                {
                    ID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreatedDateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    TotalDiscountApplied = table.Column<long>(type: "bigint", nullable: false),
                    RewardCardDiscountID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    KioskId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TransactionID = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RewardCardTransactions", x => x.ID);
                    table.ForeignKey(
                        name: "FK_RewardCardTransactions_Kiosks_KioskId",
                        column: x => x.KioskId,
                        principalTable: "Kiosks",
                        principalColumn: "ID");
                    table.ForeignKey(
                        name: "FK_RewardCardTransactions_RewardCardDiscounts_RewardCardDiscountID",
                        column: x => x.RewardCardDiscountID,
                        principalTable: "RewardCardDiscounts",
                        principalColumn: "ID");
                    table.ForeignKey(
                        name: "FK_RewardCardTransactions_Transactions_TransactionID",
                        column: x => x.TransactionID,
                        principalTable: "Transactions",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_AccountBalances_AccountId",
                table: "AccountBalances",
                column: "AccountId");

            migrationBuilder.CreateIndex(
                name: "IX_AccountCardRestrictions_AccountCardID",
                table: "AccountCardRestrictions",
                column: "AccountCardID");

            migrationBuilder.CreateIndex(
                name: "IX_AccountCards_AccountId",
                table: "AccountCards",
                column: "AccountId");

            migrationBuilder.CreateIndex(
                name: "IX_AccountCards_StoreID",
                table: "AccountCards",
                column: "StoreID");

            migrationBuilder.CreateIndex(
                name: "IX_AccountCards_Type",
                table: "AccountCards",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_AccountCardTransactions_AccountCardID",
                table: "AccountCardTransactions",
                column: "AccountCardID");

            migrationBuilder.CreateIndex(
                name: "IX_Accounts_StoreID",
                table: "Accounts",
                column: "StoreID");

            migrationBuilder.CreateIndex(
                name: "IX_AccountVehicleRegistrations_AccountId",
                table: "AccountVehicleRegistrations",
                column: "AccountId");

            migrationBuilder.CreateIndex(
                name: "IX_AccountVehicleRegistrations_VehicleRegistrationId",
                table: "AccountVehicleRegistrations",
                column: "VehicleRegistrationId");

            migrationBuilder.CreateIndex(
                name: "IX_APIKeys_StoreID",
                table: "APIKeys",
                column: "StoreID");

            migrationBuilder.CreateIndex(
                name: "IX_KioskConfigs_KioskID",
                table: "KioskConfigs",
                column: "KioskID");

            migrationBuilder.CreateIndex(
                name: "IX_KioskInfos_KioskID",
                table: "KioskInfos",
                column: "KioskID",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Kiosks_StoreID",
                table: "Kiosks",
                column: "StoreID");

            migrationBuilder.CreateIndex(
                name: "IX_RewardCardDiscounts_AccountCardID",
                table: "RewardCardDiscounts",
                column: "AccountCardID");

            migrationBuilder.CreateIndex(
                name: "IX_RewardCardTransactions_KioskId",
                table: "RewardCardTransactions",
                column: "KioskId");

            migrationBuilder.CreateIndex(
                name: "IX_RewardCardTransactions_RewardCardDiscountID",
                table: "RewardCardTransactions",
                column: "RewardCardDiscountID");

            migrationBuilder.CreateIndex(
                name: "IX_RewardCardTransactions_TransactionID",
                table: "RewardCardTransactions",
                column: "TransactionID");

            migrationBuilder.CreateIndex(
                name: "IX_TransactionAudits_KioskID",
                table: "TransactionAudits",
                column: "KioskID");

            migrationBuilder.CreateIndex(
                name: "IX_Transactions_AccountCardId",
                table: "Transactions",
                column: "AccountCardId");

            migrationBuilder.CreateIndex(
                name: "IX_Transactions_AccountId",
                table: "Transactions",
                column: "AccountId");

            migrationBuilder.CreateIndex(
                name: "IX_Transactions_FuelTransactionId",
                table: "Transactions",
                column: "FuelTransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_Transactions_KioskId",
                table: "Transactions",
                column: "KioskId");

            migrationBuilder.CreateIndex(
                name: "IX_Transactions_StoreId",
                table: "Transactions",
                column: "StoreId");

            migrationBuilder.CreateIndex(
                name: "IX_UserAccounts_AccountID",
                table: "UserAccounts",
                column: "AccountID");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AccountBalances");

            migrationBuilder.DropTable(
                name: "AccountCardRestrictions");

            migrationBuilder.DropTable(
                name: "AccountCardTransactions");

            migrationBuilder.DropTable(
                name: "AccountDiscounts");

            migrationBuilder.DropTable(
                name: "AccountJournals");

            migrationBuilder.DropTable(
                name: "AccountVehicleRegistrations");

            migrationBuilder.DropTable(
                name: "APIKeys");

            migrationBuilder.DropTable(
                name: "KioskConfigs");

            migrationBuilder.DropTable(
                name: "KioskInfos");

            migrationBuilder.DropTable(
                name: "ParameterConfigs");

            migrationBuilder.DropTable(
                name: "RewardCardTransactions");

            migrationBuilder.DropTable(
                name: "TransactionAudits");

            migrationBuilder.DropTable(
                name: "UserAccounts");

            migrationBuilder.DropTable(
                name: "VehicleRegistrations");

            migrationBuilder.DropTable(
                name: "RewardCardDiscounts");

            migrationBuilder.DropTable(
                name: "Transactions");

            migrationBuilder.DropTable(
                name: "AccountCards");

            migrationBuilder.DropTable(
                name: "FuelTransactions");

            migrationBuilder.DropTable(
                name: "Kiosks");

            migrationBuilder.DropTable(
                name: "AccountCardTypes");

            migrationBuilder.DropTable(
                name: "Accounts");

            migrationBuilder.DropTable(
                name: "Stores");
        }
    }
}
