﻿using AutoMapper;
using ElmahCore;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.Specifications;
using OPT.Application.ViewModels.AccountCard;
using OPT.Application.ViewModels.RewardCard;
using OPT.Application.ViewModels.Store;
using OPT.Application.ViewModels.User;
using OPT.Domain.Exceptions;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Interfaces.UoW;
using OPT.Domain.Models;
using OPT.Domain.Specification;
using System.Data;
using System.Data.SqlClient;

namespace OPT.Application
{
    public class AccountCardAppService : ApplicationService, IAccountCardAppService
    {
        private readonly IAccountCardService _service;
        private readonly IIdentityService _userService;
        private readonly IKioskService _kioskService;
        private readonly IAccountCardTransactionService _accountCardTransactionService;
        private readonly IAccountService _accountService;
        private readonly IVehicleRegistrationService _vehicleService;
        private readonly IRewardCardTransactionService _rewardCardTransactionService;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AccountCardAppService(IUnitOfWork unitOfWork,
                                IMapper mapper,
                                IConfiguration configuration,
                                IHttpContextAccessor httpContextAccessor,
                                IAccountCardService service,
                                IIdentityService userService,
                                IKioskService kioskService,
                                IAccountCardTransactionService accountCardTransactionService,
                                IAccountService accountService,
                                IVehicleRegistrationService vehicleService,
                                IRewardCardTransactionService rewardCardTransactionService) : base(unitOfWork)
        {
            _service = service;
            _mapper = mapper;
            _configuration = configuration;
            _userService = userService;
            _kioskService = kioskService;
            _accountCardTransactionService = accountCardTransactionService;
            _accountService = accountService;
            _vehicleService = vehicleService;
            _httpContextAccessor = httpContextAccessor;
            _rewardCardTransactionService = rewardCardTransactionService;
        }

        public void Add(AccountCardInternalAddViewModel card)
        {
            var specification = new AccountCardInternalAddedSpecification(_service);
            if (specification.IsValid(card))
            {
                _service.Add(_mapper.Map<AccountCard>(card));
                Commit();
            }
        }
        
        public Guid? Add(AccountCardAddViewModel card, ApiKey apiKey)
        {
            var account = _accountService.GetById(card.AccountId);
            if (new AccountValidSpecification(apiKey).IsValid(account))
            {
                var specification = new AccountCardAddedSpecification(_service);
                if (specification.IsValid(card))
                {
                    var ac = _mapper.Map<AccountCard>(card);
                    _service.Add(ac);
                    Commit();
                    return ac.ID;
                }
            }
            return null;
        }

        public void Add(List<AccountCardAddListViewModel> cards)
        {
            var specification = new AccountCardInternalAddedSpecification(_service);
            var specs = cards.Select(x => specification.IsValid(x, false));
            if(specs.All(x => x))
            {
                var cardModels = _mapper.Map<List<AccountCard>>(cards);
                foreach(var card in cardModels)
                {
                    var c = cards.First(x => x.PAN == card.PAN);
                    if (!string.IsNullOrEmpty(c.UserPhoneNumber))
                    {
                        var user = _userService.GetByPhoneNumber(c.UserPhoneNumber).Result;
                        if (user != null)
                        {
                            card.UserId = user.Id.ToString();
                            card.Active = true;
                        }
                    }
                }

                _service.Add(cardModels);
                Commit();

                foreach(var card in cardModels)
                {
                    var c = cards.First(x => x.PAN == card.PAN);
                    if (!string.IsNullOrEmpty(c.VehicleRegistration) && c.AccountId != null)
                    {
                        var vehicle = _vehicleService.GetByRegistrationNumber(c.VehicleRegistration);
                        if(vehicle == null)
                        {
                            _vehicleService.Add(new VehicleRegistration { RegistrationNumber = c.VehicleRegistration }, c.AccountId.Value);
                        }
                    }
                }
                Commit();
            }else
            {
                throw new BusinessException(string.Join("\n", specs.Where(x => !string.IsNullOrWhiteSpace(x)).Select(x => (string)x)));
            }
        }
        
        public void Add(List<AccountCardAddViewModel> cards, ApiKey apiKey)
        {
            var accountIds = cards.GroupBy(x => x.AccountId).Select(x => x.First().AccountId);
            var accounts = new List<Account>();
            foreach (var accId in accountIds)
            {
                var account = _accountService.GetById(accId);
                new AccountValidSpecification(apiKey).IsValid(account);
            }

            var specification = new AccountCardAddedSpecification(_service);
            var specs = cards.Select(x => specification.IsValid(x, false));
            if(specs.All(x => x))
            {
                var cardModels = _mapper.Map<List<AccountCard>>(cards);

                _service.Add(cardModels);
                Commit();
            }else
            {
                throw new BusinessException(string.Join("\n", specs.Select(x => (string)x)), 412);
            }
        }

        public Guid? Add(RewardCardAddViewModel card, ApiKey apiKey)
        {
            var specification = new RewardCardAddedSpecification(_service);
            if (specification.IsValid(card))
            {
                var rc = _mapper.Map<AccountCard>(card);
                _service.Add(rc, apiKey.StoreID);
                Commit();
                return rc.ID;
            }
            return null;
        }

        public void Update(Guid id, RewardCardUpdateViewModel card, ApiKey apiKey)
        {
            if (card.IsValid<RewardCardUpdateViewModel, RewardCardUpdatedSpecification>())
            {
                _service.Update(id, _mapper.Map<AccountCard>(card), apiKey.StoreID);
                Commit();
            }
        }

        public void Add(List<RewardCardAddViewModel> cards, ApiKey apiKey)
        {
            var specification = new RewardCardAddedSpecification(_service);
            var specs = cards.Select(x => specification.IsValid(x, false));
            if (specs.All(x => x))
            {
                var cardModels = _mapper.Map<List<AccountCard>>(cards);
                _service.Add(cardModels, apiKey.StoreID);
                Commit();
            }
            else
            {
                throw new BusinessException(string.Join("\n", specs.Select(x => (string)x).Where(x => !string.IsNullOrWhiteSpace(x))));
            }
        }

        /*public void Import(UnitedAccountCardFileViewModel card, Dictionary<string, Guid> accounts)
        {
            //var account = _accountService.GetByCode(card.AccountCode);

            if(accounts.ContainsKey(card.AccountCode) != null)
            {
                var account = accounts[card.AccountCode];
                var newCard = _mapper.Map<AccountCard>(card);
                newCard.AccountId = account;

                //if (card.User != null)
                //{
                //    Guid? userId = null;
                //    User? user = null;
                //    if (!string.IsNullOrEmpty(card.User.UserName))
                //        user = _userService.GetByEmail(card.User.UserName).Result;

                //    if (user == null && !string.IsNullOrEmpty(card.User.PhoneNumber))
                //        user = _userService.GetByPhoneNumber(card.User.PhoneNumber).Result;

                //    if (user == null)
                //    {
                //        card.User.FirstName = "User" + DateTime.Now.ToString("yyMMddHHmmssfff");
                //        card.User.LastName = account.Name.Split(" ")[0];
                //        card.User.Role = Role.User;
                //        if (string.IsNullOrEmpty(card.User.UserName))
                //            card.User.UserName = card.User.FirstName + "@changeemail.com";
                //        var companyCode = RandomHelper.RandomString(6);
                //        var useradd = _mapper.Map<User>(card.User);
                //        useradd.Active = true;
                //        useradd.CompanyHash = CryptographyHelper.Encrypt(companyCode);
                //        userId = _userService.Add(useradd).Result;
                //        if (userId != null)
                //        {
                //            var userAccount = _mapper.Map<UserAccount>(card.User);
                //            userAccount.AccountID = account.ID;
                //            _accountService.AddOrUpdateUser(userId.Value, userAccount);
                //            Commit();
                //        }
                //    }
                //    else
                //        userId = user.Id;

                //    if (userId != null)
                //        newCard.UserId = userId.ToString();
                //}

                var local = _service.GetByPANDiscounts(newCard.PAN);
                if (local != null)
                {
                    newCard.ID = local.ID;

                    if(newCard.Discounts != null && local.Discounts != null)
                    {
                        newCard.Discounts.All(x =>
                        {
                            x.AccountCardID = local.ID;
                            x.ID = local.Discounts.FirstOrDefault(y => y.FuelGrade == x.FuelGrade)?.ID ?? x.ID;
                            return true;
                        });
                    }

                    _service.Update(local, newCard);
                }
                else
                {
                    newCard.ID = Guid.NewGuid();
                    _service.Add(newCard);
                }

                Commit();
            }
        }*/

        public void Import(UnitedAccountCardFileViewModel card)
        {
            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("PAN", typeof(string));
            dataTable.Columns.Add("Type", typeof(char));
            dataTable.Columns.Add("AccountCode", typeof(string));
            dataTable.Columns.Add("HolderName", typeof(string));
            dataTable.Columns.Add("Active", typeof(int));
            dataTable.Columns.Add("Status", typeof(string));
            dataTable.Columns.Add("VehicleOdometer", typeof(string));
            dataTable.Columns.Add("BarCode", typeof(string));
            dataTable.Columns.Add("ExpiryDate", typeof(string));
            dataTable.Columns.Add("RestrictionData", typeof(string));
            dataTable.Columns.Add("DiscountData", typeof(string));
            dataTable.Columns.Add("PIN", typeof(string));

            while (card.ReadNew())
            {
                dataTable.Rows.Add(card.PAN
                                  , card.Type
                                  , card.AccountCode
                                  , card.HolderName
                                  , card.Active ? 1 : 0
                                  , card.Status
                                  , card.VehicleOdometer
                                  , card.BarCode
                                  , card.ExpiryDate
                                  , (card.Active && card.Restrictions != null) ? string.Join("|", card.Restrictions.Select(x => x.FuelGrade)) : null
                                  , (card.Active && card.Discounts != null) ? string.Join("|", card.Discounts.Select(x => x.Discount)) : null
                                  , (card.Active && !string.IsNullOrWhiteSpace(card.PIN)) ? card.PIN : null);
            }

            using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                using (var clearCommand = new SqlCommand("TRUNCATE TABLE AccountCardsTemp", connection))
                {
                    clearCommand.CommandTimeout = int.MaxValue;
                    clearCommand.ExecuteNonQuery();
                }

                using (var bulkCopy = new SqlBulkCopy(connection))
                {
                    bulkCopy.BulkCopyTimeout = 0;
                    bulkCopy.DestinationTableName = "AccountCardsTemp";
                    bulkCopy.WriteToServer(dataTable);
                }

                using (SqlCommand cmd = new SqlCommand("MERGE INTO [dbo].[AccountCards] AS Target " +
                    "USING (SELECT t.*, a.ID as AccountId " +
                    "       FROM AccountCardsTemp t" +
                    "       INNER JOIN Accounts a" +
                    "       ON t.AccountCode = a.Code) " +
                    "AS Source ON Target.PAN = Source.PAN " +
                    "WHEN MATCHED THEN " +
                    "UPDATE SET Target.Type = Source.Type, " +
                    "Target.HolderName = Source.HolderName, " +
                    "Target.AccountId = Source.AccountId, " +
                    "Target.PIN = Source.PIN, " +
                    "Target.Active = Source.Active, " +
                    "Target.Status = Source.Status, " +
                    "Target.VehicleOdometer = Source.VehicleOdometer, " +
                    "Target.BarCode = Source.BarCode, " +
                    "Target.ExpiryDate = Source.ExpiryDate " +
                    "WHEN NOT MATCHED THEN " +
                    "INSERT (PAN, Type, HolderName, AccountId, PIN, Active, Status, VehicleOdometer, BarCode, ExpiryDate) " +
                    "VALUES (Source.PAN, Source.Type, Source.HolderName, Source.AccountId, Source.PIN, Active, Source.Status, Source.VehicleOdometer, Source.BarCode, Source.ExpiryDate);"
                    , connection))
                {
                    cmd.CommandTimeout = int.MaxValue;
                    cmd.ExecuteNonQuery();
                }
                
                using (SqlCommand cmd = new SqlCommand("delete from AccountCardRestrictions " +
                    "where AccountCardID in (select ID from AccountCards " +
                        "inner join AccountCardsTemp " +
                        "on AccountCardsTemp.PAN = AccountCards.PAN " +
                        "where AccountCards.Active = 1);"
                    , connection))
                {
                    cmd.CommandTimeout = int.MaxValue;
                    cmd.ExecuteNonQuery();
                }

                using (SqlCommand cmd = new SqlCommand("INSERT INTO AccountCardRestrictions (FuelGrade, AccountCardId) " +
                    "SELECT " +
                    "    value AS FuelGrade, " +
                    "    ac.ID as AccountCardId " +
                    "FROM AccountCardsTemp act " +
                    "inner join AccountCards ac " +
                    "on act.PAN = ac.PAN " +
                    "CROSS APPLY " +
                    "    STRING_SPLIT(RestrictionData, '|') " +
                    "where ac.Active = 1 and RestrictionData is not null; "
                    , connection))
                {
                    cmd.CommandTimeout = int.MaxValue;
                    cmd.ExecuteNonQuery();
                }
                
                using (SqlCommand cmd = new SqlCommand("MERGE INTO [dbo].[RewardCardDiscounts] AS Target " +
                    "USING (SELECT " +
                    "            CONCAT('0', ROW_NUMBER() OVER (PARTITION BY act.PAN ORDER BY (SELECT NULL))) AS FuelGrade, " +
                    "            value AS Discount, " +
                    "            150 as Cap, " +
                    "            ac.ID as AccountCardId " +
                    "        FROM AccountCardsTemp act " +
                    "        inner join AccountCards ac " +
                    "        on act.PAN = ac.PAN " +
                    "        CROSS APPLY  " +
                    "            STRING_SPLIT(DiscountData, '|') " +
                    "        where ac.Active = 1 and DiscountData is not null) " +
                    "AS Source ON Target.AccountCardId = Source.AccountCardId and Target.FuelGrade = Source.FuelGrade " +
                    "WHEN MATCHED THEN " +
                    "UPDATE SET Target.Discount = Source.Discount, " +
                    "Target.Cap = Source.Cap " +
                    "WHEN NOT MATCHED THEN " +
                    "INSERT (FuelGrade, Discount, Cap, AccountCardId) " +
                    "VALUES (Source.FuelGrade, Source.Discount, Source.Cap, Source.AccountCardId);"
                    , connection))
                {
                    cmd.CommandTimeout = int.MaxValue;
                    cmd.ExecuteNonQuery();
                }
            }
        }

        public void AssociateWithUser(Guid cardId, Guid? userId)
        {
            //var pin = RandomHelper.RandomNumber(4);
            //if (_service.AssociateWithUser(cardId, userId, CryptographyHelper.EncryptBytes(pin)))
            if (_service.AssociateWithUser(cardId, userId, null))
            {
                Commit();
                //TODO send PIN to user
            }
        }

        public AccountCardInternalViewModel? Get(Guid cardId)
        {
            var card = _service.GetById(cardId);
            if(card != null)
            {
                var acc = _mapper.Map<AccountCardInternalViewModel>(card);
                if (!string.IsNullOrEmpty(card.UserId))
                    acc.User = _mapper.Map<UserCardAccountViewModel>(_userService.GetById(new Guid(card.UserId)).Result);
                return acc;
            }
            return default;
        }
        
        public AccountCardViewModel? Get(Guid cardId, ApiKey apiKey)
        {
            return _mapper.Map<AccountCardViewModel>(_service.GetById(cardId, apiKey.StoreID));
        }

        public RewardCardViewModel? GetReward(Guid id, ApiKey apiKey)
        {
            var model = _service.GetById(id);
            if (new RewardCardPublicValidSpecification(apiKey).IsValid(model))
            {
                var rcvm = _mapper.Map<RewardCardViewModel>(model);
                rcvm.LoadDiscounts(model.Discounts);
                return rcvm;
            }
            return null;
        }

        public void Update(Guid id, AccountCardInternalUpdateViewModel viewModel)
        {
            var specification = new AccountCardInternalUpdatedSpecification(_service, id);
            if (specification.IsValid(viewModel))
            {
                _service.Update(id, _mapper.Map<AccountCard>(viewModel));
                Commit();
            }
        }
        
        public void Update(Guid id, AccountCardUpdateViewModel viewModel, ApiKey apiKey)
        {
            var account = _accountService.GetById(viewModel.AccountId);
            if (new AccountValidSpecification(apiKey).IsValid(account))
            {
                var specification = new AccountCardUpdatedSpecification(_service, id);
                if (specification.IsValid(viewModel))
                {
                    _service.Update(id, _mapper.Map<AccountCard>(viewModel), apiKey.StoreID);
                    Commit();
                }
            }
        }

        public void Delete(Guid cardId)
        {
            _service.Delete(cardId);
            Commit();
        }

        public void Delete(Guid id, ApiKey apiKey)
        {
            _service.Delete(id, apiKey.StoreID);
            Commit();
        }

        public List<AccountCardInternalViewModel> ListByAccount(Guid accountId)
        {
            var cards = _service.GetByAccount(accountId);
            if (cards != null && cards.Count() > 0)
            {
                var accs = _mapper.Map<List<AccountCardInternalViewModel>>(cards);
                for(int i = 0; i < cards.Count(); i++)
                {
                    if (!string.IsNullOrEmpty(cards[i].UserId))
                        accs[i].User = _mapper.Map<UserCardAccountViewModel>(_userService.GetById(new Guid(cards[i].UserId)).Result);
                }
                return accs;
            }
            return _mapper.Map<List<AccountCardInternalViewModel>>(cards);
        }
        
        public List<AccountCardViewModel> ListByAccount(Guid accountId, ApiKey apiKey)
        {
            var account = _accountService.GetById(accountId);
            if (new AccountValidSpecification(apiKey).IsValid(account))
            {
                var ac = _service.GetByAccount(accountId);
                ac = ac.Where(x => x.AccountCardType.Payment).ToList();
                return _mapper.Map<List<AccountCardViewModel>>(ac);
            }
            return null;
        }

        public List<AccountCardInternalViewModel> GetByStores(StoreFilterViewModel filter)
        {
            if (filter.StoresId != null && filter.StoresId.Any(x => x.Equals(new Guid("228C0C6C-A85F-4A4A-841A-35C1CFFE5CE3"))))
                throw new Exception("This functionality is currently unavailable, sorry for the inconvenience");
            if (filter.TenantsID != null && filter.TenantsID.Any(x => x.Equals(new Guid("C4BD99D6-55F9-49D3-A842-A444B059F038"))))
                throw new Exception("This functionality is currently unavailable, sorry for the inconvenience");
            return _mapper.Map<List<AccountCardInternalViewModel>>(_service.GetByStores(filter.StoresId, filter.TenantsID));
        }
        
        public AccountCardPaginationViewModel GetByStoresWithPagination(StoreFilterV2ViewModel filter)
        {
            (int total, var acccards) = _service.GetByStoresWithPagination(filter.StoresId, filter.TenantsID, filter.Offset, filter.Limit);
            return new AccountCardPaginationViewModel
            {
                TotalRecords = total,
                Offset = filter.Offset,
                PageRecords = acccards.Count,
                List = _mapper.Map<List<AccountCardInternalViewModel>>(acccards)
            };
        }

        public List<RewardCardItemViewModel> ListByStores(ApiKey apikey)
        {
            var rc = _service.GetByStores(new List<Guid> { apikey.StoreID }, null);
            rc = rc.Where(x => x.AccountCardType.Reward).ToList();
            return _mapper.Map<List<RewardCardItemViewModel>>(rc);
        }

        public List<RewardCardDiscountViewModel> Valid(RewardCardValidViewModel viewModel, Kiosk kiosk)
        {
            AccountCard? card = _service.GetByPANOrBarCode(viewModel.GetPANOrBarCode());
            if (card != null)
            {
                kiosk = _kioskService.GetById(kiosk.ID);
                var specification = new RewardCardValidSpecification(kiosk);
                if (specification.IsValid(card))
                {
                    return _mapper.Map<List<RewardCardDiscountViewModel>>(card.Discounts);
                }
            }
            else
            {
                throw new BusinessException("Reward card is not registered.");
            }

            return null;
        }

        public List<RewardCardTransactionInternalViewModel> GetTransactionsByDate(Guid rewardCardId, DateTime dateFrom, DateTime dateTo)
        {
            return _mapper.Map<List<RewardCardTransactionInternalViewModel>>(_rewardCardTransactionService.GetByDate(rewardCardId, dateFrom, dateTo));
        }

        public List<RewardCardTransactionViewModel> GetTransactionsByDate(Guid id, DateTime dateFrom, DateTime dateTo, ApiKey apiKey)
        {
            var model = _service.GetById(id);
            if (new RewardCardPublicValidSpecification(apiKey).IsValid(model))
            {
                return _mapper.Map<List<RewardCardTransactionViewModel>>(_rewardCardTransactionService.GetByDate(id, dateFrom, dateTo));
            }
            return null;
        }

        public string ResetPIN(Guid id)
        {
            var pin = RandomHelper.RandomNumber(4);
            AccountCard? card = _service.ResetPIN(id, pin);
            if (card != null)
            {
                Commit();
                //TODO the line below is temporary and should be deleted when the SMS is implemented
                return $"The new PIN for card number {card.PAN} is {pin}";
                //TODO send PIN to user
            }
            return "The PIN for this card cannot be reset.";
        }

        public AccountCardPINViewModel ResetPIN(Guid id, ApiKey apiKey)
        {
            var accountCard = _service.GetById(id, apiKey.StoreID);
            if (accountCard != null)
            {
                var pin = RandomHelper.RandomNumber(4);
                AccountCard? card = _service.ResetPIN(id, pin);
                if (card != null)
                {
                    Commit();
                    return new AccountCardPINViewModel { NewPIN = pin };
                }
            }
            return null;
        }

        public List<AccountCardTypeViewModel> GetAccountCardTypes()
        {
            return _mapper.Map<List<AccountCardTypeViewModel>>(_service.GetAccountCardTypes());
        }

        public List<AccountCardRestrictionViewModel> GetRestrictions(int authorizationID, int stan, string tid)
        {
            var accctrx = _accountCardTransactionService.GetByAuthCodeSTANAndTerminalID(authorizationID.ToString("D6"), Convert.ToInt64(stan), tid);
            if (accctrx != null && accctrx.AccountCardID != null)
            {
                return _mapper.Map<List<AccountCardRestrictionViewModel>>(_service.GetRestrictions(accctrx.AccountCardID.Value));
            }
            else
            {
                throw new BusinessException("Card does not exist.", 404);
            }
        }

        public AccountCardCapabilitiesViewModel GetCapabilities(int authorizationID, int stan, string tid)
        {
            var accctrx = _accountCardTransactionService.GetByAuthCodeSTANAndTerminalID(authorizationID.ToString("D6"), Convert.ToInt64(stan), tid);
            if (accctrx != null && accctrx.AccountCardID != null)
            {
                var card = _service.GetByIdExtended(accctrx.AccountCardID.Value);
                long cardOdometer;
                return new AccountCardCapabilitiesViewModel
                {
                    PromptOdometer = card?.Account?.FlagOdometer != 'N',
                    ValidateOdometer = card?.Account?.FlagOdometer == 'Y',
                    PromptVehicle = card?.Account?.AccountVehicleRegistrations != null && card.Account.AccountVehicleRegistrations.Count > 0,
                    ValidateVehicle = card?.Account?.AccountVehicleRegistrations != null && card.Account.AccountVehicleRegistrations.Count > 0,
                    VehicleOdometer = long.TryParse(card?.VehicleOdometer, out cardOdometer) ? cardOdometer : 0,
                    VehicleRegistrations = card?.Account?.AccountVehicleRegistrations.Where(x => x.VehicleRegistration.Active).Select(x => x.VehicleRegistration.RegistrationNumber).ToList() ?? new List<string>(),
                };
            }
            return null;
        }

        public string Authenticate(AccountCardAuthenticateViewModel model)
        {
            var kiosk = _kioskService.GetById(model.KioskId);
            if (kiosk != null && kiosk.Active)
            {
                return TokenHelper.GetToken(model, _configuration);
            }
            throw new BusinessException("Unable to authenticate.");
        }

        public AccountCardTransactionResponseViewModel Authorise(AccountCardAuthoriseViewModel model, AccountCardAuthenticateViewModel cardAuthenticate)
        {
            var result = new AccountCardTransactionResponseViewModel();
            var transaction = _mapper.Map<AccountCardTransaction>(model);
            try
            {
                //save transaction
                transaction.KioskID = cardAuthenticate.KioskId;
                transaction.TransactionDate = DateTime.Now;
                _accountCardTransactionService.Add(transaction);
                Commit();

                //validate infos
                AccountCard? card;
                if(model.PAN.Length == 19)
                    card = _service.GetByPAN19Extended(model.PAN);
                else
                    card = _service.GetByPANExtended(model.PAN);
                var kiosk = _kioskService.GetById(cardAuthenticate.KioskId);
                var specification = new AccountCardAuthorizeSpecification(card, kiosk, _userService);
                var validate = specification.IsValid(model, false);
                if (validate)
                {
                    //prepare object return
                    result.MerchantId = card.Account.StoreID.ToString();

                    //update transaction with authorization infos
                    transaction.AccountCardID = card.ID;
                    transaction.RRN = (model.TransactionReferece + "X" + transaction.STAN.ToString()).PadLeft(12, '0');
                    transaction.FinalizedDate = DateTime.Now;
                    transaction.AmountAuthorized = model.Amount;
                    transaction.AuthCode = RandomHelper.RandomNumber(6);
                    transaction.HostResponse = "00"; //Transaction Approved
                }
                else
                {
                    //update transaction with error infos
                    transaction.FinalizedDate = DateTime.Now;
                    transaction.HostResponse = validate;
                }
            }catch (Exception e)
            {
                _httpContextAccessor.HttpContext.RaiseError(e);
                transaction.FinalizedDate = DateTime.Now;
                transaction.HostResponse = "96"; //System Error
            }

            if (!transaction.ID.Equals(Guid.Empty))
            {
                _accountCardTransactionService.Update(transaction);
                Commit();
            }

            result.LoadTransaction(transaction);
            return result;
        }
        
        public AccountCardTransactionResponseViewModel Validate(AccountCardValidateViewModel model)
        {
            var result = new AccountCardTransactionResponseViewModel();
            AccountCardTransaction? transaction = null;
            try
            {
                //get transaction
                transaction = _accountCardTransactionService.GetBySTANAndTerminalID(model.STAN, model.TerminalID, model.TransactionReference);

                //validate infos
                var card = _service.GetByIdExtended(transaction.AccountCardID.Value);
                var specification = new AccountCardValidateSpecification(card);
                var validate = specification.IsValid(model, false);
                if (validate)
                {
                    transaction.HostResponse = "00"; //Transaction Approved
                }
                else
                {
                    //update transaction with error infos
                    transaction.FinalizedDate = DateTime.Now;
                    transaction.HostResponse = validate;
                }

                _accountCardTransactionService.Update(transaction);
                Commit();
            }
            catch (Exception e)
            {
                _httpContextAccessor.HttpContext.RaiseError(e);
                transaction.FinalizedDate = DateTime.Now;
                transaction.HostResponse = "96"; //System Error
            }

            result.LoadTransaction(transaction);
            return result;
        }

        public AccountCardTransactionResponseViewModel Capture(AccountCardTransactionViewModel model, AccountCardAuthenticateViewModel cardAuthenticate)
        {
            var result = new AccountCardTransactionResponseViewModel();
            AccountCardTransaction? transaction = null;
            try
            {
                //get transaction
                transaction = _accountCardTransactionService.GetBySTANAndTerminalID(model.STAN, model.TerminalID, model.TransactionReferece);

                if(transaction != null && transaction.AmountCaptured != null)
                {
                    result.HostResponse = "00"; //Transaction Approved
                    return result;
                }

                //validate infos
                var specification = new AccountCardCaptureSpecification(transaction);
                var validate = specification.IsValid(model, false);
                if (validate)
                {
                    //update transaction with capture infos
                    transaction.FinalizedDate = DateTime.Now;
                    transaction.AmountCaptured = model.Amount;
                    transaction.HostResponse = "00"; //Transaction Approved

                    //Update balance
                    var card = _service.GetById(transaction.AccountCardID.Value);
                    _accountService.UpdateBalance(card.AccountId.Value, -model.Amount/100.0m);
                }
                else
                {
                    //update transaction with error infos
                    if (transaction != null)
                    {
                        transaction.FinalizedDate = DateTime.Now;
                        transaction.HostResponse = validate;
                    }
                    result.HostResponse = validate;
                }
            }
            catch (Exception e)
            {
                _httpContextAccessor.HttpContext.RaiseError(e);
                if (transaction != null)
                {
                    transaction.FinalizedDate = DateTime.Now;
                    transaction.HostResponse = "96"; //System Error
                }
                result.HostResponse = "96";
            }

            if (transaction != null)
            {
                _accountCardTransactionService.Update(transaction);
                Commit();
                result.LoadTransaction(transaction);
            }
            return result;
        }

        public AccountCardTransactionResponseViewModel Reverse(AccountCardTransactionViewModel model, AccountCardAuthenticateViewModel cardAuthenticate)
        {
            var result = new AccountCardTransactionResponseViewModel();
            AccountCardTransaction? transaction = null;
            try
            {
                //get transaction
                transaction = _accountCardTransactionService.GetBySTANAndTerminalID(model.STAN, model.TerminalID, model.TransactionReferece);

                //validate infos
                var specification = new AccountCardCaptureSpecification(transaction);
                var validate = specification.IsValid(model, false);
                if (validate)
                {
                    //update transaction with capture infos
                    transaction.FinalizedDate = DateTime.Now;
                    transaction.AmountReversed = model.Amount;
                    transaction.HostResponse = "00"; //Transaction Approved
                }
                else
                {
                    //update transaction with error infos
                    if (transaction != null)
                    {
                        transaction.FinalizedDate = DateTime.Now;
                        transaction.HostResponse = validate;
                    }
                    result.HostResponse = validate;
                }
            }
            catch (Exception e)
            {
                _httpContextAccessor.HttpContext.RaiseError(e);
                if (transaction != null)
                {
                    transaction.FinalizedDate = DateTime.Now;
                    transaction.HostResponse = "96"; //System Error
                }
                result.HostResponse = "96";
            }

            if (transaction != null)
            {
                _accountCardTransactionService.Update(transaction);
                Commit();
                result.LoadTransaction(transaction);
            }
            return result;
        }

        public void Dispose()
        {
            _service.Dispose();
            _userService.Dispose();
            _kioskService.Dispose();
            _accountCardTransactionService.Dispose();
            _rewardCardTransactionService.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
