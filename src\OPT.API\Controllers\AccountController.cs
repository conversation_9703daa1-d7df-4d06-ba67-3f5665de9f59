﻿using Microsoft.AspNetCore.Mvc;
using OPT.API.Attributes;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.Account;
using OPT.Application.ViewModels.Store;
using Swashbuckle.AspNetCore.Annotations;

namespace OPT.API.Controllers
{
    /// <summary>
    /// An Account represents a business entity that owns a fleet of vehicles and has a partnership with a fuel store. This entity serves as a central hub for managing users, AccountCards (payment cards), and Vehicles. Additionally, it has access to the Balance, which acts as a transaction ledger where payments made within the account are recorded. The Balance also allows for the application of a journal to settle outstanding payments.
    /// 
    /// ### Functionality
    /// * User Management: The Account allows for the addition, modification, and removal of users associated with the account.
    /// * AccountCard Management: The Account provides tools to manage AccountCards, including the ability to activate or deactivate cards, and set spending limits.
    /// * Vehicle Registration Management: Detailed information about each vehicle in the fleet is accessible through the Account. This includes the ability to add new vehicles, update existing details, and retire vehicles from service.
    /// * Balance Oversight: The Balance provides a clear overview of financial activities within the Account.This includes the ability to view individual transactions, track outstanding payments, and apply journals for reconciliation purposes.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerControllerOrder(2)]
    public class AccountController : ControllerBase
    {
        private readonly IAccountAppService _appService;

        /// <summary>
        /// AccountController Constructor
        /// </summary>
        /// <param name="appService"></param>
        public AccountController(IAccountAppService appService)
        {
            _appService = appService;
        }

        /// <summary>
        /// Add or update Account
        /// </summary>
        /// <returns></returns>
        [ApiExplorerSettings(GroupName = "v1")]
        [Authorize(Role.POSMaster)]
        [HttpPost]
        public ResponseViewModel Add(AccountInternalViewModel account)
        {
            _appService.Add(account);
            return ResponseViewModel.Ok($"Account {account.Name} added with success.");
        }

        /// <summary>
        /// Get Account By ID
        /// </summary>
        /// <returns></returns>
        [ApiExplorerSettings(GroupName = "v1")]
        [Authorize(Role.POSMaster)]
        [HttpGet("{id}")]
        public AccountInternalViewModel Get(Guid id)
        {
            return _appService.GetById(id);
        }

        /// <summary>
        /// list Accounts by stores
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("stores")]
        [ApiExplorerSettings(GroupName = "v1")]
        public List<AccountInternalViewModel> GetByStores(StoreFilterViewModel storeFilter)
        {
            return _appService.GetByStores(storeFilter);
        }
        
        /// <summary>
        /// list Accounts by stores
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("v2/stores")]
        [ApiExplorerSettings(GroupName = "v1")]
        public AccountPaginationViewModel GetByStoresWithPagination(StoreFilterV2ViewModel storeFilter)
        {
            return _appService.GetByStoresWithPagination(storeFilter);
        }

        /// <summary>
        /// Get Account Balance
        /// </summary>
        /// <param name="accountid"></param>
        /// <returns></returns>
        [ApiExplorerSettings(GroupName = "v1")]
        [Authorize(Role.POSMaster)]
        [HttpGet("{accountid}/balance/{datefrom}/{dateto}")]
        public AccountBalanceViewModel GetBalance(Guid accountid, DateOnly datefrom, DateOnly dateto)
        {
            return _appService.GetBalance(accountid, datefrom.ToDateTime(TimeOnly.MinValue), dateto.ToDateTime(TimeOnly.MinValue));
        }

        /// <summary>
        /// Add new journal on account
        /// </summary>
        /// <param name="accountJournal"></param>
        [ApiExplorerSettings(GroupName = "v1")]
        [Authorize(Role.POSMaster)]
        [HttpPost("journal")]
        public ResponseViewModel addJournal(AccountJournalViewModel accountJournal)
        {
            _appService.addJournal(accountJournal);
            return ResponseViewModel.Ok($"Journal added with success.");
        }

        #region Exposed APIs

        [SwaggerOperation(
            Summary = "Add new Account",
            Description = "This endpoint allows the creation of a new account associated with a company that owns a fleet of vehicles and has a partnership with a fuel station."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the succesfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpPost("v1")]
        public ResponseViewModel AddPublic(
            [SwaggerRequestBody("Account object informations", Required = true)]
            AccountAddViewModel account)
        {
            var id = _appService.Add(account, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Account {account.Name} added with success.", id);
        }

        [SwaggerOperation(
            Summary = "Update Account",
            Description = "This endpoint allows for the update of information for an existing account based on the account ID."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the succesfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpPut("v1/{id}")]
        public ResponseViewModel UpdatePublic(
            [SwaggerParameter("Account ID", Required = true)]
            Guid id,
            [SwaggerRequestBody("Account object informations", Required = true)]
            AccountAddViewModel account)
        {
            _appService.Update(id, account, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Account {account.Name} updated with success.");
        }

        [SwaggerOperation(
            Summary = "Get Account By ID",
            Description = "This endpoint allows for get information for an existing account based on the account ID."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the account object.", typeof(AccountViewModel))]
        [Authorize(Role.API)]
        [HttpGet("v1/{id}")]
        public AccountViewModel GetPublic(
            [SwaggerParameter("Account ID", Required = true)]
            Guid id)
        {
            return _appService.GetById(id, HttpContext.GetApiKey());
        }

        [SwaggerOperation(
            Summary = "List Accounts",
            Description = "This endpoint allows for list all accounts."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return a list of accounts object.", typeof(List<AccountViewModel>))]
        [Authorize(Role.API)]
        [HttpGet("v1")]
        public List<AccountViewModel> GetAll()
        {
            return _appService.GetByStore(HttpContext.GetApiKey());
        }

        [SwaggerOperation(
            Summary = "Get Balance Account",
            Description = @"This endpoint allows you to retrieve the current balance and statement history for a specific account. The API also returns a list of statements by date."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the balance object.", typeof(AccountBalanceViewModel))]
        [Authorize(Role.API)]
        [HttpGet("v1/{accountid}/balance/{datefrom}/{dateto}")]
        public AccountBalanceViewModel GetBalancePublic(
            [SwaggerParameter("Account ID", Required = true)]
            Guid accountid,
            [SwaggerParameter("Starting date the list of statements that will be returned on the balance sheet. Format yyyy-MM-dd", Required = true)]
            DateOnly datefrom,
            [SwaggerParameter("End date the list of statements that will be returned on the balance sheet. Format yyyy-MM-dd", Required = true)]
            DateOnly dateto)
        {
            return _appService.GetBalance(accountid, datefrom.ToDateTime(TimeOnly.MinValue), dateto.ToDateTime(TimeOnly.MinValue), HttpContext.GetApiKey());
        }

        [SwaggerOperation(
            Summary = "Add Journal",
            Description = @"The Journal API provides functionality for applying journal entries to the account's balance. A journal entry is a record of a financial transaction that helps in reconciling outstanding payments and credits.
### journalTypeId:
* 1 - Payment
* 2 - Positive Adjustment
* 3 - Negative Adjustment
* 4 - Fee"
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the balance object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpPost("v1/journal")]
        public ResponseViewModel addJournalPublic(
            [SwaggerRequestBody("Journal Account object informations", Required = true)]
            JournalViewModel accountJournal)
        {
            _appService.addJournal(accountJournal, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Journal added with success.");
        }

        #endregion

    }
}
