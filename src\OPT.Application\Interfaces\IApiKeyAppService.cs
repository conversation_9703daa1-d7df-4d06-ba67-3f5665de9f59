﻿using OPT.Application.ViewModels.ApiKey;

namespace OPT.Application.Interfaces
{
    public interface IApiKeyAppService : IDisposable
    {
        void Add(ApiKeyAddViewModel viewModel);

        string UpdateAccessToken(Guid id);

        void UpdateActive(Guid id, bool ative);

        List<ApiKeyViewModel> GetByStores(List<Guid> storesId);

        void Remove(Guid id);

        string Authenticate(ApiKeyAuthenticateViewModel model);
    }
}
