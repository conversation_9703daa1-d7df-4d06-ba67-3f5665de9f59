﻿using Microsoft.AspNetCore.Mvc;
using OPT.API.Attributes;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.Transaction;
using Swashbuckle.AspNetCore.Annotations;

namespace OPT.API.Controllers
{
    /// <summary>
    /// Transactions represent the recorded details of fueling activities conducted on OPT devices when a user completes a fuel purchase using either a bank card or an AccountCard.
    /// 
    /// ### Characteristics:
    /// 1. Record of Fueling Activity:
    ///    - Transactions serve as a comprehensive log capturing all relevant information regarding fuel purchases made through OPT devices.
    ///    
    /// 1. Payment Method:
    ///    - Each transaction specifies whether the payment was made using a bank card or an AccountCard, providing clarity on the payment source.
    ///    
    /// 1. Fueling Details:
    ///    - Transactions include critical information such as the amount of fuel dispensed, the type of fuel (FuelGrade), the unit price, and the total cost.
    ///    
    /// 1. Card Information:
    ///    - The card signature of the card used for payment are recorded to facilitate tracking and reference.
    ///    
    /// 1. Timestamp:
    ///    - Each transaction is timestamped, capturing the date and time of the fueling activity.
    ///    
    /// ### Functionality:
    /// - Comprehensive Recordkeeping:
    ///   - Transactions offer a detailed record of all fueling activities, ensuring transparency and accountability in the fueling process.
    /// - Payment Source Tracking:
    ///   - By specifying the payment method, transactions enable tracking of payment sources and methods used by users.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerControllerOrder(7)]
    public class TransactionController : ControllerBase
    {
        private readonly ITransactionAppService _appService;
        /// <summary>
        /// TransactionController Constructor
        /// </summary>
        /// <param name="appService"></param>
        public TransactionController(ITransactionAppService appService)
        {
            _appService = appService;
        }
        /// <summary>
        /// Add New Transaction
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Kiosk)]
        [HttpPost]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel Add(TransactionAddViewModel transaction)
        {
            _appService.Add(transaction);
            return ResponseViewModel.Ok($"Transaction added sucessfully.");
        }

        /// <summary>
        /// Get Transactions by UserId
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.User)]
        [HttpGet("user/{userid}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public List<TransactionUserViewModel> GetByUserId(string userid)
        {
            return _appService.GetByUserId(userid);
        }

        /// <summary>
        /// Get Transactions by AccountId. This is only available to users wtih the POSMaster role.
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("filter")]
        [ApiExplorerSettings(GroupName = "v1")]
        public async Task<List<TransactionAccountViewModel>> GetByFilter(TransactionFilterViewModel filter)
        {
            return await _appService.GetByFilter(filter);
        }
        
        /// <summary>
        /// Get Transactions by AccountId. This is only available to users wtih the POSMaster role.
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("v2/filter")]
        [ApiExplorerSettings(GroupName = "v1")]
        public async Task<TransactionPagination> GetByFilterWithPagination(TransactionFilterV2ViewModel filter)
        {
            return await _appService.GetByFilterWithPagination(filter);
        }

        /// <summary>
        /// Send Transaction Receipt
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Kiosk, Role.User)]
        [HttpGet("receipt/{transactionid}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public async Task<ResponseViewModel> SendTransactionReceipt(int transactionid)
        {
            await _appService.SendTransactionReceipt(transactionid);
            return ResponseViewModel.Ok($"Receipt has been sent sucessfully.");
        }
        
        /// <summary>
        /// Send Transaction Receipt
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Kiosk)]
        [HttpPost("receipt/email/{email}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public async Task<ResponseViewModel> SendTransactionReceipt(CardReceiptViewModel cardReceipt, string email)
        {
            await _appService.SendTransactionReceipt(cardReceipt, email);
            return ResponseViewModel.Ok($"Receipt has been sent sucessfully.");
        }

        #region Exposed APIs

        [SwaggerOperation(
            Summary = "Get Transactions By Filter",
            Description = "This API allows you to filter and list transactions based on specific criteria such as account, kiosk (OPT Device), and date range."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return a list of transactions that match the specified filter criteria.", typeof(List<TransactionAccountViewModel>))]
        [Authorize(Role.API)]
        [HttpPost("v1/filter")]
        public async Task<List<TransactionAccountViewModel>> GetByFilterPublic(
            [SwaggerRequestBody("Transaction filter object", Required = true)]
            TransactionFilterViewModel filter)
        {
            filter.StoresId = new List<Guid> { HttpContext.GetApiKey().StoreID };
            return await _appService.GetByFilter(filter);
        }

        #endregion
    }

}
