﻿using OPT.Domain.Models.Enumerations;

namespace OPT.Domain.Models
{
    public class AccountJournal
    {
        public Guid ID { get; set; }
        public DateTime JournalDate { get; set; }
        public Guid AccountId { get; set; }
        public JournalType JournalTypeId { get; set; }
        public decimal Amount { get; set; }
        public string POSMasterUserId { get; set; }

        public string? Reason { get; set; }
    }
}
