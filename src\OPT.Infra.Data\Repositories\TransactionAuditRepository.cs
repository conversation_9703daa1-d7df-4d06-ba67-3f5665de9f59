﻿using Microsoft.EntityFrameworkCore;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Models;
using OPT.Infra.Data.Context;

namespace OPT.Infra.Data.Repositories
{
    public class TransactionAuditRepository : Repository<TransactionAudit>, ITransactionAuditRepository
    {
        public TransactionAuditRepository(OPTContext context) : base(context)
        {
        }

        public List<List<TransactionAudit>> GetByFilter(Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId)
        {
            return _dbset
                .Include(x => x.Kiosk)
                .ThenInclude(x => x.Store)
                .AsNoTracking()
                .Where(x => (kioskId == null || x.KioskID == kioskId)
                    && (dateFrom == null || x.TransactionCreatedDate.Date >= dateFrom.Value.Date)
                    && (dateTo == null || x.TransactionCreatedDate.Date <= dateTo.Value.Date)
                    && (storesId == null || storesId.Contains(x.Kiosk.StoreID)))
                .GroupBy(x => new { x.KioskID, x.KioskTransactionNumber, x.TransactionCreatedDate })
                .OrderByDescending(x => x.First().TransactionCreatedDate)
                .Select(x => x.ToList())
                .ToList();
        }
        
        public (int, List<List<TransactionAudit>>) GetByFilterWithPagination(Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId, int offset, int limit)
        {
            var list = _dbset
                .Include(x => x.Kiosk)
                .ThenInclude(x => x.Store)
                .AsNoTracking()
                .Where(x => (kioskId == null || x.KioskID == kioskId)
                    && (dateFrom == null || x.TransactionCreatedDate.Date >= dateFrom.Value.Date)
                    && (dateTo == null || x.TransactionCreatedDate.Date <= dateTo.Value.Date)
                    && (storesId == null || storesId.Contains(x.Kiosk.StoreID)))
                .GroupBy(x => new { x.KioskID, x.KioskTransactionNumber, x.TransactionCreatedDate })
                .OrderByDescending(x => x.First().TransactionCreatedDate)
                .Select(x => x.ToList())
                .ToList();

            return (list.Count, list.Skip(offset).Take(limit).ToList());
        }
    }
}
