﻿using AutoMapper;
using OPT.Application.ViewModels.User;
using OPT.Application.ViewModels.Store;
using OPT.Application.ViewModels.Account;
using OPT.Domain.Models;
using OPT.Application.ViewModels.Kiosk;
using OPT.Application.ViewModels.Transaction;
using OPT.Application.ViewModels.FuelTransaction;
using OPT.Application.ViewModels.AccountCard;
using OPT.Application.ViewModels.VehicleRegistration;
using System.Text;
using OPT.Application.Helper;
using OPT.Application.ViewModels.TransactionAudit;
using OPT.Application.ViewModels.RewardCard;
using OPT.Application.ViewModels.ApiKey;

namespace OPT.Application.AutoMapper
{
    public class ViewModelToDomainMappingProfile : Profile
    {
        public ViewModelToDomainMappingProfile()
        {
            CreateMap<UserAddViewModel, User>();

            CreateMap<StoreViewModel, Store>();
            CreateMap<StoreAddressViewModel, Store>();

            CreateMap<AccountInternalViewModel, Account>().ForMember(x => x.StoreID, x => x.MapFrom(y => y.Store.ID));
            CreateMap<AccountViewModel, Account>();
            CreateMap<AccountAddViewModel, Account>();
            CreateMap<UnitedAccountFileViewModel, Account>();

            CreateMap<UserAddViewModel, UserAccount>().ForMember(x => x.AccountID, x => x.MapFrom(y => y.Account.ID));
            CreateMap<UserUpdateViewModel, UserAccount>().ForMember(x => x.AccountID, x => x.MapFrom(y => y.Account.ID)).ForMember(x => x.ID, x => x.Ignore());
            CreateMap<UserUpdateViewModel, User>();

            CreateMap<KioskAddViewModel, Kiosk>().ForMember(x => x.StoreID, x => x.MapFrom(y => y.Store.ID));
            CreateMap<KioskConfigViewModel, KioskConfig>();
            CreateMap<KioskInfoViewModel, KioskInfo>();
            CreateMap<KioskNameViewModel, KioskConfig>();

            CreateMap<TransactionAddViewModel, Transaction>().ForMember(x => x.StoreId, x => x.MapFrom(y => y.StoreId));
            CreateMap<FuelTransactionAddViewModel, FuelTransaction>();
            CreateMap<TransactionUserViewModel, Transaction>();
            CreateMap<TransactionViewModel, Transaction>(); 
            CreateMap<TransactionAccountViewModel, Transaction>();
            CreateMap<TransactionReceiptViewModel, Transaction>();

            CreateMap<AccountCardAddViewModel, AccountCard>()
                .ForMember(x => x.PIN, x => x.MapFrom(y => !string.IsNullOrWhiteSpace(y.PIN) ? y.PIN : null))
                .ForMember(x => x.Type, x => x.MapFrom(y => "A"));
            CreateMap<AccountCardInternalAddViewModel, AccountCard>()
                .ForMember(x => x.PIN, x => x.MapFrom(y => !string.IsNullOrWhiteSpace(y.PIN) ? y.PIN : null));
            CreateMap<AccountCardAddListViewModel, AccountCard>()
                .ForMember(x => x.PIN, x => x.MapFrom(y => !string.IsNullOrWhiteSpace(y.PIN) ? y.PIN : null));
            CreateMap<AccountCardUpdateViewModel, AccountCard>()
                .ForMember(x => x.Type, x => x.MapFrom(y => "A"));
            CreateMap<AccountCardInternalUpdateViewModel, AccountCard>();
            CreateMap<AccountCardAuthoriseViewModel, AccountCardTransaction>()
                .ForMember(x => x.PIN, x => x.MapFrom(y => y.PIN != null ? ConvertHelper.HexStringToByteArray(y.PIN) : null));
            CreateMap<VehicleRegistrationAddViewModel, VehicleRegistration>();
            CreateMap<VehicleRegistrationViewModel, VehicleRegistration>();
            CreateMap<AccountJournalViewModel, AccountJournal>();
            CreateMap<JournalViewModel, AccountJournal>();
            CreateMap<TransactionAuditAddViewModel, TransactionAudit>();
            CreateMap<AccountCardRestrictionViewModel, AccountCardRestriction>();
            CreateMap<UnitedAccountCardFileViewModel, AccountCard>().ForMember(x => x.PIN, x => x.MapFrom(y => !string.IsNullOrWhiteSpace(y.PIN) ? y.PIN : null));

            CreateMap<RewardCardAddViewModel, AccountCard>()
                .ForMember(x => x.Discounts, x=> x.MapFrom(y => y.getDiscounts()))
                .ForMember(x => x.Type, x => x.MapFrom(y => "B"));
            CreateMap<RewardCardUpdateViewModel, AccountCard>()
                .ForMember(x => x.Discounts, x=> x.MapFrom(y => y.getDiscounts()))
                .ForMember(x => x.Type, x => x.MapFrom(y => "B"));
            CreateMap<RewardCardTransactionAddViewModel, RewardCardTransaction>()
                .ForMember(x => x.ID, x => x.Ignore())
                .ForMember(x => x.RewardCardDiscountID, x => x.MapFrom(x => x.ID));
            CreateMap<RewardCardDiscountViewModel, RewardCardDiscount>();

            CreateMap<ApiKeyAddViewModel, ApiKey>().ForMember(x => x.StoreID, x => x.MapFrom(y => y.Store.ID));
        }
    }
}
