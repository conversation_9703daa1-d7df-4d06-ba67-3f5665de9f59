﻿using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;

namespace OPT.Domain.Services
{
    public class RewardCardTransactionService : IRewardCardTransactionService
    {
        private readonly IRewardCardTransactionRepository _repository;

        public RewardCardTransactionService(IRewardCardTransactionRepository repository)
        {
            _repository = repository;
        }

        public List<RewardCardTransaction> GetByDate(Guid rewardCardId, DateTime dateFrom, DateTime dateTo)
        {
            return _repository.GetByDate(rewardCardId, dateFrom, dateTo);
        }

        public void Dispose()
        {
            _repository.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
