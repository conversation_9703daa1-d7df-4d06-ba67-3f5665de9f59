﻿namespace OPT.Application.ViewModels.ApiKey
{
    public class ApiKeyViewModel
    {
        public Guid ID { get; set; }

        public required string Name { get; set; }

        public string? Description { get; set; }

        public DateTime CreatedDateTime { get; set; }

        public bool Active { get; set; }

        public string? AccessToken { get; set; }
        public DateTime? TokenDateTime { get; set; }

        public string StoreName { get; set; }
    }
}
