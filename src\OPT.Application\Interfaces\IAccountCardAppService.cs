﻿using OPT.Application.ViewModels.AccountCard;
using OPT.Application.ViewModels.RewardCard;
using OPT.Application.ViewModels.Store;
using OPT.Domain.Models;

namespace OPT.Application.Interfaces
{
    public interface IAccountCardAppService : IDisposable
    {
        void Add(AccountCardInternalAddViewModel card);
        Guid? Add(AccountCardAddViewModel card, ApiKey apiKey);
        void Add(List<AccountCardAddListViewModel> cards);
        void Add(List<AccountCardAddViewModel> cards, ApiKey apiKey);

        Guid? Add(RewardCardAddViewModel card, ApiKey apiKey);
        void Update(Guid id, RewardCardUpdateViewModel card, ApiKey apiKey);
        void Add(List<RewardCardAddViewModel> cards, ApiKey apiKey);

        void Import(UnitedAccountCardFileViewModel card);

        List<AccountCardInternalViewModel> ListByAccount(Guid accountId);
        List<AccountCardViewModel> ListByAccount(Guid accountId, ApiKey apiKey);
        AccountCardInternalViewModel? Get(Guid cardId);
        AccountCardViewModel? Get(Guid cardId, ApiKey apiKey);

        RewardCardViewModel? GetReward(Guid id, ApiKey apiKey);

        void Update(Guid id, AccountCardInternalUpdateViewModel viewModel);
        void Update(Guid id, AccountCardUpdateViewModel viewModel, ApiKey apiKey);
        void Delete(Guid cardId);
        void Delete(Guid cardId, ApiKey apiKey);
        void AssociateWithUser(Guid cardId, Guid? userId);

        List<AccountCardInternalViewModel> GetByStores(StoreFilterViewModel filter);

        AccountCardPaginationViewModel GetByStoresWithPagination(StoreFilterV2ViewModel filter);
        List<RewardCardItemViewModel> ListByStores(ApiKey apikey);
        List<RewardCardDiscountViewModel> Valid(RewardCardValidViewModel viewModel, Kiosk kiosk);
        List<RewardCardTransactionInternalViewModel> GetTransactionsByDate(Guid rewardCardId, DateTime dateFrom, DateTime dateTo);
        List<RewardCardTransactionViewModel> GetTransactionsByDate(Guid id, DateTime dateFrom, DateTime dateTo, ApiKey apiKey);

        string ResetPIN(Guid cardId);
        AccountCardPINViewModel ResetPIN(Guid cardId, ApiKey apiKey);

        List<AccountCardTypeViewModel> GetAccountCardTypes();

        List<AccountCardRestrictionViewModel> GetRestrictions(int authorizationID, int stan, string tid);
        AccountCardCapabilitiesViewModel GetCapabilities(int authorizationID, int stan, string tid);

        string Authenticate(AccountCardAuthenticateViewModel model);
        AccountCardTransactionResponseViewModel Authorise(AccountCardAuthoriseViewModel model, AccountCardAuthenticateViewModel cardAuthenticate);
        AccountCardTransactionResponseViewModel Validate(AccountCardValidateViewModel model);
        AccountCardTransactionResponseViewModel Capture(AccountCardTransactionViewModel model, AccountCardAuthenticateViewModel cardAuthenticate);
        AccountCardTransactionResponseViewModel Reverse(AccountCardTransactionViewModel model, AccountCardAuthenticateViewModel cardAuthenticate);
    }
}
