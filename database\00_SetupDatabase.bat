@echo off
REM =============================================
REM OPTdb Complete Database Setup Script (Batch)
REM Description: Complete setup script that runs all database setup steps
REM =============================================

setlocal enabledelayedexpansion

REM Set default values
set ENVIRONMENT=Development
set SERVER_INSTANCE=(localdb)\MSSQLLocalDB
if not "%1"=="" set ENVIRONMENT=%1
if not "%2"=="" set SERVER_INSTANCE=%2

echo === OPTdb Complete Database Setup ===
echo Environment: %ENVIRONMENT%
echo Server Instance: %SERVER_INSTANCE%
echo.

REM Get script directory and solution root
set SCRIPT_DIR=%~dp0
set SOLUTION_ROOT=%SCRIPT_DIR%..

echo Solution Root: %SOLUTION_ROOT%
echo.

REM Check prerequisites
echo Checking prerequisites...

REM Check if sqlcmd is available
sqlcmd -? >nul 2>&1
if errorlevel 1 (
    echo WARNING: sqlcmd not found. You may need to install SQL Server Command Line Utilities.
    echo You can still run the migration steps, but database creation and seeding will need to be done manually.
    echo.
)

REM Check if dotnet CLI is available
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: dotnet CLI is not installed or not in PATH
    echo Please install .NET SDK and try again.
    pause
    exit /b 1
)

echo Prerequisites check completed.
echo.

REM Step 1: Create Database
echo === Step 1: Creating Database ===
echo This will create/recreate the OPTdb database. Existing data will be lost!
set /p CONFIRM="Continue? (y/N): "
if /i not "%CONFIRM%"=="y" (
    echo Database creation skipped by user.
    goto :skip_db_creation
)

if exist "%SCRIPT_DIR%01_CreateDatabase.sql" (
    echo Executing database creation script...
    sqlcmd -S "%SERVER_INSTANCE%" -i "%SCRIPT_DIR%01_CreateDatabase.sql" -b
    if errorlevel 1 (
        echo ERROR: Database creation failed
        pause
        exit /b 1
    )
    echo Database creation completed.
) else (
    echo ERROR: Database creation script not found: %SCRIPT_DIR%01_CreateDatabase.sql
    pause
    exit /b 1
)

:skip_db_creation
echo.

REM Step 2: Run Entity Framework Migrations
echo === Step 2: Running Entity Framework Migrations ===

REM Set environment variable
set ASPNETCORE_ENVIRONMENT=%ENVIRONMENT%

REM Change to solution directory
cd /d "%SOLUTION_ROOT%"

REM Install EF tools if needed
echo Checking Entity Framework tools...
dotnet tool list --global | findstr "dotnet-ef" >nul
if errorlevel 1 (
    echo Installing Entity Framework tools...
    dotnet tool install --global dotnet-ef
    if errorlevel 1 (
        echo ERROR: Failed to install Entity Framework tools
        pause
        exit /b 1
    )
)

REM Navigate to Data project
set DATA_PROJECT_PATH=%SOLUTION_ROOT%\src\OPT.Infra.Data
cd /d "%DATA_PROJECT_PATH%"

REM Run main migrations
echo Applying main database migrations...
dotnet ef database update --startup-project ..\OPT.API\OPT.API.csproj
if errorlevel 1 (
    echo ERROR: Main database migrations failed
    pause
    exit /b 1
)

REM Run Identity migrations
echo Applying Identity migrations...
dotnet ef database update --context IdentityOPTContext --startup-project ..\OPT.API\OPT.API.csproj
if errorlevel 1 (
    echo ERROR: Identity migrations failed
    pause
    exit /b 1
)

echo Entity Framework migrations completed.
echo.

REM Step 3: Seed Data
echo === Step 3: Seeding Initial Data ===

if exist "%SCRIPT_DIR%03_SeedData.sql" (
    echo Executing seed data script...
    sqlcmd -S "%SERVER_INSTANCE%" -d "OPTdb" -i "%SCRIPT_DIR%03_SeedData.sql" -b
    if errorlevel 1 (
        echo ERROR: Seed data insertion failed
        pause
        exit /b 1
    )
    echo Seed data insertion completed.
) else (
    echo ERROR: Seed data script not found: %SCRIPT_DIR%03_SeedData.sql
    pause
    exit /b 1
)

echo.

REM Success message
echo === Database Setup Completed Successfully! ===
echo.
echo Next steps:
echo 1. Start the OPT.API project
echo 2. Navigate to https://localhost:7225/swagger to test the API
echo 3. Use SQL Server Management Studio to explore the database
echo.
echo Connection String:
echo Data Source=%SERVER_INSTANCE%;Initial Catalog=OPTdb;Integrated Security=True;TrustServerCertificate=True
echo.

REM Return to solution root
cd /d "%SOLUTION_ROOT%"

pause
exit /b 0
