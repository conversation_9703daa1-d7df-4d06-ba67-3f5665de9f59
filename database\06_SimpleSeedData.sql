-- Simple seed data script for OPTdb essential tables
USE OPTdb;
GO

-- Insert AccountCardTypes
INSERT INTO AccountCardTypes (ID, Description, Payment, Reward) VALUES
('P', 'Payment Card', 1, 0),
('R', 'Reward Card', 0, 1),
('B', 'Both Payment and Reward', 1, 1),
('N', 'None', 0, 0);

PRINT 'AccountCardTypes seeded successfully.';

-- Insert sample Store
DECLARE @StoreId UNIQUEIDENTIFIER = NEWID();
INSERT INTO Stores (ID, Name, Address, City, State, ZipCode, Phone, Email, Active, CreatedDate) VALUES
(@StoreId, 'Main Store', '123 Main Street', 'Anytown', 'CA', '12345', '555-0123', '<EMAIL>', 1, GETDATE());

PRINT 'Sample Store seeded successfully.';

-- Insert sample Account
DECLARE @AccountId UNIQUEIDENTIFIER = NEWID();
INSERT INTO Accounts (ID, StoreId, AccountNumber, CompanyName, ContactName, ContactEmail, ContactPhone, BillingAddress, BillingCity, BillingState, BillingZipCode, Active, CreatedDate) VALUES
(@AccountId, @StoreId, 'ACC001', 'Test Company', 'John Doe', '<EMAIL>', '555-0456', '456 Business Ave', 'Business City', 'CA', '54321', 1, GETDATE());

PRINT 'Sample Account seeded successfully.';

-- Insert sample Kiosk
DECLARE @KioskId UNIQUEIDENTIFIER = NEWID();
INSERT INTO Kiosks (ID, StoreId, KioskNumber, Location, Active, CreatedDate) VALUES
(@KioskId, @StoreId, 'K001', 'Front Entrance', 1, GETDATE());

PRINT 'Sample Kiosk seeded successfully.';

-- Insert sample AccountCard
INSERT INTO AccountCards (ID, AccountId, CardNumber, CardTypeId, Active, CreatedDate) VALUES
(NEWID(), @AccountId, '****************', 'P', 1, GETDATE());

PRINT 'Sample AccountCard seeded successfully.';

-- Insert sample API Key
INSERT INTO ApiKeys (ID, KeyName, KeyValue, Active, CreatedDate) VALUES
(NEWID(), 'Development Key', 'dev-api-key-12345', 1, GETDATE());

PRINT 'Sample API Key seeded successfully.';

PRINT '';
PRINT '=== Seed Data Complete ===';
PRINT 'Essential seed data has been inserted successfully.';
