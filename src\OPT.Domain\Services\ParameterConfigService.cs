﻿using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;

namespace OPT.Domain.Services
{
    public class ParameterConfigService : IParameterConfigService
    {
        private readonly IParameterConfigRepository _repository;

        public ParameterConfigService(IParameterConfigRepository repository)
        {
            _repository = repository;
        }

        public void Dispose()
        {
            _repository.Dispose();
            GC.SuppressFinalize(this);
        }

        public IEnumerable<ParameterConfig> GetAll()
        {
            return _repository.GetAll();
        }
    }
}
