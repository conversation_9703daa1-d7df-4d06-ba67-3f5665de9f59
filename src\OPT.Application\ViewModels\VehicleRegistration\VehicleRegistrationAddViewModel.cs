﻿using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.VehicleRegistration
{
    public class VehicleRegistrationAddViewModel
    {
        [SwaggerSchema("The registration number or identifier for the vehicle.", Nullable = false)]
        public string RegistrationNumber { get; set; }

        [SwaggerSchema("Activate or deactivate vahicle.", Nullable = false)]
        public bool Active { get; set; }

        [SwaggerSchema("The unique ID of the account to which you want to add the vehicle registration.", Nullable = false)]
        public Guid AccountId { get; set; }
    }
}
