﻿using OPT.Domain.Interfaces.UoW;

namespace OPT.Application
{
    public class ApplicationService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ApplicationService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        protected bool Commit()
        {
            return _unitOfWork.Commit();
        }

        public void BeginTransaction()
        {
            _unitOfWork.BeginTransaction();
        }

        public void CommitTransaction()
        {
            _unitOfWork.CommitTransaction();
        }

        public void RollbackTransaction()
        {
            _unitOfWork.RollbackTransaction();
        }
    }
}
