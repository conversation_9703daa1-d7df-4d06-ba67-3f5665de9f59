﻿using FluentEmail.Core;
using FluentEmail.Core.Models;
using FluentEmail.Smtp;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.Extensions.Configuration;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Helper
{
    public interface IEmailSenderHelper : IEmailSender
    {
        Task SendEmailAsync(string email, string subject, string htmlMessage, params AttachmentModel[] attachments);
    }

    public class EmailSenderHelper : IEmailSenderHelper
    {
        private readonly IConfiguration _configuration;
        public EmailSenderHelper(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public Task SendEmailAsync(string email, string subject, string htmlMessage)
        {
            return SendEmailAsync(email, subject, htmlMessage, null);
        }

        public Task SendEmailAsync(string email, string subject, string htmlMessage, params AttachmentModel[] attachments)
        {
            var sender = new SmtpSender(() => new System.Net.Mail.SmtpClient(_configuration["Email:Host"])
            {
                Port = int.Parse(_configuration["Email:Port"]),
                Credentials = new System.Net.NetworkCredential(_configuration["Email:Username"], _configuration["Email:Password"]),
                EnableSsl = true
            });

            Email.DefaultSender = sender;

            var atmts = new List<Attachment>();
            if (attachments != null && attachments.Length > 0) 
            {
                foreach (var attachment in attachments)
                {
                    atmts.Add(new Attachment() { Filename = attachment.FileName, Data = new MemoryStream(attachment.Data), ContentType = $"{attachment.MediaType}/{attachment.MediaSubtype}"});
                }
            }

            var task = Email
                .From(_configuration["Email:FromAddress"], _configuration["Email:FromName"])
                .To(email)
                .Subject(subject)
                .Body(htmlMessage, true)
                .Attach(atmts)
                .SendAsync();
            return Task.CompletedTask;
        }
        
        /*public Task SendEmailAsync(string email, string subject, string htmlMessage, params AttachmentModel[] attachments)
        {
            MimeMessage message = new();
            message.To.Add(new MailboxAddress("", email));
            message.From.Add(new MailboxAddress(_configuration["Email:FromName"], _configuration["Email:FromAddress"]));
            message.Subject = subject;
            var bodyBuilder = new BodyBuilder();
            bodyBuilder.HtmlBody = htmlMessage;
            if(attachments != null && attachments.Length > 0) 
            {
                foreach (var attachment in attachments)
                {
                    bodyBuilder.Attachments.Add(attachment.FileName, attachment.Data, new ContentType(attachment.MediaType, attachment.MediaSubtype));
                }
            }
            message.Body = bodyBuilder.ToMessageBody();

            using var emailClient = new SmtpClient
            {
                ServerCertificateValidationCallback = MySslCertificateValidationCallback
            };
            emailClient.Connect(_configuration["Email:Host"], int.Parse(_configuration["Email:Port"]), SecureSocketOptions.StartTls);
            emailClient.AuthenticationMechanisms.Remove("XOAUTH2");
            emailClient.Authenticate(_configuration["Email:Username"], _configuration["Email:Password"]);
            emailClient.Send(message);
            emailClient.Disconnect(true);
            return Task.CompletedTask;
        }

        bool MySslCertificateValidationCallback(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            return true;
        }*/
    }
}
