﻿using OPT.Domain.Models;

namespace OPT.Domain.Interfaces.Services
{
    public interface IAccountCardService : IDisposable
    {
        void Add(AccountCard card, Guid? storeId = null);

        void Add(IEnumerable<AccountCard> cards, Guid? storeId = null);

        AccountCard? GetByPAN(string pan);

        AccountCard? GetByPAN19Extended(string pan);

        AccountCard? GetByPANExtended(string pan);

        AccountCard? GetByPANOrBarCode(string PANOrBarCode);

        bool AssociateWithUser(Guid cardId, Guid? userId, string PIN);

        AccountCard? GetById(Guid id);

        AccountCard? GetById(Guid id, Guid storeId);

        AccountCard? GetByIdExtended(Guid id);

        AccountCard? GetByDiscountId(Guid discountId);

        void Update(AccountCard current, AccountCard cardUpdate);

        void Update(Guid id, AccountCard cardUpdate);

        void Update(Guid id, AccountCard model, Guid storeId);

        void Delete(Guid id);

        void Delete(Guid id, Guid storeId);

        List<AccountCard> GetByStores(List<Guid>? storesId, List<Guid>? tenantsId);

        (int, List<AccountCard>) GetByStoresWithPagination(List<Guid>? storesId, List<Guid>? tenantsId, int offset, int limit);

        List<AccountCard> GetByAccount(Guid accountId);

        AccountCard? ResetPIN(Guid id, string PIN);

        List<AccountCardRestriction> GetRestrictions(Guid cardId);

        List<AccountCardType> GetAccountCardTypes();
    }
}
