﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using OPT.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OPT.Infra.Data.Mappings
{
        public class FuelTransactionMap : Mapping<FuelTransaction>
    {
        public override void Map(EntityTypeBuilder<FuelTransaction> builder)
        {
            builder.ToTable("FuelTransactions");

            builder.HasKey(x => x.Id);

            base.Map(builder);
        }
    }
}
