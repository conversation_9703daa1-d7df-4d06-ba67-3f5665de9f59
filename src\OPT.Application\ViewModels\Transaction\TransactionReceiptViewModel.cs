﻿
using OPT.Application.ViewModels.Account;
using OPT.Application.ViewModels.FuelTransaction;
using OPT.Application.ViewModels.Kiosk;
using OPT.Application.ViewModels.Store;
using OPT.Application.ViewModels.User;

namespace OPT.Application.ViewModels.Transaction
{
    public class TransactionReceiptViewModel
    {
        public int TerminalTransactionId { get; set; }
        public string TransactionDateTime { get; set; }
        public string Type { get; set; }
        public int Status { get; set; }
        //public decimal AuthorisedAmount { get; set; }
        public decimal FinalAmount { get; set; }
        public decimal GSTAmount { get; set; }
        public string CardSignature { get; set; }
        public string VehicleRegistration { get; set; }
        public string VehicleOdometer { get; set; }
        public string AccountType { get; set; }
        public string ApplicationId { get; set; }
        public int STAN { get; set; }
        public string ResponseCode { get; set; }
        public string HostResponse { get; set; }
        public int AuthorizationID { get; set; }
        public int PanSeqNo { get; set; }
        public string CVM { get; set; }
        public string Processor { get; set; }
        //public int TerminalId { get; set; }
        public Guid StoreId { get; set; }
        public Guid KioskId { get; set; }
        public Guid AccountId { get; set; }
        public string UserId { get; set; }
        public FuelTransactionDisplayViewModel FuelTransaction { get; set; }
        public AccountUserViewModel Account { get; set; }
        public StoreAddressViewModel Store { get; set; }
        public KioskNameViewModel Kiosk { get; set; }
        public UserAccountViewModel User { get; set; }

        public string Currency { get; set; }
        public string? ApplicationLabel { get; set; }
        public int? CardATC { get; set; }
        public string? TID { get; set; }

    }
}
