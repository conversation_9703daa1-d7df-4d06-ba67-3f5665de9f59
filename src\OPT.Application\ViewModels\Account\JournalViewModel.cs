﻿using OPT.Domain.Models.Enumerations;
using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.Account
{
    public class JournalViewModel
    {
        [SwaggerSchema("Date of journal.", Nullable = false)]
        public DateTime JournalDate { get; set; }

        [SwaggerSchema("Account ID.", Nullable = false)]
        public Guid AccountId { get; set; }

        [SwaggerSchema("Journal Type (1 - Payment, 2 - Positive Adjustment, 3 - Negative Adjustment, 4 - Fee).", Nullable = false)]
        public JournalType JournalTypeId { get; set; }

        [SwaggerSchema("Amount of Journal.", Nullable = false)]
        public decimal Amount { get; set; }

        [SwaggerSchema("Reason of Journal.", Nullable = true)]
        public string? Reason { get; set; }
    }
}
