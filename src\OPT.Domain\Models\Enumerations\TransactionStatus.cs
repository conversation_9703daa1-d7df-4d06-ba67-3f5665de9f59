﻿using System.ComponentModel;

namespace OPT.Domain.Models.Enumerations
{
    public enum TransactionStatus : byte
    {
        [Description("Card Terminal Initialised")]
        CardTerminalInitialised = 0,
        [Description("Card Authorizing")]
        CardAuthorizing = 1,
        [Description("Card Authorized")]
        CardAuthorized = 2,
        [Description("Pump Authorized")]
        PumpAuthorized = 3,
        [Description("Pump Finished")]
        PumpFinished = 4,
        [Description("Card Captured")]
        CardCaptured = 5,
        [Description("Card in Progress")]
        CardInProgress = 6,
        [Description("Card Reversed")]
        CardReversed = 7,
        [Description("User Cancelled")]
        UserCancelled = 8,
        [Description("Card Auth Rejected")]
        CardAuthRejected = 12,
        [Description("Card Capture Rejected")]
        CardCaptureRejected = 25,
        [Description("Card Reversal Rejected")]
        CardReversalRejected = 26,
        [Description("Transaction Error")]
        TrxError = 99
    }

    public static class TransactionStatusExtension
    {
        public static string GetDescription(this TransactionStatus? value)
        {
            if (value == null)
                return null;

            var attribute = value.GetType()
                .GetField(value.ToString())?
                .GetCustomAttributes(typeof(DescriptionAttribute), false)
                .SingleOrDefault() as DescriptionAttribute;
            return attribute == null ? value.ToString() : attribute.Description;
        }
    }
}
