﻿using Microsoft.Extensions.DependencyInjection;
using OPT.Application;
using OPT.Application.Interfaces;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Interfaces.UoW;
using OPT.Domain.Services;
using OPT.Infra.Data.Context;
using OPT.Infra.Data.Helper;
using OPT.Infra.Data.Identity.Context;
using OPT.Infra.Data.Repositories;
using OPT.Infra.Data.UoW;

namespace OPT.Infra.CrossCutting.IoC
{
    public class BootStrapper
    {
        public static void RegisterServices(IServiceCollection services)
        {
            // Application
            services.AddScoped<IUserAppService, UserAppService>();
            services.AddScoped<IStoreAppService, StoreAppService>();
            services.AddScoped<IAccountAppService, AccountAppService>();
            services.AddScoped<IKioskAppService, KioskAppService>();
            services.AddScoped<ITransactionAppService, TransactionAppService>();
            services.AddScoped<IAccountCardAppService, AccountCardAppService>();
            services.AddScoped<IVehicleRegistrationAppService, VehicleRegistrationAppService>();
            services.AddScoped<ITransactionAuditAppService, TransactionAuditAppService>();
            services.AddScoped<IApiKeyAppService, ApiKeyAppService>();

            // Domain
            services.AddScoped<IIdentityService, IdentityService>();
            services.AddScoped<IStoreService, StoreService>();
            services.AddScoped<IAccountService, AccountService>();
            services.AddScoped<IKioskService, KioskService>();
            services.AddScoped<ITransactionService, TransactionService>();
            services.AddScoped<IParameterConfigService, ParameterConfigService>();
            services.AddScoped<IAccountCardService, AccountCardService>();
            services.AddScoped<IAccountCardTransactionService, AccountCardTransactionService>();
            services.AddScoped<IVehicleRegistrationService, VehicleRegistrationService>();
            services.AddScoped<ITransactionAuditService, TransactionAuditService>();
            services.AddScoped<IRewardCardTransactionService, RewardCardTransactionService>();
            services.AddScoped<IApiKeyService, ApiKeyService>();

            // Data
            services.AddScoped<IUnitOfWork, UnitOfWork<OPTContext>>();
            services.AddScoped<OPTContext>();
            services.AddScoped<IEmailSenderHelper, EmailSenderHelper>();
            services.AddScoped<IIdentityRepository, IdentityRepository>();
            services.AddScoped<IStoreRepository, StoreRepository>();
            services.AddScoped<IAccountRepository, AccountRepository>();
            services.AddScoped<IKioskRepository, KioskRepository>();
            services.AddScoped<ITransactionRepository, TransactionRepository>();
            services.AddScoped<IParameterConfigRepository, ParameterConfigRepository>();
            services.AddScoped<IAccountCardRepository, AccountCardRepository>();
            services.AddScoped<IAccountCardTransactionRepository, AccountCardTransactionRepository>();
            services.AddScoped<IVehicleRegistrationRepository, VehicleRegistrationRepository>();
            services.AddScoped<ITransactionAuditRepository, TransactionAuditRepository>();
            services.AddScoped<IRewardCardTransactionRepository, RewardCardTransactionRepository>();
            services.AddScoped<IApiKeyRepository, ApiKeyRepository>();

            //Identity
            services.AddScoped<UserStoreCustom>();
        }
    }
}