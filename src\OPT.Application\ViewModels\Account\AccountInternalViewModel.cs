﻿using OPT.Application.ViewModels.Store;

namespace OPT.Application.ViewModels.Account
{
    public class AccountInternalViewModel
    {
        public Guid ID { get; set; }

        public string Name { get; set; }
        public string? Email { get; set; }

        public bool Active { get; set; }

        public string? MobileNumber { get; set; }

        public decimal? CreditLimit { get; set; }

        public string? Code { get; set; }

        public char? FlagOdometer { get; set; }

        public StoreViewModel? Store { get; set; }

        public Guid? TenantID { get; set; }
    }
}
