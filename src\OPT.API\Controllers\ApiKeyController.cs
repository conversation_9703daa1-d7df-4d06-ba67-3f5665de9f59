﻿using Microsoft.AspNetCore.Mvc;
using OPT.API.Attributes;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.ApiKey;
using Swashbuckle.AspNetCore.Annotations;

namespace OPT.API.Controllers
{
    /// <summary>
    /// Authentication in the OPT API is crucial to ensure data security and integrity. To get started, users need to obtain an API Key through POSMaster. This key serves as a unique credential to authenticate the application with the API.
    ///
    /// ### Step 1: Obtaining the API Key
    /// Access POSMaster and navigate to OPT -> API Key Management.
    /// Create a new API Key associated with your application.This key will be used to authenticate your API calls.
    /// ### Step 2: Authentication and Obtaining the Access Token
    /// With the API Key in hand, you can request an access token from the API.
    /// Authentication Endpoint:
    /// 
    ///     POST /api/ApiKey/authenticate
    ///     
    /// Request Parameters:
    /// 
    /// ```yaml
    /// {
    /// "accessToken": "API Key",
    /// }
    /// ```
    /// 
    /// - `accessToken`: The API Key obtained earlier.
    /// 
    /// The response will be an access token in JSON Web Token (JWT) format, of type Bearer.This token should be included in the Authorization header in all your future requests.
    /// 
    /// ### Step 3: Using the Access Token
    /// The access token is valid for 1 hour from the time of issuance. After this period, the token will expire, and you will need to obtain a new one.
    /// 
    /// If the token expires and you attempt to access a protected endpoint, the API will return HTTP status code `401` (Unauthorized) or `407` (Proxy Authentication Required).
    /// 
    /// ### Authentication Errors
    /// If a user attempts to access an API for which they do not have permission, the API will return HTTP status code `403` (Forbidden).
    /// 
    /// > Remember to store and manage your API Key and access tokens securely.Never share your API Key and avoid exposing your access tokens publicly.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerControllerOrder(1)]
    public class ApiKeyController : ControllerBase
    {
        private readonly IApiKeyAppService _appService;

        /// <summary>
        /// ApiKeyController Constructor
        /// </summary>
        /// <param name="appService"></param>
        public ApiKeyController(IApiKeyAppService appService)
        {
            _appService = appService;
        }

        /// <summary>
        /// Add New ApiKey
        /// </summary>
        /// <returns></returns>
        [ApiExplorerSettings(GroupName = "v1")]
        [Authorize(Role.POSMaster)]
        [HttpPost]
        public ResponseViewModel Add(ApiKeyAddViewModel kiosk)
        {
            _appService.Add(kiosk);
            return ResponseViewModel.Ok($"Kiosk {kiosk.Name} added with success.");
        }

        /// <summary>
        /// Change ApiKey access Token
        /// </summary>
        /// <param name="id">ApiKey id</param>
        [ApiExplorerSettings(GroupName = "v1")]
        [Authorize(Role.POSMaster)]
        [HttpPut("{id}/accessToken")]
        public ResponseViewModel UpdateAccessToken(Guid id)
        {
            string accessToken = _appService.UpdateAccessToken(id);
            return ResponseViewModel.Ok(accessToken);
        }

        /// <summary>
        /// Change ApiKey activation
        /// </summary>
        /// <param name="id">ApiKey id</param>
        /// <param name="active">true ou false</param>
        [ApiExplorerSettings(GroupName = "v1")]
        [Authorize(Role.POSMaster)]
        [HttpPut("{id}/active/{active}")]
        public ResponseViewModel UpdateActive(Guid id, bool active)
        {
            _appService.UpdateActive(id, active);
            return ResponseViewModel.Ok($"ApiKey {id} updated with success.");
        }

        /// <summary>
        /// list ApiKey by stores
        /// </summary>
        /// <returns></returns>
        [ApiExplorerSettings(GroupName = "v1")]
        [Authorize(Role.POSMaster)]
        [HttpPost("stores")]
        public List<ApiKeyViewModel> GetByStores(List<Guid> storesId)
        {
            return _appService.GetByStores(storesId);
        }

        /// <summary>
        /// delete ApiKey
        /// </summary>
        /// <returns></returns>
        [ApiExplorerSettings(GroupName = "v1")]
        [Authorize(Role.POSMaster)]
        [HttpDelete("{id}")]
        public ResponseViewModel Remove(Guid id)
        {
            _appService.Remove(id);
            return ResponseViewModel.Ok($"ApiKey {id} removed with success.");
        }

        [HttpPost("authenticate")]
        [SwaggerOperation(
            Summary = "Get Authentication",
            Description = "This endpoint is responsible for generating an access token, which must be included in the authorization header of all subsequent requests."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return an access token in JSON Web Token (JWT) format of type Bearer.", typeof(ApiKeyTokenViewModel))]
        public ApiKeyTokenViewModel Authenticate(
            [SwaggerRequestBody("The API Key generated from POSMaster. This key is essential to authenticate the application with the API.", Required = true)]
            ApiKeyAuthenticateViewModel model)
        {
            return new ApiKeyTokenViewModel(_appService.Authenticate(model));
        }
    }
}
