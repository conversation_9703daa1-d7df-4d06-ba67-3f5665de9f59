﻿using OPT.Domain.Models;

namespace OPT.Domain.Interfaces.Services
{
    public interface IKioskService : IDisposable
    {
        Kiosk? GetById(Guid id);

        Kiosk? GetByDeviceId(string deviceId);

        Kiosk? GetByHash(string validHash);

        void AddOrUpdate(Kiosk model, string validHash);

        void Update(Kiosk model);

        void UpdateActive(Guid id, bool ative);

        List<Kiosk> GetByStores(List<Guid> storesId);

        void AddInfo(KioskInfo info);
    }
}
