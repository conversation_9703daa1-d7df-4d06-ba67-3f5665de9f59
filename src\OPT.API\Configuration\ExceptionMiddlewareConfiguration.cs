﻿using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http.Json;
using Microsoft.Extensions.Options;
using OPT.Application.ViewModels;
using OPT.Domain.Exceptions;
using System.Net;
using System.Text.Json;

namespace OPT.API.Configuration
{
    public static class ExceptionMiddlewareConfiguration
    {
        public static void ConfigureExceptionHandler(this IApplicationBuilder app)
        {
            app.UseExceptionHandler(appError =>
            {
                appError.Run(async context =>
                {
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    context.Response.ContentType = "application/json";
                    var contextFeature = context.Features.Get<IExceptionHandlerFeature>();
                    if (contextFeature != null)
                    {
                        //context.RaiseError(context.Exception);

                        context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                        context.Response.ContentType = "application/json";


                        // Get the options
                        var jsonOptions = context.RequestServices.GetService<IOptions<JsonOptions>>();

                        // Serialise using the settings provided
                        var json = JsonSerializer.Serialize(
                            ResponseViewModel.Error(contextFeature.Error.Message), // Switch this with your object
                            jsonOptions?.Value.SerializerOptions);

                        BusinessException? bussinessEx;
                        if ((bussinessEx = contextFeature.Error as BusinessException) != null && bussinessEx.Code != null)
                        {
                            context.Response.StatusCode = bussinessEx.Code.Value;
                        }

                        await context.Response.WriteAsync(json);

                    }
                });
            });

            app.Use(async (context, next) =>
            {
                await next();

                if (context.Response.StatusCode == (int)HttpStatusCode.Unauthorized
                    || context.Response.StatusCode == (int)HttpStatusCode.ProxyAuthenticationRequired
                    || context.Response.StatusCode == (int)HttpStatusCode.Forbidden)
                {
                    //context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    context.Response.ContentType = "application/json";

                    // Get the options
                    var jsonOptions = context.RequestServices.GetService<IOptions<JsonOptions>>();

                    var message = string.Empty;
                    switch (context.Response.StatusCode)
                    {
                        case (int)HttpStatusCode.Unauthorized:
                        case (int)HttpStatusCode.ProxyAuthenticationRequired:
                            message = "Token Validation Has Failed. Request Access Denied.";
                            break;
                        case (int)HttpStatusCode.Forbidden:
                            message = "The client does not have access rights to the content. Request Access Denied.";
                            break;
                        default:
                            break;
                    }

                    // Serialise using the settings provided
                    var json = JsonSerializer.Serialize(
                        ResponseViewModel.Error(message), // Switch this with your object
                        jsonOptions?.Value.SerializerOptions);

                    await context.Response.WriteAsync(json);
                }
            });
        }
    }
}
