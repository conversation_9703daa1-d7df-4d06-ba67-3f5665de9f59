﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using OPT.Domain.Models;
using OPT.Infra.Data.Extensions;
using OPT.Infra.Data.Mappings;

namespace OPT.Infra.Data.Context
{
    public class OPTContext : DbContext
    {
        protected readonly IConfiguration Configuration;

        public OPTContext(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        #region DBSet
        
        public DbSet<Store> Stores { get; set; }
        public DbSet<Account> Accounts { get; set; }
        public DbSet<UserAccount> UserAccounts { get; set; }
        public DbSet<Kiosk> Kiosks { get; set; }
        public DbSet<KioskConfig> KioskConfigs { get; set; }
        public DbSet<KioskInfo> kioskInfos { get; set; }
        public DbSet<Transaction> Transactions { get; set; }
        public DbSet<FuelTransaction> FuelTransactions { get; set; }
        public DbSet<ParameterConfig> ParameterConfigs { get; set; }
        public DbSet<AccountCard> AccountCards { get; set; }
        public DbSet<AccountCardRestriction> AccountCardRestrictions { get; set; }
        public DbSet<AccountCardType> AccountCardTypes { get; set; }
        public DbSet<VehicleRegistration> VehicleRegistrations { get; set; }
        public DbSet<AccountVehicleRegistration> AccountVehicleRegistrations { get; set; }
        public DbSet<AccountDiscount> AccountDiscounts { get; set; }
        public DbSet<AccountCardTransaction> AccountCardTransactions { get; set; }
        public DbSet<AccountJournal> AccountJournals { get; set; }
        public DbSet<AccountBalance> AccountBalances { get; set; }
        public DbSet<TransactionAudit> TransactionAudits { get; set; }
        public DbSet<RewardCardDiscount> RewardCardDiscounts { get; set; }
        public DbSet<RewardCardTransaction> RewardCardTransactions { get; set; }
        public DbSet<ApiKey> ApiKeys { get; set; }

        #endregion

        protected override void OnConfiguring(DbContextOptionsBuilder options)
        {
            options.UseSqlServer(Configuration.GetConnectionString("DefaultConnection"));
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.AddConfiguration(new StoreMap());
            modelBuilder.AddConfiguration(new AccountMap());
            modelBuilder.AddConfiguration(new UserAccountMap());
            modelBuilder.AddConfiguration(new KioskMap());
            modelBuilder.AddConfiguration(new KioskConfigMap());
            modelBuilder.AddConfiguration(new KioskInfoMap());
            modelBuilder.AddConfiguration(new TransactionMap());
            modelBuilder.AddConfiguration(new FuelTransactionMap());
            modelBuilder.AddConfiguration(new ParameterConfigMap());
            modelBuilder.AddConfiguration(new AccountCardMap());
            modelBuilder.AddConfiguration(new AccountCardRestrictionMap());
            modelBuilder.AddConfiguration(new AccountCardTypeMap());
            modelBuilder.AddConfiguration(new VehicleRegistrationMap());
            modelBuilder.AddConfiguration(new AccountVehicleRegistrationMap());
            modelBuilder.AddConfiguration(new AccountDiscountMap());
            modelBuilder.AddConfiguration(new AccountCardTransactionMap());
            modelBuilder.AddConfiguration(new AccountJournalMap());
            modelBuilder.AddConfiguration(new AccountBalanceMap());
            modelBuilder.AddConfiguration(new TransactionAuditMap());
            modelBuilder.AddConfiguration(new RewardCardDiscountMap());
            modelBuilder.AddConfiguration(new RewardCardTransactionMap());
            modelBuilder.AddConfiguration(new ApiKeyMap());

            base.OnModelCreating(modelBuilder);
        }
    }
}
