﻿using AutoMapper;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.Transaction;
using OPT.Application.ViewModels.User;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Interfaces.UoW;
using OPT.Domain.Models;

namespace OPT.Application
{
    
    public class TransactionAppService : ApplicationService, ITransactionAppService
    {
        private readonly ITransactionService _service;
        private readonly IIdentityService _userService;
        private readonly IAccountCardTransactionService _accountCardTransactionService;
        private readonly IAccountCardService _accountCardService;
        private readonly IMapper _mapper;

        public TransactionAppService(IUnitOfWork unitOfWork,
                                IMapper mapper,
                                ITransactionService service,
                                IIdentityService userService,
                                IAccountCardTransactionService accountCardTransactionService,
                                IAccountCardService accountCardService) : base(unitOfWork)
        {
            _service = service;
            _mapper = mapper;
            _userService = userService;
            _accountCardTransactionService = accountCardTransactionService;
            _accountCardService = accountCardService;
        }


        public void Add(TransactionAddViewModel transaction)
        {
            var trx = _mapper.Map<Transaction>(transaction);

            if (trx.AuthorizationID != null && trx.STAN != null && trx.TID != null)
            {
                var accctrx = _accountCardTransactionService.GetByAuthCodeSTANAndTerminalID(trx.AuthorizationID.Value.ToString("D6"), Convert.ToInt64(trx.STAN), trx.TID);
                if (accctrx != null)
                {
                    trx.Processor = "WC";
                    trx.AccountId = accctrx.AccountCard?.AccountId;
                    trx.AccountCardId = accctrx.AccountCard?.ID;
                }
            }

            if(trx.RewardCardTransactions != null)
            {
                foreach (var item in trx.RewardCardTransactions)
                {
                    //single use per day - Quick Fuel Card Rule
                    var card = _accountCardService.GetByDiscountId(item.RewardCardDiscountID);
                    if (card != null && (new char[] {'C', 'R', 'S'}).Contains(card.Type))
                    {
                        card.Status = "06";
                        _accountCardService.Update(card, card);
                    }

                    item.CreatedDateTime = trx.TransactionDateTime;
                    item.KioskId = trx.KioskId;
                }
            }

            _service.Add(trx);
            Commit();
        }

        public List<UnitedTransactionFileViewModel> GetUnitedToExport(Guid tenantID)
        {
            var localTrxs = _service.GetUnitedToExport(tenantID);
            var trxs = _mapper.Map<List<UnitedTransactionFileViewModel>>(localTrxs);

            if(localTrxs.Count > 0)
                _service.SetUnitedExported(localTrxs.Select(x => x.Id));

            return trxs;
        }

        public async Task <List<TransactionAccountViewModel>> GetByFilter(TransactionFilterViewModel filter)
        {
            List<Transaction> t = _service.GetByFilter(filter.AccountId, filter.KioskId, filter.DateFrom, filter.DateTo, filter.StoresId);
            var tvms = _mapper.Map<List<TransactionAccountViewModel>>(t);
            for (int i = 0; i < t.Count; i++)
            {
                if (!string.IsNullOrEmpty(t[i].UserId))
                    tvms[i].User = _mapper.Map<UserAccountViewModel>(await _userService.GetById(new Guid(t[i].UserId)));
            }

            return tvms;
        }
        
        public async Task <TransactionPagination> GetByFilterWithPagination(TransactionFilterV2ViewModel filter)
        {
            (int total, List<FuelTransaction> totalGrades, List<Transaction> t) = _service.GetByFilterWithPagination(filter.AccountId, filter.KioskId, filter.DateFrom, filter.DateTo, filter.StoresId, filter.Offset, filter.Limit);
            var tvms = _mapper.Map<List<TransactionAccountViewModel>>(t);
            for (int i = 0; i < t.Count; i++)
            {
                if (!string.IsNullOrEmpty(t[i].UserId))
                    tvms[i].User = _mapper.Map<UserAccountViewModel>(await _userService.GetById(new Guid(t[i].UserId)));
            }

            return new TransactionPagination
            {
                TotalRecords = total,
                Offset = filter.Offset,
                PageRecords = tvms.Count,
                List = tvms,
                Grades = totalGrades.Select(y => new TotalTransactionGradeViewModel
                {
                    FuelGradeId = y.FuelGradeId,
                    FuelGradeName = y.FuelGradeName,
                    TotalRevenue = y.FuelAmount / 100m,
                    TotalVolume = y.FuelVolume / 100m
                }).ToList()
            };
        }

        public List<TransactionUserViewModel> GetByUserId(string userId)
        {
            return _mapper.Map<List<TransactionUserViewModel>>(_service.GetByUserId(userId));
            
        }

        public void Dispose()
        {
            _service.Dispose();
            _accountCardService.Dispose();
            _accountCardTransactionService.Dispose();
            GC.SuppressFinalize(this);
        }

        public async Task SendTransactionReceipt(int transactionId)
        {
            TransactionReceiptViewModel trvm =_mapper.Map<TransactionReceiptViewModel>(_service.GetByTransactionId(transactionId));
            trvm.User = _mapper.Map<UserAccountViewModel>(await _userService.GetById(new Guid(trvm.UserId)));
            await _service.SendEmail(trvm.User.UserName, "SmartFuel Transaction Receipt", HtmlTemplateHelper.GetTransactionReceiptTemplate(trvm));
            //await _service.SendEmail(trvm.Account.Email, "SmartFuel Transaction Receipt", HtmlTemplateHelper.GetTransactionReceiptTemplate(trvm));
        }
        
        public async Task SendTransactionReceipt(CardReceiptViewModel cardReceipt, string email)
        {
            var html = HtmlTemplateHelper.GetTransactionReceiptTemplate(cardReceipt);
            var att = new AttachmentModel(
                "Receipt_" + cardReceipt.TerminalTransactionId + ".pdf",
                PDFHelper.HtmlToPDF(html),
                "application",
                "pdf"
            );
            await _service.SendEmail(email, "SmartFuel Transaction Receipt", html, att);
        }
    }
}
