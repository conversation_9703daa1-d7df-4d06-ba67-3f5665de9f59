﻿using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.Account
{
    public class AccountBalanceViewModel
    {
        [SwaggerSchema("Last time the balance was updated", Nullable = false)]
        public DateTime LastBalanceDate { get; set; }

        [SwaggerSchema("Balance total amount", Nullable = false)]
        public decimal Amount { get; set; }

        [SwaggerSchema("List of Statements", Nullable = false)]
        public List<AccountStatementViewModel> Statements { get; set; }
    }
}
