﻿using OPT.Domain.Models;

namespace OPT.Domain.Interfaces.Repositories
{
    public interface IAccountRepository : IRepository<Account>
    {
        void RemoveUser(Guid userId);

        void AddOrUpdateUser(UserAccount entity);

        List<UserAccount> GetUsers(Guid id);

        Account? GetByUser(Guid userId);

        List<Account> GetByStores(List<Guid>? storesId, List<Guid>? tenantsId);

        int GetTotalByStores(List<Guid>? storesId, List<Guid>? tenantsId);

        List<Account> GetByStoresWithPagination(List<Guid>? storesId, List<Guid>? tenantsId, int offset, int limit);

        AccountBalance? GetBalance(Guid accountId);

        Account? GetByCode(string code);

        void addJournal(AccountJournal accountJournal);

        List<AccountJournal> GetJournalsByDate(Guid accountId, DateTime dateFrom, DateTime dateTo);

        void UpdateBalance(Guid accountId, decimal amount);
    }
}
