﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;
namespace OPT.Infra.Data.Mappings
{
    public class ApiKeyMap : Mapping<ApiKey>
    {
        public override void Map(EntityTypeBuilder<ApiKey> builder)
        {
            builder.ToTable("APIKeys");

            builder.HasKey(x => x.ID);

            builder.Property(x => x.CreatedDateTime).ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);

            builder.HasOne(x => x.Store).WithMany().HasForeignKey(x => x.StoreID).OnDelete(DeleteBehavior.NoAction);

            base.Map(builder);
        }
    }
}
