﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class AccountCardTransactionMap : Mapping<AccountCardTransaction>
    {
        public override void Map(EntityTypeBuilder<AccountCardTransaction> builder)
        {
            builder.ToTable("AccountCardTransactions");

            builder.<PERSON><PERSON>ey(x => x.ID);

            builder.Property(x => x.STAN).ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);

            builder.HasOne(x => x.AccountCard).WithMany().HasForeignKey(x => x.AccountCardID).OnDelete(DeleteBehavior.NoAction);

            base.Map(builder);
        }
    }
}
