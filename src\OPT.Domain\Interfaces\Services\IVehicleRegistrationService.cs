﻿using OPT.Domain.Models;

namespace OPT.Domain.Interfaces.Services
{
    public interface IVehicleRegistrationService : IDisposable
    {
        void Add(VehicleRegistration vehicleRegistration, Guid accountId);

        void Update(VehicleRegistration vehicleRegistration);

        void Update(VehicleRegistration vehicleRegistration, Guid storeId);

        void Delete(Guid id);

        void Delete(Guid id, Guid storeId);

        List<VehicleRegistration> GetByAccount(Guid accountId);

        VehicleRegistration? GetById(Guid id);

        VehicleRegistration? GetById(Guid id, Guid storeId);

        VehicleRegistration? GetByRegistrationNumber(string registrationNumber);
    }
}
