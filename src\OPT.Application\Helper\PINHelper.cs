﻿using OPT.Domain.Models;

namespace OPT.Application.Helper
{
    public class PINHelper
    {
        public static string DecryptPin(string pinOrig)
        {
            byte[] PIN = ConvertHelper.HexStringToByteArray(pinOrig);

            byte[] cipher = new byte[8];
            Array.Copy(PIN, 0, cipher, 0, 8);
            byte[] ksn = new byte[10];
            Array.Copy(PIN, 8, ksn, 0, 10);
            byte[] pin = CryptographyHelper.DecryptDUKPT(ksn, cipher);
            string pinstr = ConvertHelper.ByteArrayToHexString(pin);
            int pinlength = int.Parse(pinstr.Substring(1, 1));
            //pinstr = pinstr.Remove(0, 1).Insert(0, "0");
            //for (int i = 0; i < 14 - pinlength; i++)
            //    pinstr = pinstr.Remove(i + 2 + pinlength, 1).Insert(i + 2 + pinlength, "F");
            //pin = ConvertHelper.HexStringToByteArray(pinstr);
            //for (int i = 0; i < 6; i++)
            //    pin[i + 2] ^= extractedPAN[i];
            //pin = HSM.Instance.EncryptPINBlock(pin, HSM.KEY_SESSION_PIN_1);
            return pinstr.Substring(2, pinlength);
        }
    }
}
