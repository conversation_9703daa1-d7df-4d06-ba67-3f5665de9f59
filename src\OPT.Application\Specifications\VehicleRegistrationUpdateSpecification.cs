﻿using OPT.Application.ViewModels.VehicleRegistration;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class VehicleRegistrationUpdateSpecification : ISpecificationConfiguration<VehicleRegistrationViewModel>
    {
        private readonly IVehicleRegistrationService _service;
        public VehicleRegistrationUpdateSpecification(IVehicleRegistrationService service)
        {
            _service = service;
        }

        public ISpecification<VehicleRegistrationViewModel> Map(ISpecification<VehicleRegistrationViewModel> builder)
        {
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.RegistrationNumber), "The Registration Number is incorrect!");

            builder.IsSatisfiedBy(x => 
            { 
                var reg = _service.GetByRegistrationNumber(x.RegistrationNumber);
                return !(reg != null && !reg.ID.Equals(x.ID));
            }, "This registration number already exists.");


            return builder;
        }
    }
}
