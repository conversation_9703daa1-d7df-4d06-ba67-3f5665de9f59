﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class AccountBalanceMap : Mapping<AccountBalance>
    {
        public override void Map(EntityTypeBuilder<AccountBalance> builder)
        {
            builder.ToTable("AccountBalances");

            builder.HasKey(x => x.ID);

            //builder.HasOne(x => x.Account).WithMany().HasForeignKey(x => x.AccountId).OnDelete(DeleteBehavior.NoAction);

            base.Map(builder);
        }
    }
}
