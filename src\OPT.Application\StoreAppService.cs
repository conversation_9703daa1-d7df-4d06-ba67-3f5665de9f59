﻿using AutoMapper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels.Store;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Interfaces.UoW;
using OPT.Domain.Models;

namespace OPT.Application
{
    public class StoreAppService : ApplicationService, IStoreAppService
    {
        private readonly IStoreService _service;
        private readonly IMapper _mapper;

        public StoreAppService(IUnitOfWork unitOfWork,
                                IMapper mapper,
                                IStoreService service) : base(unitOfWork)
        {
            _service = service;
            _mapper = mapper;
        }

        public void Add(StoreViewModel store)
        {
            _service.Add(_mapper.Map<Store>(store));
            Commit();
        }

        public void Dispose()
        {
            _service.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
