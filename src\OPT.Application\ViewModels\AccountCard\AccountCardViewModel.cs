﻿using OPT.Application.ViewModels.Account;
using OPT.Application.ViewModels.User;
using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.AccountCard
{
    public class AccountCardViewModel
    {
        [SwaggerSchema("A unique identifier for the AccountCard.", Nullable = true)]
        public Guid ID { get; set; }

        [SwaggerSchema("A unique identifier for the AccountCard used in transaction processing.", Nullable = false)]
        public string PAN { get; set; }

        [SwaggerSchema("The date when the AccountCard expires and can no longer be used for transactions (Format: YYMM).", Nullable = false)]
        public string ExpiryDate { get; set; }

        [SwaggerSchema("Indicates whether the AccountCard is currently active and available for use.", Nullable = false)]
        public bool Active { get; set; }

        [SwaggerSchema("The maximum amount that can be spent using the AccountCard for a single transaction.", Nullable = true)]
        public decimal? TransactionLimit { get; set; }

        //public UserCardAccountViewModel User { get; set; }

        [SwaggerSchema("A unique identifier for the AccountCard used in transaction processing with 19 digits.", Nullable = true)]
        public string? PAN19 { get; set; }

        public List<AccountCardRestrictionViewModel> Restrictions { get; set; }
    }
}
