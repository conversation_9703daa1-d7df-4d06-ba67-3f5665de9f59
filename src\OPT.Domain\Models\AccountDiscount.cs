﻿using OPT.Domain.Models.Enumerations;

namespace OPT.Domain.Models
{
    public class AccountDiscount
    {
        public Guid ID { get; set; }

        public Guid AccountId { get; set; }

        public TransactionDiscountTypes DiscountTypeId { get; set; }

        public decimal DiscountValue { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public bool Active { get; set; }

    }
}
