﻿using Microsoft.EntityFrameworkCore;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Models;
using OPT.Infra.Data.Context;

namespace OPT.Infra.Data.Repositories
{
    public class AccountCardRepository : Repository<AccountCard>, IAccountCardRepository
    {
        public AccountCardRepository(OPTContext context) : base(context)
        {
        }

        public new AccountCard? GetById(params object[] ids)
        {
            return _dbset
                .Include(x => x.Account)
                .Include(x => x.Restrictions)
                .Include(x => x.Discounts)
                .Include(x => x.Store)
                .AsNoTracking()
                .FirstOrDefault(x => ids.Contains(x.ID));
        }
        
        public AccountCard? GetByIdExtended(params object[] ids)
        {
            return _dbset
                .Include(x => x.Account)
                .ThenInclude(x => x.AccountVehicleRegistrations)
                .ThenInclude(x => x.VehicleRegistration)
                .AsNoTracking().FirstOrDefault(x => ids.Contains(x.ID));
        }

        public AccountCard? GetByDiscountId(Guid discountId)
        {
            return _dbset.AsNoTracking().FirstOrDefault(x => x.Discounts.Any(y => y.ID == discountId));
        }

        public List<AccountCard> GetByStores(List<Guid>? storeIds, List<Guid>? tenantsId)
        {

            if (storeIds != null)
                return _dbset
                    .Where(x => (x.StoreID != null && storeIds.Contains(x.StoreID.Value)) || (x.StoreID == null && x.Account.StoreID != null && storeIds.Contains(x.Account.StoreID.Value)))
                    .Include(x => x.Store)
                    .Include(x => x.Account)
                    .Include(x => x.AccountCardType)
                    .AsNoTracking().OrderBy(x => x.PAN).ToList();
            else if (tenantsId != null)
                return _dbset
                    .Where(x => (x.StoreID != null && tenantsId.Contains(x.Store.TenantID)) 
                                || (x.StoreID == null && tenantsId.Contains(x.Account.TenantID.Value)))
                    .Include(x => x.Store)
                    .Include(x => x.Account)
                    .Include(x => x.AccountCardType)
                    .AsNoTracking().OrderBy(x => x.PAN).ToList();
            else
                return new List<AccountCard>();
        }
        
        public int GetTotalByStores(List<Guid>? storeIds, List<Guid>? tenantsId)
        {
            if (storeIds != null)
                return _dbset
                    .Where(x => (x.StoreID != null && storeIds.Contains(x.StoreID.Value)) || (x.StoreID == null && x.Account.StoreID != null && storeIds.Contains(x.Account.StoreID.Value)))
                    .AsNoTracking().Count();
            else if (tenantsId != null)
                return _dbset
                    .Where(x => (x.StoreID != null && tenantsId.Contains(x.Store.TenantID)) 
                                || (x.StoreID == null && tenantsId.Contains(x.Account.TenantID.Value)))
                    .AsNoTracking().Count();
            else
                return 0;
        }
        
        public List<AccountCard> GetByStoresWithPagination(List<Guid>? storeIds, List<Guid>? tenantsId, int offset, int limit)
        {

            if (storeIds != null)
                return _dbset
                    .Where(x => (x.StoreID != null && storeIds.Contains(x.StoreID.Value)) || (x.StoreID == null && x.Account.StoreID != null && storeIds.Contains(x.Account.StoreID.Value)))
                    .Include(x => x.Store)
                    .Include(x => x.Account)
                    .Include(x => x.AccountCardType)
                    .AsNoTracking().OrderBy(x => x.PAN)
                    .Skip(offset)
                    .Take(limit)
                    .ToList();
            else if (tenantsId != null)
                return _dbset
                    .Where(x => (x.StoreID != null && tenantsId.Contains(x.Store.TenantID)) 
                                || (x.StoreID == null && tenantsId.Contains(x.Account.TenantID.Value)))
                    .Include(x => x.Store)
                    .Include(x => x.Account)
                    .Include(x => x.AccountCardType)
                    .AsNoTracking().OrderBy(x => x.PAN)
                    .Skip(offset)
                    .Take(limit)
                    .ToList();
            else
                return new List<AccountCard>();
        }

        public AccountCard? GetByPANOrBarCode(string PANOrBarcode)
        {
            return _dbset
                .Include(x => x.Discounts)
                .Include(x => x.Account)
                .Include(x => x.AccountCardType)
                .AsNoTracking()
                .FirstOrDefault(x => x.PAN == PANOrBarcode || x.BarCode == PANOrBarcode);
        }

        public new void Add(AccountCard entity)
        {
            if (entity.Store != null)
            {
                var store = _context.Set<Store>().AsNoTracking().Where(x => x.ID == entity.Store.ID).FirstOrDefault();
                if (store == null)
                    _context.Entry(entity.Store).State = EntityState.Added;
                else
                    _context.Entry(entity.Store).State = EntityState.Modified;
            }
            _dbset.Add(entity);
        }

        public new void Add(IEnumerable<AccountCard> entity)
        {
            List<Store> cache = new List<Store>();
            foreach (var item in entity)
            {
                if (item.Store != null)
                {
                    var store = _context.Set<Store>().AsNoTracking().Where(x => x.ID == item.Store.ID).FirstOrDefault();
                    if (store == null)
                        _context.Entry(item.Store).State = EntityState.Added;
                    else if ((store = cache.FirstOrDefault(x => x.ID.Equals(item.Store.ID))) != null)
                    {
                        item.Store = store;
                    }
                    else
                    {
                        _context.Entry(item.Store).State = EntityState.Modified;
                        cache.Add(item.Store);
                    }
                }
            }
            _dbset.AddRange(entity);
        }

        public new void Update(AccountCard current, AccountCard newModel)
        {
            if (current.Restrictions != null)
            {
                foreach(var restriction in current.Restrictions)
                {
                    _context.Entry(restriction).State = EntityState.Deleted;
                }
            }
            _context.Entry(current).CurrentValues.SetValues(newModel);
            current.Account = null;
            if (newModel.Restrictions != null)
            {
                current.Restrictions = newModel.Restrictions;
                foreach (var restriction in current.Restrictions)
                {
                    _context.Entry(restriction).State = EntityState.Added;
                }
            }

            if (newModel.Store != null)
            {
                current.Store = newModel.Store;
                var store = _context.Set<Store>().AsNoTracking().Where(x => x.ID == newModel.Store.ID).FirstOrDefault();
                if (store == null)
                    _context.Entry(current.Store).State = EntityState.Added;
                else
                    _context.Entry(current.Store).State = EntityState.Modified;
            }

            if (newModel.Discounts != null)
            {
                current.Discounts = newModel.Discounts;
                foreach (var d in current.Discounts)
                {
                    _context.Entry(d).State = EntityState.Modified;
                }
            }

            _dbset.Update(current);
        }
        
        public List<AccountCard> GetByAccount(Guid accountId)
        {
            return _dbset.AsNoTracking()
                .Include(x => x.AccountCardType)
                .Where(x => x.AccountId.Equals(accountId))
                .OrderBy(x => x.PAN)
                .ToList();
        }
        
        public AccountCard? GetByPAN(string PAN)
        {
            return _dbset
                //.Include(x => x.Account)
                //.ThenInclude(x => x.AccountVehicleRegistrations)
                //.ThenInclude(x => x.VehicleRegistration)
                .Include(x => x.Account)
                .ThenInclude(x => x.AccountBalances)
                //.Include(x => x.Restrictions)
                //.Include(x => x.Discounts)
                .Include(x => x.AccountCardType)
                .AsNoTracking().FirstOrDefault(x => x.PAN == PAN);
        }
        
        public AccountCard? GetByPAN19(string PAN)
        {
            return _dbset
                //.Include(x => x.Account)
                //.ThenInclude(x => x.AccountVehicleRegistrations)
                //.ThenInclude(x => x.VehicleRegistration)
                .Include(x => x.Account)
                .ThenInclude(x => x.AccountBalances)
                .Include(x => x.AccountCardType)
                .AsNoTracking().FirstOrDefault(x => x.PAN19 == PAN);
        }

        public List<AccountCardType> GetAccountCardTypes()
        {
            return _context.Set<AccountCardType>().ToList();
        }

        public bool Valid(Guid id, Guid storeId)
        {
            return _dbset.Any(x => x.Account.StoreID.Equals(storeId) && x.ID.Equals(id));
        }
    }
}
