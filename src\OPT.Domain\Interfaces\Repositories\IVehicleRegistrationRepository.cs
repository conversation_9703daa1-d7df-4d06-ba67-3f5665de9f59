﻿using OPT.Domain.Models;

namespace OPT.Domain.Interfaces.Repositories
{
    public interface IVehicleRegistrationRepository : IRepository<VehicleRegistration>
    {
        List<VehicleRegistration> GetByAccount(Guid accountId);

        VehicleRegistration? GetByRegistrationNumber(string registrationNumber);

        void AddAccount(AccountVehicleRegistration accountVehicleRegistration);

        void RemoveAccount(AccountVehicleRegistration accountVehicleRegistration);

        List<AccountVehicleRegistration> GetAccountByVehicleRegistration(Guid vehicleRegistrationId);

        bool Valid(Guid id, Guid storeId);
    }
}
