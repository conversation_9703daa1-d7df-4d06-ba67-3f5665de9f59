﻿using OPT.Domain.Exceptions;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;

namespace OPT.Domain.Services
{
    public class AccountCardService : IAccountCardService
    {
        private readonly IAccountCardRepository _repository;

        public AccountCardService(IAccountCardRepository repository)
        {
            _repository = repository;
        }

        public void Add(AccountCard card, Guid? storeId = null)
        {
            if (card.PIN != null && card.PIN.Length == 0)
                card.PIN = null;
            if(storeId != null)
                card.StoreID = storeId;
            _repository.Add(card);
        }
        
        public void Add(IEnumerable<AccountCard> cards, Guid? storeId = null)
        {
            foreach (var card in cards)
            {
                if (storeId != null)
                    card.StoreID = storeId;
                if (card.PIN != null && card.PIN.Length == 0)
                    card.PIN = null;
            }
            _repository.Add(cards);
        }

        public AccountCard? GetByPAN(string pan)
        {
            return _repository.Find(x => x.PAN == pan).FirstOrDefault();
        }

        public AccountCard? GetByPANExtended(string pan)
        {
            return _repository.GetByPAN(pan);
        }
        
        public AccountCard? GetByPAN19Extended(string pan)
        {
            return _repository.GetByPAN19(pan);
        }

        public AccountCard? GetByPANOrBarCode(string PANOrBarCode)
        {
            return _repository.GetByPANOrBarCode(PANOrBarCode);
        }

        public bool AssociateWithUser(Guid cardId, Guid? userId, string PIN)
        {
            var card = _repository.GetById(cardId);
            if (card != null)
            {
                if (card.UserId != userId.ToString())
                {
                    card.UserId = userId?.ToString();
                    if(PIN != null)
                        card.PIN = PIN;
                    //card.Active = true;
                    _repository.Update(card);
                    return true;
                }
                return false;
            }
            else
            {
                throw new BusinessException("Card does not exist.", 404);
            }
        }

        public void Update(AccountCard current, AccountCard cardUpdate)
        {
            if (cardUpdate.PIN != null && cardUpdate.PIN.Length == 0)
                cardUpdate.PIN = null;
            _repository.Update(current, cardUpdate);
        }

        public void Update(Guid id, AccountCard cardUpdate)
        {
            var card = _repository.GetById(id);
            if (card != null)
            {
                cardUpdate.ID = id;
                cardUpdate.UserId = card.UserId;
                cardUpdate.PIN = card.PIN;
                if (cardUpdate.AccountId == null && cardUpdate.StoreID == null)
                {
                    cardUpdate.AccountId = card.AccountId;
                    cardUpdate.Account = card.Account;
                }
                if(cardUpdate.StoreID == null)
                {
                    card.Store = null;
                }
                    
                if (cardUpdate.Discounts != null && card.Discounts != null)
                {
                    cardUpdate.Discounts.All(x =>
                    {
                        x.ID = card.Discounts.First(y => y.FuelGrade == x.FuelGrade).ID;
                        x.AccountCardID = id;
                        return true;
                    });
                }
                _repository.Update(card, cardUpdate);
            }
            else
            {
                throw new BusinessException("Card does not exist.", 404);
            }
        }

        public void Update(Guid id, AccountCard cardUpdate, Guid storeId)
        {
            if (_repository.Valid(id, storeId))
            {
                cardUpdate.StoreID = storeId;
                Update(id, cardUpdate);
            }
            else
            {
                throw new BusinessException("You are not authorized to update this AccountCard!", 401);
            }
        }

        public AccountCard? GetById(Guid id)
        {
            return _repository.GetById(id);
        }

        public AccountCard? GetById(Guid id, Guid storeId)
        {
            if (_repository.Valid(id, storeId))
            {
                return _repository.GetById(id);
            }
            else
            {
                throw new BusinessException("You are not authorized to get this Account Card!", 401);
            }
        }

        public AccountCard? GetByIdExtended(Guid id)
        {
            return _repository.GetByIdExtended(id);
        }

        public AccountCard? GetByDiscountId(Guid discountId)
        {
            return _repository.GetByDiscountId(discountId);
        }


        public void Delete(Guid id)
        {
            _repository.Remove(id);
        }

        public void Delete(Guid id, Guid storeId)
        {
            if (_repository.Valid(id, storeId))
            {
                Delete(id);
            }
            else
            {
                throw new BusinessException("You are not authorized to delete this AccountCard!", 401);
            }
        }

        public List<AccountCard> GetByStores(List<Guid>? storesId, List<Guid>? tenantsId)
        {
            return _repository.GetByStores(storesId, tenantsId);
        }
        
        public (int, List<AccountCard>) GetByStoresWithPagination(List<Guid>? storesId, List<Guid>? tenantsId, int offset, int limit)
        {
            var total = _repository.GetTotalByStores(storesId, tenantsId);
            return (total, _repository.GetByStoresWithPagination(storesId, tenantsId, offset, limit));
        }

        public List<AccountCard> GetByAccount(Guid accountId)
        {
            return _repository.GetByAccount(accountId);
        }

        public AccountCard? ResetPIN(Guid id, string PIN)
        {
            var card = _repository.GetById(id);
            if (card != null)
            {
                //removing the obligation of the account card to be associated with the user
                //if (!string.IsNullOrWhiteSpace(card.UserId))
                //{
                card.PIN = PIN;
                _repository.Update(card);
                return card;
                //}
            }
            else
            {
                throw new BusinessException("Card does not exist.", 404);
            }
        }

        public List<AccountCardRestriction> GetRestrictions(Guid cardId)
        {
            return _repository.GetById(cardId).Restrictions;
        }

        public List<AccountCardType> GetAccountCardTypes()
        {
            return _repository.GetAccountCardTypes();
        }

        public void Dispose()
        {
            _repository.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
