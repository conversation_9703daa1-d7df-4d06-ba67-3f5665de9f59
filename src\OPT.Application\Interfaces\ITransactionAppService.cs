﻿using OPT.Application.ViewModels;
using OPT.Application.ViewModels.Transaction;
using OPT.Domain.Models;

namespace OPT.Application.Interfaces
{
    
    public interface ITransactionAppService : IDisposable
    {
        void Add(TransactionAddViewModel transaction);
        List<TransactionUserViewModel> GetByUserId(string userId);

        List<UnitedTransactionFileViewModel> GetUnitedToExport(Guid tenantID);

        Task<List<TransactionAccountViewModel>> GetByFilter(TransactionFilterViewModel filter);

        Task<TransactionPagination> GetByFilterWithPagination(TransactionFilterV2ViewModel filter);

        //Task<TransactionReceiptViewModel> GetByTransactionId(int transactionId);
        Task SendTransactionReceipt(int transactionId);
        Task SendTransactionReceipt(CardReceiptViewModel cardReceipt, string email);

    }
}
