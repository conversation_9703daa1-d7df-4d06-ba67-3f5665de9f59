﻿using OPT.Domain.Models;

namespace OPT.Domain.Interfaces.Services
{
    public interface IAccountCardTransactionService : IDisposable
    {
        void Add(AccountCardTransaction cardTransaction);
        void Update(AccountCardTransaction cardTransaction);
        AccountCardTransaction? GetBySTANAndTerminalID(long STAN, string terminalId, long transactionReferece);

        List<AccountCardTransaction> GetByDate(Guid accountId, DateTime dateFrom, DateTime dateTo);
        
        AccountCardTransaction? GetByAuthCodeSTANAndTerminalID(string authCode, long STAN, string terminalId);
    }
}
