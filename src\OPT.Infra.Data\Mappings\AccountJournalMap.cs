﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class AccountJournalMap : Mapping<AccountJournal>
    {
        public override void Map(EntityTypeBuilder<AccountJournal> builder)
        {
            builder.ToTable("AccountJournals");

            builder.HasKey(x => x.ID);

            //builder.HasOne(x => x.Account).WithMany().HasForeignKey(x => x.AccountId).OnDelete(DeleteBehavior.NoAction);

            base.Map(builder);
        }
    }
}
