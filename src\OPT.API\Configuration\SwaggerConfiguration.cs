﻿using Microsoft.OpenApi.Models;
using OPT.API.Attributes;
using OPT.API.Filters;
using System.Reflection;
using static Org.BouncyCastle.Math.EC.ECCurve;

namespace OPT.API.Configuration
{
    public static class SwaggerConfiguration
    {
        public static void AddSwaggerSetup(this IServiceCollection services)
        {
            if (services == null) throw new ArgumentNullException(nameof(services));

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("public", new OpenApiInfo
                {
                    Title = "OPT API Reference",
                    Version = "v1",
                    Description = @"## Introduction
Welcome to the official documentation of the OPT API. This API provides secure and efficient access to create and manage information related to accounts, balances, associated vehicles, cards, and transactions.

## Overview

This API has been developed to streamline the process of registering accounts, checking balances, managing vehicles linked to accounts, controlling associated cards, and transactions. With a simple structure and intuitive endpoints, you can seamlessly integrate this API into your existing applications and systems.

## Authentication

To begin using the API, authentication is required using access tokens. Access tokens must be generated from an API Key created in `POSMaster -> OPT -> API Key Management`. Details on how to obtain and use access tokens will be provided in the API Key section.

## Errors
The APIs use standard HTTP status codes to indicate success or failure.All error responses will have a JSON body in the following format:

```yaml
{
""success"": false,
""message"": ""error message""
}
```

- `success`: False = Error / True = Success
- `message`: Error description/ Bussiness success message
"
                });

                c.SwaggerDoc("v1", new OpenApiInfo { Title = "OPT API", Version = "v1" });
                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                c.IncludeXmlComments(xmlPath, includeControllerXmlComments: true);
                c.DocumentFilter<LowercaseDocumentFilter>();
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                {
                    Name = "Authorization",
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer",
                    BearerFormat = "JWT",
                    In = ParameterLocation.Header,
                    Description = "JWT Authorization header using the Bearer scheme. \r\n\r\n Enter 'Bearer' [space] and then your token in the text input below.\r\n\r\nExample: \"Bearer 1safsfsdfdfd\"",
                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement {
                    {
                        new OpenApiSecurityScheme {
                            Reference = new OpenApiReference {
                                Type = ReferenceType.SecurityScheme,
                                    Id = "Bearer"
                            }
                        },
                        new string[] {}
                    }
                });
                c.CustomSchemaIds(type =>
                {
                    string returnedValue = type.Name;
                    if (returnedValue.EndsWith("ViewModel"))
                        returnedValue = returnedValue.Replace("ViewModel", string.Empty);
                    return returnedValue;
                });
                c.DocumentFilter<OrderTagsDocumentFilter>();
                c.EnableAnnotations();
            });
        }
    }
}
