﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Models;
using OPT.Infra.Data.Context;
using OPT.Infra.Data.Helper;
using System.Data;

namespace OPT.Infra.Data.Repositories
{
    public class TransactionRepository : Repository<Transaction>, ITransactionRepository
    {
        private readonly IEmailSenderHelper _emailSender;

        public TransactionRepository(OPTContext context, IEmailSenderHelper emailSender) : base(context)
        {
            _emailSender = emailSender;
        }

        public List<Transaction> GetByUser(string userId)
        {

            return _dbset
                .Include(x => x.Store)
                .Include(x => x.Kiosk)
                .AsNoTracking()
                .Where(x => x.UserId == userId)
                .OrderByDescending(x=>x.TransactionDateTime)
                .ToList();
        }

        public List<Transaction> GetByFilter(Guid? accountId, Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId)
        {
            return _dbset
                .Include(x => x.Store)
                .Include(x => x.Kiosk)
                .Include(x => x.FuelTransaction)
                .Include(x => x.Account)
                .Include(x => x.AccountCard)
                .Include(x => x.RewardCardTransactions)
                .ThenInclude(x => x.RewardCardDiscount)
                .ThenInclude(x => x.AccountCard)
                .AsNoTracking()
                .Where(x => (accountId == null || x.AccountId == accountId)
                    && (kioskId == null || x.KioskId == kioskId)
                    && (dateFrom == null || x.TransactionDateTime.Date >= dateFrom.Value.Date) 
                    && (dateTo == null || x.TransactionDateTime.Date <= dateTo.Value.Date)
                    && (storesId == null || storesId.Contains(x.StoreId)))
                .OrderByDescending(x => x.TransactionDateTime)
                .ToList();
        }
        
        public List<Transaction> GetByFilterWithPagination(Guid? accountId, Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId, int offset, int limit)
        {
            return _dbset
                .Include(x => x.Store)
                .Include(x => x.Kiosk)
                .Include(x => x.FuelTransaction)
                .Include(x => x.Account)
                .Include(x => x.AccountCard)
                .Include(x => x.RewardCardTransactions)
                .ThenInclude(x => x.RewardCardDiscount)
                .ThenInclude(x => x.AccountCard)
                .AsNoTracking()
                .Where(x => (accountId == null || x.AccountId == accountId)
                    && (kioskId == null || x.KioskId == kioskId)
                    && (dateFrom == null || x.TransactionDateTime.Date >= dateFrom.Value.Date) 
                    && (dateTo == null || x.TransactionDateTime.Date <= dateTo.Value.Date)
                    && (storesId == null || storesId.Contains(x.StoreId)))
                .OrderByDescending(x => x.TransactionDateTime)
                .Skip(offset)
                .Take(limit)
                .ToList();
        }
        
        public int GetTotalByFilter(Guid? accountId, Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId)
        {
            return _dbset
                .AsNoTracking()
                .Where(x => (accountId == null || x.AccountId == accountId)
                    && (kioskId == null || x.KioskId == kioskId)
                    && (dateFrom == null || x.TransactionDateTime.Date >= dateFrom.Value.Date) 
                    && (dateTo == null || x.TransactionDateTime.Date <= dateTo.Value.Date)
                    && (storesId == null || storesId.Contains(x.StoreId)))
                .Count();
        }
        
        public List<FuelTransaction> GetTotalGradeByFilter(Guid? accountId, Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId)
        {
            return _dbset
                .AsNoTracking()
                //.Include(x => x.FuelTransaction)
                .Where(x => (accountId == null || x.AccountId == accountId)
                    && (kioskId == null || x.KioskId == kioskId)
                    && (dateFrom == null || x.TransactionDateTime.Date >= dateFrom.Value.Date) 
                    && (dateTo == null || x.TransactionDateTime.Date <= dateTo.Value.Date)
                    && (storesId == null || storesId.Contains(x.StoreId)))
                .GroupBy(x => x.FuelTransaction.FuelGradeId)
                .Select(x => new FuelTransaction
                {
                    FuelAmount = x.Sum(y => (int)(y.FinalAmount * 100)),
                    FuelVolume = x.Sum(y => y.FuelTransaction.FuelVolume),
                    FuelGradeId = x.First().FuelTransaction.FuelGradeId,
                    FuelGradeName = x.First().FuelTransaction.FuelGradeName
                })
                .ToList();
        }
        
        public List<Transaction> GetUnitedToExport(Guid tenantID)
        {
            return _dbset
                .Include(x => x.Store)
                .Include(x => x.FuelTransaction)
                .Include(x => x.AccountCard)
                .Include(x => x.RewardCardTransactions)
                .ThenInclude(x => x.RewardCardDiscount)
                .ThenInclude(x => x.AccountCard)
                .AsNoTracking()
                .Where(x => x.Store.TenantID.Equals(tenantID) 
                            //&& (x.AccountCardId != null 
                            //|| x.RewardCardTransactions.Any())
                            && !x.UnitedExported)
                .OrderBy(x => x.TransactionDateTime)
                .ToList();
        }

        public void SetUnitedExported(IEnumerable<Guid> ids)
        {
            var parameterNames = ids.Select((id, index) => $"@id{index}").ToArray();
            var parameters = ids.Select((id, index) => new SqlParameter($"@id{index}", SqlDbType.UniqueIdentifier) { Value = id }).ToArray();

            var parameterString = string.Join(", ", parameterNames);
            var query = $"UPDATE [dbo].[Transactions] SET [UnitedExported] = 1 WHERE [ID] IN ({parameterString})";

            _context.Database.ExecuteSqlRaw(query, parameters);
        }

        public Transaction GetByTransaction(int transactionId)
        {
            return _dbset
                .Include(x => x.Store)
                .Include(x => x.Kiosk)
                .Include(x => x.Account)
                .Include(x => x.FuelTransaction)
                .AsNoTracking()
                .Where(x => x.TerminalTransactionId == transactionId).First();
        }
        
        public Transaction? GetByTransactionIdAndKiosk(int transactionId, Guid kioskId)
        {
            return _dbset
                .Include(x => x.Store)
                .Include(x => x.Kiosk)
                .Include(x => x.Account)
                .Include(x => x.FuelTransaction)
                .AsNoTracking()
                .Where(x => x.TerminalTransactionId == transactionId && x.KioskId.Equals(kioskId))
                .OrderByDescending(x => x.TransactionDateTime).FirstOrDefault();
        }
        
        public Transaction? GetByTransactionIdAndTransactionDate(int transactionId, DateTime transactionDate)
        {
            return _dbset
                .AsNoTracking()
                .Where(x => x.TerminalTransactionId == transactionId && x.TransactionDateTime == transactionDate)
                .OrderByDescending(x => x.TransactionDateTime).FirstOrDefault();
        }

        public async Task SendEmail(string email, string subject, string htmlMessage, params AttachmentModel[] attachments)
        {
            await _emailSender.SendEmailAsync(
                    email,
                    subject,
                    htmlMessage,
                    attachments);
        }

    }
}
