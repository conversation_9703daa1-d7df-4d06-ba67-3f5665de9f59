﻿using OPT.Application.ViewModels.Account;
using OPT.Application.ViewModels.RewardCard;
using OPT.Application.ViewModels.Store;
using OPT.Application.ViewModels.User;

namespace OPT.Application.ViewModels.AccountCard
{
    public class AccountCardInternalViewModel
    {
        public Guid ID { get; set; }

        public required string PAN { get; set; }

        public required string ExpiryDate { get; set; }

        public bool Active { get; set; }

        public decimal? TransactionLimit { get; set; }

        public Guid? AccountId { get; set; }

        public AccountUserViewModel? Account { get; set; }

        public UserCardAccountViewModel? User { get; set; }

        public string? PAN19 { get; set; }

        public string? BarCode { get; set; }

        public string? HolderName { get; set; }

        public char Type { get; set; }

        public AccountCardTypeViewModel? AccountCardType { get; set; }

        public string? Status { get; set; }

        public string? VehicleOdometer { get; set; }

        public Guid? StoreID { get; set; }

        public StoreViewModel? Store { get; set; }

        public List<RewardCardDiscountViewModel>? Discounts { get; set; }

        public List<AccountCardRestrictionViewModel>? Restrictions { get; set; }
    }
}
