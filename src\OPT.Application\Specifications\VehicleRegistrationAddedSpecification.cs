﻿using OPT.Application.ViewModels.VehicleRegistration;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class VehicleRegistrationAddedSpecification : ISpecificationConfiguration<VehicleRegistrationAddViewModel>
    {
        private readonly IVehicleRegistrationService _service;

        public VehicleRegistrationAddedSpecification(IVehicleRegistrationService service)
        {
            _service = service;
        }

        public ISpecification<VehicleRegistrationAddViewModel> Map(ISpecification<VehicleRegistrationAddViewModel> builder)
        {
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.RegistrationNumber), "The Registration Number is incorrect!", 412);
            
            builder.IsSatisfiedBy(x => _service.GetByRegistrationNumber(x.RegistrationNumber) == null, "This registration number already exists.", 409);

            return builder;
        }
    }
}
