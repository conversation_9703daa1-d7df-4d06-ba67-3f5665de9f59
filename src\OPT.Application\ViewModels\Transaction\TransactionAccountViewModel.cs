﻿
using OPT.Application.ViewModels.AccountCard;
using OPT.Application.ViewModels.FuelTransaction;
using OPT.Application.ViewModels.RewardCard;
using OPT.Application.ViewModels.User;
using OPT.Domain.Models;

namespace OPT.Application.ViewModels.Transaction
{
    public class TransactionAccountViewModel
    {
        public string StoreName { get; set; }
        public string StoreCode { get; set; }
        public string KioskName { get; set; }
        public int TerminalTransactionId { get; set; }

        public DateTime TransactionDateTime { get; set; }
        public decimal FinalAmount { get; set; }
        public decimal GSTAmount { get; set; }
        public string? CardSignature { get; set; }
        public FuelTransactionAccountViewModel FuelTransaction { get; set; }
        
        public string VehicleRegistration { get; set; }
        public string VehicleOdometer { get; set; }
        public string AccountName { get; set; }
        public int Currency { get; set; }
        public string? AccountType { get; set; }
        public UserAccountViewModel User { get; set; }

        public AccountCardTransactionAccountViewModel? AccountCard { get; set; }

        public List<RewardCardDiscountTransactionViewModel> RewardCardTransactions { get; set; }
    }
}
