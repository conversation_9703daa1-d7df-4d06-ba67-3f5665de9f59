﻿using OPT.Domain.Models;

namespace OPT.Domain.Interfaces.Services
{
    public interface IApiKeyService : IDisposable
    {
        void Add(ApiKey model, string accessToken);

        void UpdateAccessToken(Guid id, string accessToken);

        void UpdateActive(Guid id, bool ative);

        List<ApiKey> GetByStores(List<Guid> storesId);

        ApiKey? GetByAccessToken(string accessToken);

        void Remove(Guid id);
    }
}
