﻿namespace OPT.Domain.Models
{
    public class AccountCard
    {
        public Guid ID { get; set; }

        public string PAN { get; set; }

        public string ExpiryDate { get; set; }

        public string? PIN { get; set; }

        public bool Active { get; set; }

        public decimal? TransactionLimit { get; set; }

        public Guid? AccountId { get; set; }

        public Account? Account { get; set; }

        public string? UserId { get; set; }

        public User? User { get; set; }

        public string? PAN19 { get; set; }

        public string? BarCode { get; set; }

        public string? HolderName { get; set; }

        public char Type { get; set; }

        public AccountCardType AccountCardType { get; set; }

        public string? Status { get; set; }

        public string? VehicleOdometer { get; set; }

        //public Guid? AccountCardID { get; set; }

        public Guid? StoreID { get; set; }

        public Store? Store { get; set; }

        public List<AccountCardRestriction> Restrictions { get; set; }

        public List<RewardCardDiscount> Discounts { get; set; }
    }
}
