﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.Specifications;
using OPT.Application.ViewModels.Account;
using OPT.Application.ViewModels.User;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Interfaces.UoW;
using OPT.Domain.Models;
using OPT.Domain.Specification;

namespace OPT.Application
{
    public class UserAppService : ApplicationService, IUserAppService
    {
        private readonly IIdentityService _service;
        private readonly IAccountService _accountService;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;

        public UserAppService(IUnitOfWork unitOfWork,
                                IMapper mapper,
                                IConfiguration configuration,
                                IIdentityService service,
                                IAccountService accountService) : base(unitOfWork)
        {
            _service = service;
            _mapper = mapper;
            _accountService = accountService;
            _configuration = configuration;
        }

        public async Task Add(UserAddViewModel user)
        {
            var spec = new UserAddedSpecification(_service);
            if (spec.IsValid(user))
            {
                var companyCode = RandomHelper.RandomString(6);
                var useradd = _mapper.Map<User>(user);
                useradd.CompanyHash = CryptographyHelper.Encrypt(companyCode);
                var id = await _service.Add(useradd);
                if (id != null)
                {
                    _accountService.AddOrUpdateUser(id.Value, _mapper.Map<UserAccount>(user));
                    Commit();
                    await _service.SendEmail(user.UserName, "OPT Registration", HtmlTemplateHelper.GetWelcomeEmailTemplate(user.UserName, companyCode));
                }
            }
        }

        public async Task Update(UserUpdateViewModel user)
        {
            var spec = new UserUpdatedSpecification(_service);
            if (spec.IsValid(user))
            {
                await _service.Update(_mapper.Map<User>(user));
                //await _service.UpdateRole(user.Id, user.Role);
                _accountService.AddOrUpdateUser(user.Id, _mapper.Map<UserAccount>(user));
                Commit();
            }
        }

        public async Task<string> ConfirmEmail(ConfirmEmailViewModel confirmEmail)
        {
            if (new ConfirmEmailSpecification(_service).IsValid(confirmEmail))
            {
                return await _service.ConfirmEmail(confirmEmail.Email);
            }
            return string.Empty;
        }
        
        public async Task ResetPassword(ResetPasswordViewModel resetPassword)
        {
            if (resetPassword.IsValid<ResetPasswordViewModel, ResetPasswordSpecification>())
            {
                await _service.ResetPassword(resetPassword.Id, resetPassword.Email, resetPassword.PassToken, resetPassword.NewPassword);
            }
        }

        public async Task Remove(Guid id)
        {
            _accountService.RemoveUser(id);
            Commit();
            await _service.Remove(id);
        }

        public async Task<List<UserViewModel>> GetByAccount(Guid accountId)
        {
            var useraccounts = _accountService.GetUsers(accountId);
            if (useraccounts != null && useraccounts.Count > 0)
                return _mapper.Map<List<UserViewModel>>(await _service.GetUsersByIds(useraccounts.Select(x => x.UserID).ToList()));
            else
                return new List<UserViewModel>();
        }
        
        public async Task<UserViewModel> GetById(Guid id)
        {
            return _mapper.Map<UserViewModel>(await _service.GetById(id));
        }
        
        public async Task<UserKioskViewModel> GetByIdKiosk(Guid id)
        {
            var user = _mapper.Map<UserKioskViewModel>(await _service.GetById(id));
            if (user.Active)
            {
                var account = _mapper.Map<AccountUserViewModel>(_accountService.GetByUser(id));
                if (account != null)
                    user.Account = account;
            }
            return user;
        }

        public async Task<UserSessionViewModel> Authenticate(AuthenticateViewModel authenticateViewModel)
        {
            if(authenticateViewModel.IsValid<AuthenticateViewModel, AuthenticateSpecification>()){
                var user = await _service.Authenticate(authenticateViewModel.Username, authenticateViewModel.Password);
                if (user != null)
                {
                    return new UserSessionViewModel
                    {
                        ID = user.Id,
                        Token = TokenHelper.GetToken(user, _configuration)
                    };
                }
            }
            return null;
        }

        public async Task ForgotPassword(string userName)
        {
            if (userName.IsValid<string, ForgotPasswordSpecification>())
            {
                var companyCode = RandomHelper.RandomString(6);
                var companyHash = CryptographyHelper.Encrypt(companyCode);
                var success = await _service.ForgotPassword(userName, companyHash);
                if (success)
                {
                    await _service.SendEmail(userName, "OPT Forgot Password", HtmlTemplateHelper.GetForgotPasswordTemplate(companyCode));
                }
            }
        }

        /*public IEnumerable<UserViewModel> GetAll()
        {
            var user = _httpContextAccessor?.HttpContext?.GetUser();
            if (user != null)
            {
                return _mapper.Map<IEnumerable<UserViewModel>>(_service.GetAll());
            }
            return null;
        }*/

        public void Dispose()
        {
            _service.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
