﻿using Microsoft.EntityFrameworkCore;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Models;
using OPT.Infra.Data.Context;

namespace OPT.Infra.Data.Repositories
{
    public class AccountRepository : Repository<Account>, IAccountRepository
    {
        private readonly DbSet<UserAccount> _dbsetUserAccount;

        public AccountRepository(OPTContext context) : base(context)
        {
            _dbsetUserAccount = context.Set<UserAccount>();
        }

        public void RemoveUser(Guid userId)
        {
            var userAccount = _dbsetUserAccount.Where(x => x.UserID == userId.ToString().ToLower());
            _dbsetUserAccount.RemoveRange(userAccount);
        }

        public void AddOrUpdateUser(UserAccount entity)
        {
            if (entity.Account?.Store != null)
            {
                var store = _context.Set<Store>().AsNoTracking().Where(x => x.ID == entity.Account.Store.ID).FirstOrDefault();
                if (store == null)
                    _context.Entry(entity.Account.Store).State = EntityState.Added;
                else
                    _context.Entry(entity.Account.Store).State = EntityState.Modified;
            }
            if (entity.Account != null)
            {
                var account = _dbset.AsNoTracking().Where(x => x.ID == entity.Account.ID).FirstOrDefault();
                if (account == null)
                    _context.Entry(entity.Account).State = EntityState.Added;
                else
                    _context.Entry(entity.Account).State = EntityState.Modified;
            }
            var userAccount = _dbsetUserAccount.AsNoTracking().Where(x => x.UserID == entity.UserID).FirstOrDefault();
            if (userAccount == null)
                _dbsetUserAccount.Add(entity);
            else
            {
                entity.ID = userAccount.ID;
                _dbsetUserAccount.Update(entity);
            }
        }

        public new void Add(Account entity)
        {
            if (entity.Store != null)
            {
                var store = _context.Set<Store>().AsNoTracking().Where(x => x.ID == entity.Store.ID).FirstOrDefault();
                if (store == null)
                    _context.Entry(entity.Store).State = EntityState.Added;
                else
                    _context.Entry(entity.Store).State = EntityState.Modified;
            }
            _dbset.Add(entity);
        }

        public new void Update(Account current, Account updated)
        {
            _context.Entry(current).CurrentValues.SetValues(updated);
            if (updated.Store != null)
            {
                current.Store = updated.Store;
                var store = _context.Set<Store>().AsNoTracking().Where(x => x.ID == updated.Store.ID).FirstOrDefault();
                if (store == null)
                    _context.Entry(current.Store).State = EntityState.Added;
                else
                    _context.Entry(current.Store).State = EntityState.Modified;
            }
            _context.Entry(current).State = EntityState.Modified; 
        }

        public List<UserAccount> GetUsers(Guid id)
        {
            return _dbsetUserAccount.AsNoTracking().Where(x => x.AccountID == id).ToList();
        }

        public new Account? GetById(params object[] ids)
        {
            return _dbset.AsNoTracking().Include(x => x.Store).FirstOrDefault(x => ids.Contains(x.ID));
        }

        public Account? GetByUser(Guid userId)
        {
            return _dbsetUserAccount.AsNoTracking().Include(x => x.Account).FirstOrDefault(x => x.UserID.ToLower() == userId.ToString().ToLower())?.Account;
        }

        public List<Account> GetByStores(List<Guid>? storesId, List<Guid>? tenantsId)
        {
            if (storesId != null)
                return _dbset.Where(x => x.StoreID != null && storesId.Contains(x.StoreID.Value)).Include(x => x.Store).AsNoTracking().ToList();
            else if (tenantsId != null)
                return _dbset.Where(x => x.TenantID != null && tenantsId.Contains(x.TenantID.Value)).Include(x => x.Store).AsNoTracking().ToList();
            else
                return new List<Account>();
        }
        
        public int GetTotalByStores(List<Guid>? storesId, List<Guid>? tenantsId)
        {
            if (storesId != null)
                return _dbset.Where(x => x.StoreID != null && storesId.Contains(x.StoreID.Value)).Include(x => x.Store).AsNoTracking().Count();
            else if (tenantsId != null)
                return _dbset.Where(x => x.TenantID != null && tenantsId.Contains(x.TenantID.Value)).Include(x => x.Store).AsNoTracking().Count();
            else
                return 0;
        }
        
        public List<Account> GetByStoresWithPagination(List<Guid>? storesId, List<Guid>? tenantsId, int offset, int limit)
        {
            if (storesId != null)
                return _dbset.Where(x => x.StoreID != null && storesId.Contains(x.StoreID.Value)).Include(x => x.Store).AsNoTracking().OrderBy(x => x.Name).Skip(offset)
                .Take(limit).ToList();
            else if (tenantsId != null)
                return _dbset.Where(x => x.TenantID != null && tenantsId.Contains(x.TenantID.Value)).Include(x => x.Store).AsNoTracking().OrderBy(x => x.Name).Skip(offset)
                .Take(limit).ToList();
            else
                return new List<Account>();
        }

        public AccountBalance? GetBalance(Guid accountId)
        {
            return _dbset.Include(x => x.AccountBalances).AsNoTracking().FirstOrDefault(x => x.ID.Equals(accountId))?.AccountBalances?.FirstOrDefault();
        }
        
        public Account? GetByCode(string code)
        {
            return _dbset.Include(x => x.AccountBalances).AsNoTracking().FirstOrDefault(x => x.Code == code);
        }

        public void addJournal(AccountJournal accountJournal)
        {
            _context.Set<AccountJournal>().Add(accountJournal);
        }
        
        public List<AccountJournal> GetJournalsByDate(Guid accountId, DateTime dateFrom, DateTime dateTo)
        {
            return _context.Set<AccountJournal>()
                .AsNoTracking()
                .Where(x => x.AccountId.Equals(accountId) && x.JournalDate.Date >= dateFrom.Date && x.JournalDate.Date <= dateTo.Date)
                .ToList();
        }

        public void UpdateBalance(Guid accountId, decimal amount)
        {
            var balance = GetBalance(accountId);
            if (balance == null)
            {
                balance = new AccountBalance();
                balance.AccountId = accountId;
                balance.LastBalanceDate = DateTime.Now;
                balance.Amount = amount;
                _context.Set<AccountBalance>().Add(balance);
            }
            else
            {
                balance.LastBalanceDate = DateTime.Now;
                balance.Amount = balance.Amount + amount;
                _context.Set<AccountBalance>().Update(balance);
            }
        }
    }
}
