﻿using OPT.Application.ViewModels.User;

namespace OPT.Application.Interfaces
{
    public interface IUserAppService : IDisposable
    {
        Task Add(UserAddViewModel user);

        Task Update(UserUpdateViewModel user);

        Task<string> ConfirmEmail(ConfirmEmailViewModel confirmEmail);

        Task ResetPassword(ResetPasswordViewModel resetPassword);

        Task Remove(Guid id);

        Task<List<UserViewModel>> GetByAccount(Guid accountId);

        Task<UserViewModel> GetById(Guid id);

        Task<UserKioskViewModel> GetByIdKiosk(Guid id);

        Task<UserSessionViewModel> Authenticate(AuthenticateViewModel authenticateViewModel);

        Task ForgotPassword(string userName);

        //IEnumerable<UserViewModel> GetAll();
    }
}
