﻿using Microsoft.EntityFrameworkCore;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Models;
using OPT.Infra.Data.Context;

namespace OPT.Infra.Data.Repositories
{
    public class ApiKeyRepository : Repository<ApiKey>, IApiKeyRepository
    {
        public ApiKeyRepository(OPTContext context) : base(context)
        {
        }

        public new ApiKey? GetById(params object[] ids)
        {
            return _dbset.Include(x => x.Store).AsNoTracking().FirstOrDefault(x => ids.Contains(x.ID));
        }

        public new void Add(ApiKey entity)
        {
            if (entity.Store != null)
            {
                var store = _context.Set<Store>().AsNoTracking().Where(x => x.ID == entity.Store.ID).FirstOrDefault();
                if (store == null)
                    _context.Entry(entity.Store).State = EntityState.Added;
                else
                    _context.Entry(entity.Store).State = EntityState.Modified;
            }
            _dbset.Add(entity);
        }
        
        public new void Update(ApiKey entity)
        {
            if (entity.Store != null)
            {
                var store = _context.Set<Store>().AsNoTracking().Where(x => x.ID == entity.Store.ID).FirstOrDefault();
                if (store == null)
                    _context.Entry(entity.Store).State = EntityState.Added;
                else
                    _context.Entry(entity.Store).State = EntityState.Modified;
            }
            _dbset.Update(entity);
        }

        public List<ApiKey> GetByStores(List<Guid> storesId)
        {
            return _dbset.Where(x => storesId.Contains(x.StoreID)).Include(x => x.Store).AsNoTracking().ToList();
        }

        public new void Update(ApiKey current, ApiKey updated)
        {
            if (updated.Store != null)
            {
                var store = _context.Set<Store>().AsNoTracking().Where(x => x.ID == updated.Store.ID).FirstOrDefault();
                if (store == null)
                    _context.Entry(updated.Store).State = EntityState.Added;
                else
                    _context.Entry(updated.Store).State = EntityState.Modified;
            }
            _context.Entry(current).CurrentValues.SetValues(updated);
            _context.Entry(current).State = EntityState.Modified;
        }
    }
}
