﻿using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using OPT.Domain.Interfaces.Repositories;

namespace OPT.Infra.Data.Repositories
{
    public class Repository<TEntity> : IRepository<TEntity> where TEntity : class
    {
        protected DbContext _context;
        protected DbSet<TEntity> _dbset;

        protected Repository(DbContext context)
        {
            _context = context;
            _dbset = _context.Set<TEntity>();
        }

        public void Add(TEntity entity)
        {
            _dbset.Add(entity);
        }

        public void Add(IEnumerable<TEntity> entity)
        {
            _dbset.AddRange(entity);
        }

        public TEntity? GetById(params object[] ids)
        {
            return _dbset.Find(ids);
        }

        public IEnumerable<TEntity> GetAll()
        {
            return _dbset.AsNoTracking().ToList();
        }

        public void Update(TEntity entity)
        {
            _context.Update(entity);
        }

        public void Update(IEnumerable<TEntity> entities)
        {
            _context.UpdateRange(entities);
        }

        public void Update(TEntity current, TEntity updated)
        {
            _context.Entry(current).CurrentValues.SetValues(updated);
        }

        public void Remove(params object[] ids)
        {
            _context.Remove(GetById(ids));
        }

        public void Remove(TEntity entity)
        {
            _context.Remove(entity);
        }

        public void Remove(IEnumerable<TEntity> entities)
        {
            _context.RemoveRange(entities);
        }

        public IEnumerable<TEntity> Find(Expression<Func<TEntity, bool>> predicate)
        {
            return _dbset.AsNoTracking().Where(predicate);
        }

        public int SaveChanges()
        {
            return _context.SaveChanges();
        }

        public void Dispose()
        {
            _context.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
