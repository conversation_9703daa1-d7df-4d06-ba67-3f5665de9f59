﻿using OPT.Application.ViewModels.Account;

namespace OPT.Application.ViewModels.User
{
    public class UserUpdateViewModel
    {
        public Guid Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public string Role { get; set; }
        public bool Active { get; set; }

        public AccountInternalViewModel Account { get; set; }
    }
}
