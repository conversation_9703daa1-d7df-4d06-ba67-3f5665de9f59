﻿using OPT.Application.ViewModels.AccountCard;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Specification.Interface;
using System.Globalization;

namespace OPT.Application.Specifications
{
    internal class AccountCardInternalAddedSpecification : ISpecificationConfiguration<AccountCardInternalAddViewModel>
    {
        IAccountCardService accountCardService;

        public AccountCardInternalAddedSpecification(IAccountCardService accountCardService)
        {
            this.accountCardService = accountCardService;
        }

        public ISpecification<AccountCardInternalAddViewModel> Map(ISpecification<AccountCardInternalAddViewModel> builder)
        {
            builder.SetStopOnFirstFailure(true);

            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.PAN) && x.PAN.Length == 16 && x.PAN.All(c => char.IsNumber(c)), "The PAN number is incorrect!", 412);
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.PAN) && accountCardService.GetByPAN(x.PAN) == null , "Registered PAN number already exists!", 409);
            builder.IsSatisfiedBy(x => string.IsNullOrEmpty(x.PIN) || (!string.IsNullOrEmpty(x.PIN) && x.PIN.Length == 4 && x.PIN.All(c => char.IsNumber(c))), "PIN must have 4 digits!", 412);
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.ExpiryDate) && x.ExpiryDate.Length == 4 && x.ExpiryDate.All(c => char.IsNumber(c)), "The Expiry Date is incorrect!", 412);
            builder.IsSatisfiedBy(x => {
                var culture = new CultureInfo("en-AU");
                culture.Calendar.TwoDigitYearMax = 2099;
                if (!string.IsNullOrEmpty(x.ExpiryDate) && DateTime.TryParseExact(x.ExpiryDate, "yyMM", culture, System.Globalization.DateTimeStyles.None, out DateTime expirationDate))
                {
                    if (expirationDate >= DateTime.Now.Date.AddDays(-DateTime.Now.Day + 1))
                    {
                        return true;
                    }
                }
                return false;
            }, "The Expiry Date is invalid!", 412);
            builder.IsSatisfiedBy(x => x.AccountId != null || (x.AccountId == null && x.Store != null), "Store is required!");

            return builder;
        }
    }
}
