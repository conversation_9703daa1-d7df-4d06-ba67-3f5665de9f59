﻿using OPT.Application.ViewModels.Kiosk;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class KioskAddSpecification : ISpecificationConfiguration<KioskAddViewModel>
    {
        public ISpecification<KioskAddViewModel> Map(ISpecification<KioskAddViewModel> builder)
        {
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.Name), "Name is required!");
            builder.IsSatisfiedBy(x => x.Store != null, "Store is required!");

            return builder;
        }
    }
}
