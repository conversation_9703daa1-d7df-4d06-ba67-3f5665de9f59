﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels.Account;
using OPT.Application.ViewModels.AccountCard;
using System.IO.Compression;

namespace OPT.Scheduler.Workers
{
    public class UnitedImporterWorker : BackgroundService
    {
        private readonly ILogger<UnitedImporterWorker> _logger;
        private readonly IConfiguration _configuration;
        private readonly IAccountAppService _accountAppService;
        private readonly IAccountCardAppService _accountCardAppService;

        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        public UnitedImporterWorker(ILogger<UnitedImporterWorker> logger, 
                                        IConfiguration configuration, 
                                        IAccountAppService accountAppService,
                                        IAccountCardAppService accountCardAppService)
        {
            _logger = logger;
            _configuration = configuration;
            _accountAppService = accountAppService;
            _accountCardAppService = accountCardAppService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                DateTime nextRunTime = DateTime.Today.AddDays(1).AddHours(1); //Execute 1AM

                TimeSpan delay = nextRunTime - DateTime.Now;

                _logger.LogInformation("UnitedImporterWorker running at: {time}", DateTimeOffset.Now);
                try
                {
                    await _semaphore.WaitAsync(stoppingToken);

                    Proccess();
                }
                catch (Exception ex)
                {
                    _logger.LogError("UnitedImporterWorker running at: {time}\nException: {exception}", DateTimeOffset.Now, ex);
                }
                finally
                {
                    _semaphore.Release();
                }

                //await Task.Delay(int.Parse(_configuration["UnitedSettings:ImportPeriod"]), stoppingToken);
                await Task.Delay(delay, stoppingToken);
            }
        }

        private void Proccess()
        {
            using (var ftpClient = new FTPClientHelper(_configuration["FTPSettings:Host"], 
                                                        int.Parse(_configuration["FTPSettings:Port"]), 
                                                        _configuration["FTPSettings:User"], 
                                                        _configuration["FTPSettings:Password"], 
                                                        _configuration["FTPSettings:InFolder"], 
                                                        _configuration["FTPSettings:OutFolder"])) {
                ftpClient.LoadFilesByExtension(FTPClientHelper.ZIP);
                bool first = true;
                while (ftpClient.HasFile())
                {
                    if (first)
                    {
                        _logger.LogInformation("New zip file found: {0}", ftpClient.FileName());

                        string destDir = _configuration["FileSettings:TempFolder"];
                        string fileName = ftpClient.FileName();

                        deleteTemp(destDir);

                        ftpClient.DownloadFile(destDir);

                        ProccessFile(destDir, fileName);

                        ftpClient.DeleteFile();

                        first = false;
                    }
                    else
                    {
                        ftpClient.DeleteFile();
                    }
                }
            }
        }

        private void deleteTemp(string destDir)
        {
            Directory.CreateDirectory(destDir);
            DirectoryInfo di = new DirectoryInfo(destDir);
            foreach (FileInfo file in di.GetFiles())
            {
                file.Delete();
            }
        }

        private void ProccessFile(string destDir, string fileName)
        {
            using (ZipArchive zipArchive = ZipFile.OpenRead(Path.Combine(destDir, fileName)))
            {
                foreach (ZipArchiveEntry entry in zipArchive.Entries)
                {
                    entry.ExtractToFile(Path.Combine(destDir, entry.Name), true);
                }
            }

            using (var accounts = new UnitedAccountFileViewModel(Path.Combine(destDir, "unitedaccounts.uaf")))
            {
                _accountAppService.Import(accounts);
            }

            using (var cards = new UnitedAccountCardFileViewModel(Path.Combine(destDir, "unitedcards.ucf")))
            {
                _accountCardAppService.Import(cards);
            }

            string destPath = _configuration["FileSettings:StorageFolder"];
            Directory.CreateDirectory(destPath);
            File.Move(Path.Combine(destDir, fileName), Path.Combine(destPath, DateTime.Now.ToString("yyyy-MM-dd-HH_mm_ss_fff") + ".zip"));
        }
    }
}
