﻿using OPT.Application.ViewModels.Store;

namespace OPT.Application.ViewModels.Kiosk
{
    public class KioskViewModel
    {
        public Guid ID { get; set; }

        public string Name { get; set; }
        //public string ValidCode { get; set; }
        public string? DeviceID { get; set; }
        public bool Validated { get; set; }
        public bool Active { get; set; }

        public Guid StoreID { get; set; }

        public StoreViewModel Store { get; set; }

        public List<KioskConfigViewModel> Configs { get; set; }

        public KioskInfoViewModel Info { get; set; }
    }
}
