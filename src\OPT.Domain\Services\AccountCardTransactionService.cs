﻿using OPT.Domain.Exceptions;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;

namespace OPT.Domain.Services
{
    public class AccountCardTransactionService : IAccountCardTransactionService
    {
        private readonly IAccountCardTransactionRepository _repository;

        public AccountCardTransactionService(IAccountCardTransactionRepository repository)
        {
            _repository = repository;
        }

        public void Add(AccountCardTransaction cardTransaction)
        {
            _repository.Add(cardTransaction);
        }

        public void Update(AccountCardTransaction cardTransaction)
        {
            _repository.Update(cardTransaction);
        }

        public AccountCardTransaction? GetBySTANAndTerminalID(long STAN, string terminalId, long transactionReferece)
        {
            return _repository.Find(x => x.STAN == STAN && x.TerminalID == terminalId && x.TransactionReferece == transactionReferece).FirstOrDefault();
}
        
        public AccountCardTransaction? GetByAuthCodeSTANAndTerminalID(string authCode, long STAN, string terminalId)
        {
            return _repository.GetByAuthCodeSTANAndTerminalID(authCode, STAN, terminalId);
        }

        public List<AccountCardTransaction> GetByDate(Guid accountId, DateTime dateFrom, DateTime dateTo)
        {
            return _repository.Find(x => x.AccountCard.AccountId.Equals(accountId) 
                && x.FinalizedDate != null
                && x.AmountCaptured > 0
                && x.FinalizedDate.Value.Date >= dateFrom.Date 
                && x.FinalizedDate.Value.Date <= dateTo.Date).ToList();
        }

        public void Dispose()
        {
            _repository.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
