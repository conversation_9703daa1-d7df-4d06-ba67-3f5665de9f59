﻿using OPT.Domain.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.RewardCard
{
    public class RewardCardViewModel
    {
        [SwaggerSchema("A unique identifier for the RewardCard.", Nullable = true)]
        public Guid ID { get; set; }

        [SwaggerSchema("A unique identifier for the RewardCard used in transaction processing.", Nullable = false)]
        public string PAN { get; set; }

        [SwaggerSchema("A unique identifier for the RewardCard read from barcode.", Nullable = true)]
        public string? BarCode { get; set; }

        [SwaggerSchema("The date when the RewardCard expires and can no longer be used for transactions (Format: YYMM).", Nullable = false)]
        public string ExpiryDate { get; set; }

        [SwaggerSchema("Indicates whether the RewardCard is currently active and available for use.", Nullable = false)]
        public bool Active { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply kerosene.", Nullable = true)]
        public long? KeroseneDiscount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of kerosene for applying discount.", Nullable = true)]
        public long? KeroseneCap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply Unleaded 91.", Nullable = true)]
        public long? Unleaded91Discount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of Unleaded 91 for applying discount.", Nullable = true)]
        public long? Unleaded91Cap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply Unleaded 95.", Nullable = true)]
        public long? Unleaded95Discount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of Unleaded 95 for applying discount.", Nullable = true)]
        public long? Unleaded95Cap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply E10.", Nullable = true)]
        public long? E10Discount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of E10 for applying discount.", Nullable = true)]
        public long? E10Cap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply Vpower 98.", Nullable = true)]
        public long? Vpower98Discount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of Vpower 98 for applying discount.", Nullable = true)]
        public long? Vpower98Cap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply Premium Diesel.", Nullable = true)]
        public long? PremiumDieselDiscount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of Premium Diesel for applying discount.", Nullable = true)]
        public long? PremiumDieselCap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply Diesel.", Nullable = true)]
        public long? DieselDiscount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of Diesel for applying discount.", Nullable = true)]
        public long? DieselCap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply ADBlue.", Nullable = true)]
        public long? ADBlueDiscount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of ADBlue for applying discount.", Nullable = true)]
        public long? ADBlueCap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply LPG.", Nullable = true)]
        public long? LpgDiscount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of LPG for applying discount.", Nullable = true)]
        public long? LpgCap { get; set; }

        public void LoadDiscounts(List<RewardCardDiscount> discounts)
        {
            foreach (var item in discounts)
            {
                switch (item.FuelGrade)
                {
                    case "01":
                        KeroseneDiscount = item.Discount == 0 ? null : item.Discount;
                        KeroseneCap = item.Cap;
                        break;
                    case "02":
                        Unleaded91Discount = item.Discount == 0 ? null : item.Discount;
                        Unleaded91Cap = item.Cap;
                        break;
                    case "03":
                        Unleaded95Discount = item.Discount == 0 ? null : item.Discount;
                        Unleaded95Cap = item.Cap;
                        break;
                    case "04":
                        E10Discount = item.Discount == 0 ? null : item.Discount;
                        E10Cap = item.Cap;
                        break;
                    case "05":
                        Vpower98Discount = item.Discount == 0 ? null : item.Discount;
                        Vpower98Cap = item.Cap;
                        break;
                    case "06":
                        PremiumDieselDiscount = item.Discount == 0 ? null : item.Discount;
                        PremiumDieselCap = item.Cap;
                        break;
                    case "07":
                        DieselDiscount = item.Discount == 0 ? null : item.Discount;
                        DieselCap = item.Cap;
                        break;
                    case "08":
                        ADBlueDiscount = item.Discount == 0 ? null : item.Discount;
                        ADBlueCap = item.Cap;
                        break;
                    case "09":
                        LpgDiscount = item.Discount == 0 ? null : item.Discount;
                        LpgCap = item.Cap;
                        break;
                    default:
                        break;
                }
            }
        }
    }
}
