﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using OPT.Application.Interfaces;
using OPT.Application.Specifications;
using OPT.Application.ViewModels.Account;
using OPT.Application.ViewModels.Store;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Interfaces.UoW;
using OPT.Domain.Models;
using OPT.Domain.Specification;
using System.Data;
using System.Data.SqlClient;

namespace OPT.Application
{
    public class AccountAppService : ApplicationService, IAccountAppService
    {
        private readonly IAccountService _service;
        private readonly IAccountCardTransactionService _transactionService;
        private readonly IStoreService _storeService;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;

        public AccountAppService(IUnitOfWork unitOfWork,
                                IMapper mapper,
                                IAccountService service,
                                IAccountCardTransactionService transactionService,
                                IStoreService storeService,
                                IConfiguration configuration) : base(unitOfWork)
        {
            _service = service;
            _mapper = mapper;
            _transactionService = transactionService;
            _storeService = storeService;
            _configuration = configuration;
        }

        public AccountInternalViewModel GetById(Guid id)
        {
            return _mapper.Map<AccountInternalViewModel>(_service.GetById(id));
        }
        
        public AccountViewModel GetById(Guid id, ApiKey apiKey)
        {
            var account = _service.GetById(id);
            if (new AccountValidSpecification(apiKey).IsValid(account))
            {
                return _mapper.Map<AccountViewModel>(account);
            }
            return null;
        }

        public List<AccountInternalViewModel> GetByStores(StoreFilterViewModel storeFilter)
        {
            if (storeFilter.StoresId != null && storeFilter.StoresId.Any(x => x.Equals(new Guid("228C0C6C-A85F-4A4A-841A-35C1CFFE5CE3"))))
                throw new Exception("This functionality is currently unavailable, sorry for the inconvenience");
            if (storeFilter.TenantsID != null && storeFilter.TenantsID.Any(x => x.Equals(new Guid("C4BD99D6-55F9-49D3-A842-A444B059F038"))))
                throw new Exception("This functionality is currently unavailable, sorry for the inconvenience");
            return _mapper.Map<List<AccountInternalViewModel>>(_service.GetByStores(storeFilter.StoresId, storeFilter.TenantsID));
        }
        
        public AccountPaginationViewModel GetByStoresWithPagination(StoreFilterV2ViewModel storeFilter)
        {
            (int total, List<Account> accs) = _service.GetByStoresWithPagination(storeFilter.StoresId, storeFilter.TenantsID, storeFilter.Offset, storeFilter.Limit);
            return new AccountPaginationViewModel
            {
                TotalRecords = total,
                Offset = storeFilter.Offset,
                PageRecords = accs.Count,
                List = _mapper.Map<List<AccountInternalViewModel>>(accs)
            };
        }

        public List<AccountViewModel> GetByStore(ApiKey apiKey)
        {
            return _mapper.Map<List<AccountViewModel>>(_service.GetByStores(new List<Guid> { apiKey.StoreID }, null));
        }

        public void Add(AccountInternalViewModel account)
        {
            var add = _mapper.Map<Account>(account);
            _service.Add(add);
            Commit();
        }
        
        public Guid? Add(AccountAddViewModel account, ApiKey apiKey)
        {
            if (account.IsValid<AccountAddViewModel, AccountAddSpecification>())
            {
                var store = _storeService.GetById(apiKey.StoreID);
                var acc = _mapper.Map<Account>(account);
                acc.TenantID = store?.TenantID;
                _service.Add(acc, apiKey.StoreID);
                Commit();
                return acc.ID;
            }
            return null;
        }
        
        public void Update(Guid id, AccountAddViewModel account, ApiKey apiKey)
        {
            if (account.IsValid<AccountAddViewModel, AccountAddSpecification>())
            {
                var acc = _mapper.Map<Account>(account);
                _service.Update(id, acc, apiKey.StoreID);
                Commit();
            }
        }

        public void Import(UnitedAccountFileViewModel account)
        {
            Guid tenantID = new Guid(_configuration["UnitedSettings:TenantID"]);

            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("Code", typeof(string));
            dataTable.Columns.Add("Name", typeof(string));
            dataTable.Columns.Add("Active", typeof(int));
            dataTable.Columns.Add("CreditLimit", typeof(decimal));
            dataTable.Columns.Add("FlagOdometer", typeof(char));
            dataTable.Columns.Add("TenantID", typeof(Guid));
            dataTable.Columns.Add("CurrentBalance", typeof(decimal));

            while (account.ReadNew())
            {
                dataTable.Rows.Add( account.Code
                                  , account.Name
                                  , account.Active ? 1 : 0
                                  , account.CreditLimit
                                  , account.FlagOdometer
                                  , tenantID
                                  , account.CurrentBalance);
            }

            using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                using (var clearCommand = new SqlCommand("TRUNCATE TABLE AccountsTemp", connection))
                {
                    clearCommand.CommandTimeout = int.MaxValue;
                    clearCommand.ExecuteNonQuery();
                }

                using (var bulkCopy = new SqlBulkCopy(connection))
                {
                    bulkCopy.BulkCopyTimeout = 0;
                    bulkCopy.DestinationTableName = "AccountsTemp";
                    bulkCopy.WriteToServer(dataTable);
                }

                using (SqlCommand cmd = new SqlCommand("MERGE INTO [dbo].[Accounts] AS Target " +
                    "USING AccountsTemp AS Source ON Target.Code = Source.Code " +
                    "WHEN MATCHED THEN " +
                    "UPDATE SET Target.Name = Source.Name, " +
                    "Target.CreditLimit = Source.CreditLimit, " +
                    "Target.Active = Source.Active, " +
                    "Target.FlagOdometer = Source.FlagOdometer " +
                    "WHEN NOT MATCHED THEN " +
                    "INSERT (ID, Name, CreditLimit, Active, Code, FlagOdometer, TenantID) " +
                    "VALUES (newid(), Source.Name, Source.CreditLimit, Source.Active, Source.Code, Source.FlagOdometer, Source.TenantID);"
                    , connection))
                {
                    cmd.CommandTimeout = int.MaxValue;
                    cmd.ExecuteNonQuery();
                }
                
                using (SqlCommand cmd = new SqlCommand("MERGE INTO [dbo].[AccountBalances] AS Target " +
                    "USING (select ac.ID, Act.Code, act.CurrentBalance " +
                          "from [dbo].[AccountsTemp] act " +
                          "inner join [dbo].[Accounts] ac " +
                          "on ac.Code = act.Code " +
                          "and ac.Active = 1) " +
                    "AS Source ON Target.AccountId = Source.Id " +
                    "WHEN MATCHED THEN " +
                    "UPDATE SET Target.LastBalanceDate = [dbo].[sfGetDate](), " +
                    "Target.Amount = Source.CurrentBalance " +
                    "WHEN NOT MATCHED THEN " +
                    "INSERT (AccountId, LastBalanceDate, Amount) " +
                    "VALUES (Source.Id, [dbo].[sfGetDate](), Source.CurrentBalance);"
                    , connection))
                {
                    cmd.CommandTimeout = int.MaxValue;
                    cmd.ExecuteNonQuery();
                }
            }
        }

        public AccountBalanceViewModel GetBalance(Guid accountId, DateTime dateFrom, DateTime DateTo)
        {
            var balance = _mapper.Map<AccountBalanceViewModel>(_service.GetBalance(accountId));
            if (balance != null)
            {
                balance.Statements = new List<AccountStatementViewModel>();
                balance.Statements.AddRange(_mapper.Map<List<AccountStatementViewModel>>(_service.GetJournalsByDate(accountId, dateFrom, DateTo)));
                balance.Statements.AddRange(_mapper.Map<List<AccountStatementViewModel>>(_transactionService.GetByDate(accountId, dateFrom, DateTo)));
                balance.Statements = balance.Statements.OrderByDescending(s => s.Date).ToList();
                return balance;
            }
            return new AccountBalanceViewModel()
            {
                LastBalanceDate = DateTime.Now,
                Statements = new List<AccountStatementViewModel>()
            };
        }
        
        public AccountBalanceViewModel GetBalance(Guid accountId, DateTime dateFrom, DateTime DateTo, ApiKey apiKey)
        {
            var account = _service.GetById(accountId);
            if (new AccountValidSpecification(apiKey).IsValid(account))
            {
                var balance = _mapper.Map<AccountBalanceViewModel>(_service.GetBalance(accountId));
                if (balance != null)
                {
                    balance.Statements = new List<AccountStatementViewModel>();
                    balance.Statements.AddRange(_mapper.Map<List<AccountStatementViewModel>>(_service.GetJournalsByDate(accountId, dateFrom, DateTo)));
                    balance.Statements.AddRange(_mapper.Map<List<AccountStatementViewModel>>(_transactionService.GetByDate(accountId, dateFrom, DateTo)));
                    balance.Statements = balance.Statements.OrderByDescending(s => s.Date).ToList();
                    return balance;
                }
                return new AccountBalanceViewModel()
                {
                    LastBalanceDate = DateTime.Now,
                    Statements = new List<AccountStatementViewModel>()
                };
            }

            return null;
        }

        public void addJournal(AccountJournalViewModel accountJournal)
        {
            if (accountJournal.IsValid<AccountJournalViewModel, AccountJournalSpecification>())
            {
                _service.addJournal(_mapper.Map<AccountJournal>(accountJournal));
                Commit();
            }
        }
        
        public void addJournal(JournalViewModel accountJournal, ApiKey apiKey)
        {
            var account = _service.GetById(accountJournal.AccountId);
            if (new AccountValidSpecification(apiKey).IsValid(account))
            {
                if (accountJournal.IsValid<JournalViewModel, JournalSpecification>())
                {
                    var journal = _mapper.Map<AccountJournal>(accountJournal);
                    journal.POSMasterUserId = apiKey.ID.ToString();
                    _service.addJournal(journal);
                    Commit();
                }
            }
        }

        public void Dispose()
        {
            _service.Dispose();
            _transactionService.Dispose();
            _storeService.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
