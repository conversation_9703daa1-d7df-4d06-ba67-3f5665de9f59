﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using OPT.Application.Helper;
using OPT.Infra.Data.Identity.Context;
using OPT.Infra.Data.Identity.Model;

namespace OPT.Scheduler.Configuration
{
    public static class IdentityConfiguration
    {
        public static IServiceCollection AddIdentitySetup(this IServiceCollection services, IConfiguration configuration)
        {
            if (services == null) throw new ArgumentNullException(nameof(services));

            services.AddDbContext<IdentityOPTContext>(options => options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")));

            services.AddDefaultIdentity<OPTUser>(options => options.SignIn.RequireConfirmedAccount = true)
                .AddRoles<IdentityRole>()
                .AddEntityFrameworkStores<IdentityOPTContext>()
                .AddUserStore<UserStoreCustom>()
                .AddTokenProvider<AesDataProtectorTokenProvider<OPTUser>>(TokenOptions.DefaultProvider);

            return services;
        }
    }
}
