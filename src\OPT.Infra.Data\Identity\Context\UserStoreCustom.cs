﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using OPT.Infra.Data.Identity.Model;

namespace OPT.Infra.Data.Identity.Context
{
    public class UserStoreCustom : UserStore<OPTUser>
    {
        public UserStoreCustom(IdentityOPTContext context, IdentityErrorDescriber describer = null)
            : base(context, describer)
        {

        }
    }
}
