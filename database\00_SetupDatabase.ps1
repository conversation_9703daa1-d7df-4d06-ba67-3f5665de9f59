# =============================================
# OPTdb Complete Database Setup Script
# Description: Complete setup script that runs all database setup steps
# =============================================

param(
    [string]$Environment = "Development",
    [string]$ServerInstance = "(localdb)\MSSQLLocalDB",
    [switch]$SkipDatabaseCreation = $false,
    [switch]$SkipMigrations = $false,
    [switch]$SkipSeedData = $false,
    [switch]$Force = $false,
    [switch]$Verbose = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$SolutionRoot = Split-Path -Parent $ScriptDir

Write-Host "=== OPTdb Complete Database Setup ===" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "Server Instance: $ServerInstance" -ForegroundColor Yellow
Write-Host "Solution Root: $SolutionRoot" -ForegroundColor Yellow
Write-Host ""

# Function to execute SQL script
function Execute-SqlScript {
    param(
        [string]$ScriptPath,
        [string]$Database = "master"
    )
    
    Write-Host "Executing SQL script: $ScriptPath" -ForegroundColor Cyan
    
    try {
        # Try using sqlcmd first
        if (Get-Command "sqlcmd" -ErrorAction SilentlyContinue) {
            if ($Database -eq "master") {
                sqlcmd -S $ServerInstance -i $ScriptPath -b
            } else {
                sqlcmd -S $ServerInstance -d $Database -i $ScriptPath -b
            }
            
            if ($LASTEXITCODE -ne 0) {
                throw "sqlcmd failed with exit code: $LASTEXITCODE"
            }
        } else {
            Write-Host "sqlcmd not found. Please install SQL Server Command Line Utilities or execute the script manually in SSMS." -ForegroundColor Red
            Write-Host "Script location: $ScriptPath" -ForegroundColor Yellow
            throw "sqlcmd not available"
        }
    } catch {
        Write-Host "Error executing SQL script: $($_.Exception.Message)" -ForegroundColor Red
        throw
    }
}

try {
    # Step 1: Create Database (if not skipped)
    if (-not $SkipDatabaseCreation) {
        Write-Host "=== Step 1: Creating Database ===" -ForegroundColor Green
        $createDbScript = Join-Path $ScriptDir "01_CreateDatabase.sql"
        
        if (Test-Path $createDbScript) {
            if ($Force) {
                Execute-SqlScript -ScriptPath $createDbScript
            } else {
                Write-Host "This will create/recreate the OPTdb database. Existing data will be lost!" -ForegroundColor Red
                $confirmation = Read-Host "Continue? (y/N)"
                if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
                    Execute-SqlScript -ScriptPath $createDbScript
                } else {
                    Write-Host "Database creation skipped by user." -ForegroundColor Yellow
                    $SkipDatabaseCreation = $true
                }
            }
        } else {
            Write-Host "Database creation script not found: $createDbScript" -ForegroundColor Red
            throw "Required script missing"
        }
        Write-Host "Database creation completed." -ForegroundColor Green
        Write-Host ""
    }
    
    # Step 2: Run Entity Framework Migrations (if not skipped)
    if (-not $SkipMigrations) {
        Write-Host "=== Step 2: Running Entity Framework Migrations ===" -ForegroundColor Green
        
        # Set environment variable
        $env:ASPNETCORE_ENVIRONMENT = $Environment
        
        # Change to solution directory
        Set-Location $SolutionRoot
        
        # Check if dotnet CLI is available
        if (-not (Get-Command "dotnet" -ErrorAction SilentlyContinue)) {
            throw "dotnet CLI is not installed or not in PATH"
        }
        
        # Install EF tools if needed
        $efTools = dotnet tool list --global | Select-String "dotnet-ef"
        if (-not $efTools) {
            Write-Host "Installing Entity Framework tools..." -ForegroundColor Yellow
            dotnet tool install --global dotnet-ef
        }
        
        # Navigate to Data project
        $DataProjectPath = Join-Path $SolutionRoot "src\OPT.Infra.Data"
        Set-Location $DataProjectPath
        
        # Run main migrations
        Write-Host "Applying main database migrations..." -ForegroundColor Cyan
        $migrationArgs = @("ef", "database", "update", "--startup-project", "..\OPT.API\OPT.API.csproj")
        if ($Verbose) { $migrationArgs += "--verbose" }
        
        & dotnet $migrationArgs
        if ($LASTEXITCODE -ne 0) {
            throw "Main database migrations failed with exit code: $LASTEXITCODE"
        }
        
        # Run Identity migrations
        Write-Host "Applying Identity migrations..." -ForegroundColor Cyan
        $identityArgs = @("ef", "database", "update", "--context", "IdentityOPTContext", "--startup-project", "..\OPT.API\OPT.API.csproj")
        if ($Verbose) { $identityArgs += "--verbose" }
        
        & dotnet $identityArgs
        if ($LASTEXITCODE -ne 0) {
            throw "Identity migrations failed with exit code: $LASTEXITCODE"
        }
        
        Write-Host "Entity Framework migrations completed." -ForegroundColor Green
        Write-Host ""
    }
    
    # Step 3: Seed Data (if not skipped)
    if (-not $SkipSeedData) {
        Write-Host "=== Step 3: Seeding Initial Data ===" -ForegroundColor Green
        $seedDataScript = Join-Path $ScriptDir "03_SeedData.sql"
        
        if (Test-Path $seedDataScript) {
            Execute-SqlScript -ScriptPath $seedDataScript -Database "OPTdb"
        } else {
            Write-Host "Seed data script not found: $seedDataScript" -ForegroundColor Red
            throw "Required script missing"
        }
        Write-Host "Seed data insertion completed." -ForegroundColor Green
        Write-Host ""
    }
    
    # Success message
    Write-Host "=== Database Setup Completed Successfully! ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Start the OPT.API project" -ForegroundColor White
    Write-Host "2. Navigate to https://localhost:7225/swagger to test the API" -ForegroundColor White
    Write-Host "3. Use SQL Server Management Studio to explore the database" -ForegroundColor White
    Write-Host ""
    Write-Host "Connection String:" -ForegroundColor Cyan
    Write-Host "Data Source=$ServerInstance;Initial Catalog=OPTdb;Integrated Security=True;TrustServerCertificate=True" -ForegroundColor White
    
} catch {
    Write-Host "=== Database Setup Failed ===" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($Verbose) {
        Write-Host "Stack trace: $($_.Exception.StackTrace)" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "1. Ensure SQL Server LocalDB is installed and running" -ForegroundColor White
    Write-Host "2. Check if you have sufficient permissions" -ForegroundColor White
    Write-Host "3. Verify the connection string is correct" -ForegroundColor White
    Write-Host "4. Run with -Verbose flag for more details" -ForegroundColor White
    Write-Host "5. Check the README.md file for detailed instructions" -ForegroundColor White
    
    exit 1
} finally {
    # Return to original directory
    Set-Location $SolutionRoot
}
