﻿using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.Account
{
    public class AccountViewModel
    {
        [SwaggerSchema("The unique ID of the account", Nullable = false)]
        public Guid ID { get; set; }

        [SwaggerSchema("The email of account.", Nullable = true)]
        public string Name { get; set; }

        [SwaggerSchema("The email of account.", Nullable = true)]
        public string? Email { get; set; }

        [SwaggerSchema("Activate or deactivate account.", Nullable = false)]
        public bool Active { get; set; }

        [SwaggerSchema("Mobile number of someone responsible for the account. This number can be used for future notifications.", Nullable = true)]
        public string? MobileNumber { get; set; }

        [SwaggerSchema("Account credit limit. If the total number of transactions is greater than this amount, the account will be disabled for new transactions until payment is made (journal).", Nullable = true)]
        public decimal? CreditLimit { get; set; }
    }
}
