@echo off
REM =============================================
REM OPTdb Entity Framework Migrations Script (Batch)
REM Description: Runs EF Core migrations to create database schema
REM =============================================

setlocal enabledelayedexpansion

REM Set default environment
set ENVIRONMENT=Development
if not "%1"=="" set ENVIRONMENT=%1

echo === OPTdb Database Migration Script ===
echo Environment: %ENVIRONMENT%

REM Get script directory and solution root
set SCRIPT_DIR=%~dp0
set SOLUTION_ROOT=%SCRIPT_DIR%..

echo Solution Root: %SOLUTION_ROOT%

REM Change to solution directory
cd /d "%SOLUTION_ROOT%"

REM Check if dotnet CLI is available
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: dotnet CLI is not installed or not in PATH
    pause
    exit /b 1
)

REM Set environment variable
set ASPNETCORE_ENVIRONMENT=%ENVIRONMENT%
echo Set ASPNETCORE_ENVIRONMENT to: %ENVIRONMENT%

REM Check if Entity Framework tools are installed
echo Checking Entity Framework tools...
dotnet tool list --global | findstr "dotnet-ef" >nul
if errorlevel 1 (
    echo Installing Entity Framework tools...
    dotnet tool install --global dotnet-ef
    if errorlevel 1 (
        echo ERROR: Failed to install Entity Framework tools
        pause
        exit /b 1
    )
)

REM Navigate to the Data project directory
set DATA_PROJECT_PATH=%SOLUTION_ROOT%\src\OPT.Infra.Data
if not exist "%DATA_PROJECT_PATH%" (
    echo ERROR: Data project not found at: %DATA_PROJECT_PATH%
    pause
    exit /b 1
)

cd /d "%DATA_PROJECT_PATH%"
echo Changed to Data project directory: %DATA_PROJECT_PATH%

REM Check current migration status
echo Checking current migration status...
dotnet ef migrations list --startup-project ..\OPT.API\OPT.API.csproj
if errorlevel 1 (
    echo ERROR: Failed to list migrations
    pause
    exit /b 1
)

REM Apply migrations to database
echo Applying migrations to database...
dotnet ef database update --startup-project ..\OPT.API\OPT.API.csproj
if errorlevel 1 (
    echo ERROR: Database migrations failed
    pause
    exit /b 1
)

echo === Database migrations completed successfully! ===

REM Apply Identity migrations
echo Applying Identity migrations...
dotnet ef database update --context IdentityOPTContext --startup-project ..\OPT.API\OPT.API.csproj
if errorlevel 1 (
    echo ERROR: Identity migrations failed
    pause
    exit /b 1
)

echo === Identity migrations completed successfully! ===

REM Return to solution root
cd /d "%SOLUTION_ROOT%"

echo === Migration script completed ===
pause
