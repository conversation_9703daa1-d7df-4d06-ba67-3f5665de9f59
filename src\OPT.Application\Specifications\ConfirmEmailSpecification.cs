﻿using OPT.Application.Helper;
using OPT.Application.ViewModels.User;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class ConfirmEmailSpecification : ISpecificationConfiguration<ConfirmEmailViewModel>
    {
        private readonly IIdentityService _identityService;
        public ConfirmEmailSpecification(IIdentityService identityService)
        {
            _identityService = identityService;
        }

        public ISpecification<ConfirmEmailViewModel> Map(ISpecification<ConfirmEmailViewModel> builder)
        {
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.CompanyCode) && x.CompanyCode.Length == 6, "Company code is incorrect.");
            builder.IsSatisfiedBy(x => {
                var user = _identityService.GetByEmail(x.Email.Trim()).Result;
                return user != null && x.CompanyCode == CryptographyHelper.Decrypt(user.CompanyHash);
            }, "User not found or Company code is incorrect.");

            return builder;
        }
    }
}
