-- Essential tables creation script for OPTdb
-- Based on Entity Framework migration: InitialOPTDbCreation

USE OPTdb;
GO

-- Create AccountCardTypes table
CREATE TABLE [AccountCardTypes] (
    [ID] nvarchar(1) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    [Payment] bit NOT NULL,
    [Reward] bit NOT NULL,
    CONSTRAINT [PK_AccountCardTypes] PRIMARY KEY ([ID])
);

-- Create Stores table
CREATE TABLE [Stores] (
    [ID] uniqueidentifier NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Address] nvarchar(max) NOT NULL,
    [City] nvarchar(max) NOT NULL,
    [State] nvarchar(max) NOT NULL,
    [ZipCode] nvarchar(max) NOT NULL,
    [Phone] nvarchar(max) NOT NULL,
    [Email] nvarchar(max) NOT NULL,
    [Active] bit NOT NULL,
    [CreatedDate] datetime2 NOT NULL,
    [ModifiedDate] datetime2 NULL,
    CONSTRAINT [PK_Stores] PRIMARY KEY ([ID])
);

-- Create Accounts table
CREATE TABLE [Accounts] (
    [ID] uniqueidentifier NOT NULL,
    [StoreId] uniqueidentifier NOT NULL,
    [AccountNumber] nvarchar(max) NOT NULL,
    [CompanyName] nvarchar(max) NOT NULL,
    [ContactName] nvarchar(max) NOT NULL,
    [ContactEmail] nvarchar(max) NOT NULL,
    [ContactPhone] nvarchar(max) NOT NULL,
    [BillingAddress] nvarchar(max) NOT NULL,
    [BillingCity] nvarchar(max) NOT NULL,
    [BillingState] nvarchar(max) NOT NULL,
    [BillingZipCode] nvarchar(max) NOT NULL,
    [Active] bit NOT NULL,
    [CreatedDate] datetime2 NOT NULL,
    [ModifiedDate] datetime2 NULL,
    CONSTRAINT [PK_Accounts] PRIMARY KEY ([ID]),
    CONSTRAINT [FK_Accounts_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([ID]) ON DELETE CASCADE
);

-- Create Kiosks table
CREATE TABLE [Kiosks] (
    [ID] uniqueidentifier NOT NULL,
    [StoreId] uniqueidentifier NOT NULL,
    [KioskNumber] nvarchar(max) NOT NULL,
    [Location] nvarchar(max) NOT NULL,
    [Active] bit NOT NULL,
    [CreatedDate] datetime2 NOT NULL,
    [ModifiedDate] datetime2 NULL,
    CONSTRAINT [PK_Kiosks] PRIMARY KEY ([ID]),
    CONSTRAINT [FK_Kiosks_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([ID]) ON DELETE CASCADE
);

-- Create AccountCards table
CREATE TABLE [AccountCards] (
    [ID] uniqueidentifier NOT NULL,
    [AccountId] uniqueidentifier NOT NULL,
    [CardNumber] nvarchar(max) NOT NULL,
    [CardTypeId] nvarchar(1) NOT NULL,
    [Active] bit NOT NULL,
    [CreatedDate] datetime2 NOT NULL,
    [ModifiedDate] datetime2 NULL,
    CONSTRAINT [PK_AccountCards] PRIMARY KEY ([ID]),
    CONSTRAINT [FK_AccountCards_Accounts_AccountId] FOREIGN KEY ([AccountId]) REFERENCES [Accounts] ([ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_AccountCards_AccountCardTypes_CardTypeId] FOREIGN KEY ([CardTypeId]) REFERENCES [AccountCardTypes] ([ID]) ON DELETE CASCADE
);

-- Create Transactions table
CREATE TABLE [Transactions] (
    [ID] uniqueidentifier NOT NULL,
    [KioskId] uniqueidentifier NOT NULL,
    [AccountId] uniqueidentifier NOT NULL,
    [TransactionDate] datetime2 NOT NULL,
    [TransactionType] nvarchar(max) NOT NULL,
    [Amount] decimal(18,2) NOT NULL,
    [Status] nvarchar(max) NOT NULL,
    [CreatedDate] datetime2 NOT NULL,
    [ModifiedDate] datetime2 NULL,
    CONSTRAINT [PK_Transactions] PRIMARY KEY ([ID]),
    CONSTRAINT [FK_Transactions_Accounts_AccountId] FOREIGN KEY ([AccountId]) REFERENCES [Accounts] ([ID]) ON DELETE CASCADE,
    CONSTRAINT [FK_Transactions_Kiosks_KioskId] FOREIGN KEY ([KioskId]) REFERENCES [Kiosks] ([ID]) ON DELETE NO ACTION
);

-- Create ApiKeys table
CREATE TABLE [ApiKeys] (
    [ID] uniqueidentifier NOT NULL,
    [KeyName] nvarchar(max) NOT NULL,
    [KeyValue] nvarchar(max) NOT NULL,
    [Active] bit NOT NULL,
    [CreatedDate] datetime2 NOT NULL,
    [ModifiedDate] datetime2 NULL,
    CONSTRAINT [PK_ApiKeys] PRIMARY KEY ([ID])
);

PRINT 'Essential tables created successfully!';
