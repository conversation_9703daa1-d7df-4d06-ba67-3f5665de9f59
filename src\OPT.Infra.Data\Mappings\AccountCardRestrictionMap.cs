﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class AccountCardRestrictionMap : Mapping<AccountCardRestriction>
    {
        public override void Map(EntityTypeBuilder<AccountCardRestriction> builder)
        {
            builder.ToTable("AccountCardRestrictions");

            builder.HasKey(x => x.ID);

            base.Map(builder);
        }
    }
}
