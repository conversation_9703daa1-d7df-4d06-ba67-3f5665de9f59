﻿using Microsoft.EntityFrameworkCore;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Models;
using OPT.Infra.Data.Context;

namespace OPT.Infra.Data.Repositories
{
    public class RewardCardTransactionRepository : Repository<RewardCardTransaction>, IRewardCardTransactionRepository
    {
        public RewardCardTransactionRepository(OPTContext context) : base(context)
        {
        }

        public List<RewardCardTransaction> GetByDate(Guid rewardCardId, DateTime dateFrom, DateTime dateTo)
        {
            return _dbset.Where(x => x.RewardCardDiscount.AccountCardID.Equals(rewardCardId)
                && x.CreatedDateTime.Date >= dateFrom.Date
                && x.CreatedDateTime.Date <= dateTo.Date)
                .Include(x => x.Kiosk)
                .Include(x => x.RewardCardDiscount)
                .AsNoTracking()
                .ToList();
        }
    }
}
