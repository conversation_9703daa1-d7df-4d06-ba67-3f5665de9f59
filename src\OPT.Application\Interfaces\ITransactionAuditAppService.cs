﻿using OPT.Application.ViewModels.TransactionAudit;
using OPT.Domain.Models;

namespace OPT.Application.Interfaces
{
    public interface ITransactionAuditAppService : IDisposable
    {
        void Add(TransactionAuditAddViewModel model, Kiosk kiosk);

        List<GroupTransactionAuditViewModel> GetByFilter(TransactionAuditFilterViewModel filter);

        TransactionAuditPagination GetByFilterWithPagination(TransactionAuditFilterV2ViewModel filter);
    }
}
