﻿using OPT.Application.Helper;
using OPT.Application.ViewModels.User;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class UserAddedSpecification : ISpecificationConfiguration<UserAddViewModel>
    {
        private readonly IIdentityService _identityservice;
        public UserAddedSpecification(IIdentityService identityservice)
        {
            _identityservice = identityservice;
        }

        public ISpecification<UserAddViewModel> Map(ISpecification<UserAddViewModel> builder)
        {
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.FirstName), "First Name is required!");
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.LastName), "Last Name is required!");
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.UserName), "UserName is required!");
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.Role), "Role is required!");

            builder.IsSatisfiedBy(x => ValidatorHelper.ValidEmail(x.UserName), "Email is not valid!");
            builder.IsSatisfiedBy(x => x.Role == Role.Admin || x.Role == Role.User || x.Role == Role.SuperUser, "Role is invalid!");

            builder.IsSatisfiedBy(x => string.IsNullOrWhiteSpace(x.PhoneNumber) || (!string.IsNullOrWhiteSpace(x.PhoneNumber) && (_identityservice.GetByPhoneNumber(x.PhoneNumber).Result == null)), "User with that phone number already exists!");

            builder.IsSatisfiedBy(x => _identityservice.GetByName(x.UserName).Result == null, "User already exists!");

            return builder;
        }
    }
}
