﻿using System.Numerics;
using System.Security.Cryptography;
using System.Text;

namespace OPT.Application.Helper
{
    public class CryptographyHelper
    {
        private static readonly byte[] bdk = new byte[] { 0xfe, 0xef, 0xdc, 0xcd, 0xba, 0xab, 0x98, 0x89, 0x76, 0x67, 0x54, 0x45, 0x32, 0x23, 0x10, 0x01 };

        public static string Encrypt(string raw)
        {
            return Convert.ToBase64String(EncryptBytes(raw));
        }
        
        public static byte[] EncryptBytes(string raw)
        {
            return new AesProtectionProvider(new SystemSettings()).CreateProtector("").Protect(Encoding.UTF8.GetBytes(raw));
        }

        
        public static string DecryptBytes(byte[] hash)
        {
            return Encoding.UTF8.GetString(new AesProtectionProvider(new SystemSettings()).CreateProtector("").Unprotect(hash));
        }
        
        public static string Decrypt(string hash)
        {
            return DecryptBytes(Convert.FromBase64String(hash));
        }

        #region Private Mask Constants
        private static BigInteger HexToBigInteger(string str)
        {
            return BigInteger.Parse("00" + str, System.Globalization.NumberStyles.HexNumber);
        }
        private static byte[] GetBytes(BigInteger number)
        {
            return number.ToByteArray().Reverse().SkipWhile(b => b == 0).ToArray();
        }
        private static BigInteger ToBigInteger(byte[] bytes)
        {
            return new BigInteger(bytes.Reverse().Concat(new byte[] { 0 }).ToArray());
        }
        private static readonly BigInteger Reg3Mask = HexToBigInteger("1FFFFF");
        private static readonly BigInteger ShiftRegMask = HexToBigInteger("100000");
        private static readonly BigInteger Reg8Mask = HexToBigInteger("FFFFFFFFFFE00000");
        private static readonly BigInteger Ls16Mask = HexToBigInteger("FFFFFFFFFFFFFFFF");
        private static readonly BigInteger Ms16Mask = HexToBigInteger("FFFFFFFFFFFFFFFF0000000000000000");
        private static readonly BigInteger KeyMask = HexToBigInteger("C0C0C0C000000000C0C0C0C000000000");
        private static readonly BigInteger PekMask = HexToBigInteger("FF00000000000000FF");
        private static readonly BigInteger KsnMask = HexToBigInteger("FFFFFFFFFFFFFFE00000");
        private static readonly BigInteger DekMask = HexToBigInteger("0000000000FF00000000000000FF0000");

        #endregion

        #region Private Methods

        /// <summary>
        /// Create Initial PIN Encryption Key
        /// </summary>
        /// <param name="ksn">Key Serial Number</param>
        /// <param name="bdk">Base Derivation Key</param>
        /// <returns>Initial PIN Encryption Key</returns>
        private static BigInteger CreateIpek(BigInteger ksn, BigInteger bdk)
        {
            return Transform("TripleDES", true, bdk, (ksn & KsnMask) >> 16) << 64
                    | Transform("TripleDES", true, bdk ^ KeyMask, (ksn & KsnMask) >> 16);
        }

        /// <summary>
        /// Create Session Key with PEK Mask
        /// </summary>
        /// <param name="ipek">Initial PIN Encryption Key</param>
        /// <param name="ksn">Key Serial Number</param>
        /// <returns>Session Key</returns>
        private static BigInteger CreateSessionKeyPEK(BigInteger ipek, BigInteger ksn)
        {
            return DeriveKey(ipek, ksn) ^ PekMask;
        }

        /// <summary>
        /// Create Session Key
        /// </summary>
        /// <param name="bdk">Base Derivation Key</param>
        /// <param name="ksn">Key Serial Number</param>
        /// <param name="DUKPTVariant">DUKPT variant used to determine session key creation method</param>
        /// <returns>Session Key</returns>
        private static BigInteger CreateSessionKey(byte[] bdk, byte[] ksn)
        {
            BigInteger ksnBigInt = HexToBigInteger(ConvertHelper.ByteArrayToHexString(ksn));
            BigInteger ipek = CreateIpek(ksnBigInt, HexToBigInteger(ConvertHelper.ByteArrayToHexString(bdk)));
            BigInteger sessionKey;

            sessionKey = CreateSessionKeyPEK(ipek, ksnBigInt);

            return sessionKey;
        }

        /// <summary>
        /// Derive Key from IPEK and KSN
        /// </summary>
        /// <param name="ipek">Initial PIN Encryption Key</param>
        /// <param name="ksn">Key Serial Number</param>
        /// <returns>Key derived from IPEK and KSN</returns>
        private static BigInteger DeriveKey(BigInteger ipek, BigInteger ksn)
        {
            BigInteger ksnReg = ksn & Ls16Mask & Reg8Mask;
            BigInteger curKey = ipek;
            for (BigInteger shiftReg = ShiftRegMask; shiftReg > 0; shiftReg >>= 1)
            {
                if ((shiftReg & ksn & Reg3Mask) > 0)
                {
                    ksnReg = ksnReg | shiftReg;
                    curKey = GenerateKey(curKey, ksnReg);
                }
            }
            return curKey;
        }

        /// <summary>
        /// Generate Key
        /// </summary>
        /// <param name="key">Key</param>
        /// <param name="ksn">Key Serial Number</param>
        /// <returns>Key generated from provided key and KSN</returns>
        private static BigInteger GenerateKey(BigInteger key, BigInteger ksn)
        {
            return EncryptRegister(key ^ KeyMask, ksn) << 64 | EncryptRegister(key, ksn);
        }

        /// <summary>
        /// Encrypt Register
        /// </summary>
        /// <param name="key">Key</param>
        /// <param name="reg8">Register which to encrypt</param>
        /// <returns>Encrypted register value</returns>
        private static BigInteger EncryptRegister(BigInteger key, BigInteger reg8)
        {
            return (key & Ls16Mask) ^ Transform("DES", true, (key & Ms16Mask) >> 64, (key & Ls16Mask ^ reg8));
        }

        /// <summary>
        /// Transform Data
        /// </summary>
        /// <param name="name">Encryption algorithm name</param>
        /// <param name="encrypt">Encrypt data flag</param>
        /// <param name="key">Encryption key</param>
        /// <param name="message">Data to encrypt or decrypt</param>
        /// <returns>Result of transformation (encryption or decryption)</returns>
        private static BigInteger Transform(string name, bool encrypt, BigInteger key, BigInteger message)
        {
            using (SymmetricAlgorithm cipher = SymmetricAlgorithm.Create(name))
            {
                byte[] k = GetBytes(key);
                cipher.Key = new byte[Math.Max(0, GetNearestWholeMultiple(k.Length, 8) - k.Length)].Concat(GetBytes(key)).ToArray();
                cipher.IV = new byte[8];
                cipher.Mode = CipherMode.ECB;
                cipher.Padding = PaddingMode.None;
                using (ICryptoTransform crypto = encrypt ? cipher.CreateEncryptor() : cipher.CreateDecryptor())
                {
                    byte[] data = GetBytes(message);
                    data = new byte[Math.Max(0, GetNearestWholeMultiple(data.Length, 8) - data.Length)].Concat(GetBytes(message)).ToArray();
                    return ToBigInteger(crypto.TransformFinalBlock(data, 0, data.Length));
                }
            }
        }

        /// <summary>
        /// Get nearest whole value of provided decimal value which is a multiple of provided integer
        /// </summary>
        /// <param name="input">Number which to determine nearest whole multiple</param>
        /// <param name="multiple">Multiple in which to divide input</param>
        /// <returns>Whole integer value of input nearest to a multiple of provided decimal</returns>
        private static int GetNearestWholeMultiple(decimal input, int multiple)
        {
            decimal output = Math.Round(input / multiple);
            if (output == 0 && input > 0)
            {
                output += 1;
            }
            output *= multiple;
            return (int)output;
        }

        #endregion

        #region Public Methods

        public static byte[] EncryptDUKPT(byte[] ksn, byte[] data)
        {
            if (data == null)
            {
                throw new ArgumentNullException(nameof(data));
            }

            return GetBytes(Transform("TripleDES", true, CreateSessionKey(bdk, ksn), ToBigInteger(data)));
        }

        public static byte[] DecryptDUKPT(byte[] ksn, byte[] encryptedData)
        {
            if (encryptedData == null)
            {
                throw new ArgumentNullException(nameof(encryptedData));
            }

            return GetBytes(Transform("TripleDES", false, CreateSessionKey(bdk, ksn), ToBigInteger(encryptedData)));
        }

        #endregion

    }
}
