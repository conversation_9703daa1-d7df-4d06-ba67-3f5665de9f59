﻿using OPT.Application.ViewModels.RewardCard;
using OPT.Domain.Specification.Interface;
using System.Globalization;

namespace OPT.Application.Specifications
{
    internal class RewardCardUpdatedSpecification : ISpecificationConfiguration<RewardCardUpdateViewModel>
    {

        public RewardCardUpdatedSpecification()
        {
        }

        public ISpecification<RewardCardUpdateViewModel> Map(ISpecification<RewardCardUpdateViewModel> builder)
        {
            builder.SetStopOnFirstFailure(true);

            builder.IsSatisfiedBy(x => {
                var culture = new CultureInfo("en-AU");
                culture.Calendar.TwoDigitYearMax = 2099;
                if (!string.IsNullOrEmpty(x.ExpiryDate) && DateTime.TryParseExact(x.ExpiryDate, "yyMM", culture, DateTimeStyles.None, out DateTime expirationDate))
                {
                    if (expirationDate >= DateTime.Now.Date.AddDays(-DateTime.Now.Day + 1))
                    {
                        return true;
                    }
                }
                return false;
            }, "The Expiry Date is invalid!");

            return builder;
        }
    }
}
