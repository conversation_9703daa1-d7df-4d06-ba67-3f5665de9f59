﻿using Microsoft.AspNetCore.Mvc;
using OPT.API.Attributes;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.VehicleRegistration;
using Swashbuckle.AspNetCore.Annotations;

namespace OPT.API.Controllers
{
    /// <summary>
    /// The Vehicle Registration feature is essential for maintaining control over the fleet associated with the account. Whenever a user uses the AccountCard for a transaction, they will be asked to specify which vehicle is being used for the transaction. This ensures accurate record-keeping of vehicle-related operations.
    /// 
    /// ### Features:
    /// 1. Association of Vehicles with the Account:
    ///    - Vehicles are registered and linked to the account, allowing for detailed control over the company's fleet.
    /// 
    /// 1. Validation of Vehicle in AccountCard Usage:   
    ///    - When using the AccountCard, the user will be prompted to indicate which vehicle is being used for the transaction.
    ///    - The API validates if the vehicle provided by the user is registered in the account and if it is active.
    /// 
    /// 1. Management of Vehicle Activation/Deactivation:
    ///    - Vehicles can be marked as active or inactive.
    ///    - Transactions associated with inactive vehicles will be denied to ensure security and accurate records.
    /// 
    /// 1. Fleet Control without Vehicle Registration:
    ///    - If the account does not have any registered vehicles, the validation of the user's vehicle registration will be ignored.
    ///    - This allows flexibility for accounts that do not require detailed fleet management.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerControllerOrder(5)]
    public class VehicleRegistrationController : ControllerBase
    {
        private readonly IVehicleRegistrationAppService _appService;

        /// <summary>
        /// VehicleRegistrationController Constructor
        /// </summary>
        /// <param name="appService"></param>
        public VehicleRegistrationController(IVehicleRegistrationAppService appService)
        {
            _appService = appService;
        }

        /// <summary>
        /// Add New VehicleRegistration
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel Add(VehicleRegistrationAddViewModel viewModel)
        {
            _appService.Add(viewModel);
            return ResponseViewModel.Ok($"Vehicle Registration added with success.");
        }

        /// <summary>
        /// list VehicleRegistration By Account
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpGet("account/{accountid}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public List<VehicleRegistrationViewModel> ListByAccount(Guid accountid)
        {
            return _appService.ListByAccount(accountid);
        }
        
        /// <summary>
        /// Get VehicleRegistration By ID
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpGet("{id}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public VehicleRegistrationViewModel? Get(Guid id)
        {
            return _appService.GetById(id);
        }

        /// <summary>
        /// Update VehicleRegistration
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPut]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel Update([FromBody]VehicleRegistrationViewModel vehicleRegistration)
        {
            _appService.Update(vehicleRegistration);
            return ResponseViewModel.Ok($"Vehicle Registration updated with success.");
        }

        /// <summary>
        /// Delete VehicleRegistration
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpDelete("{id}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel Delete(Guid id)
        {
            _appService.Delete(id);
            return ResponseViewModel.Ok($"Vehicle Registration {id} deleted with success.");
        }

        #region Exposed Apis

        [SwaggerOperation(
            Summary = "Add New VehicleRegistration",
            Description = "This endpoint allows you to add a new vehicle registration to the account's fleet."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the succesfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpPost("v1")]
        public ResponseViewModel AddPublic(
            [SwaggerRequestBody("VehicleRegistration object informations", Required = true)]
            VehicleRegistrationAddViewModel viewModel)
        {
            var id = _appService.Add(viewModel, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Vehicle Registration added with success.", id);
        }

        [SwaggerOperation(
            Summary = "List VehicleRegistration By Account",
            Description = "This endpoint allows you to retrieve a list of vehicle registrations associated with a specific account."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return a list of Vehicle Registration object.", typeof(List<VehicleRegistrationViewModel>))]
        [Authorize(Role.API)]
        [HttpGet("v1/account/{accountid}")]
        public List<VehicleRegistrationViewModel> ListByAccountPublic(
            [SwaggerParameter("Account ID", Required = true)]
            Guid accountid)
        {
            return _appService.ListByAccount(accountid, HttpContext.GetApiKey());
        }

        [SwaggerOperation(
            Summary = "Get Vehicle Registration By ID",
            Description = "This endpoint allows you to retrieve detailed information about a specific vehicle registration associated with a particular account."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the Vehicle Registration object.", typeof(VehicleRegistrationViewModel))]
        [Authorize(Role.API)]
        [HttpGet("v1/{id}")]
        public VehicleRegistrationViewModel? GetPublic(
            [SwaggerParameter("Vehicle Registration ID", Required = true)]
            Guid id)
        {
            return _appService.GetById(id, HttpContext.GetApiKey());
        }

        [SwaggerOperation(
            Summary = "Update VehicleRegistration",
            Description = "This endpoint allows you to update the details of a specific vehicle registration associated with a particular account."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return successfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpPut("v1/{id}")]
        public ResponseViewModel UpdatePublic(
            [SwaggerParameter("Vehicle Registration ID", Required = true)]
            Guid id,
            [SwaggerRequestBody("VehicleRegistration object informations", Required = true)]
            [FromBody] VehicleRegistrationAddViewModel vehicleRegistration)
        {
            _appService.Update(id, vehicleRegistration, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Vehicle Registration updated with success.");
        }

        [SwaggerOperation(
            Summary = "Delete VehicleRegistration",
            Description = "This endpoint allows you to remove a specific vehicle registration associated with a particular account."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return successfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpDelete("v1/{id}")]
        public ResponseViewModel DeletePublic(
            [SwaggerParameter("Vehicle Registration ID", Required = true)]
            Guid id)
        {
            _appService.Delete(id, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Vehicle Registration {id} deleted with success.");
        }

        #endregion

    }
}
