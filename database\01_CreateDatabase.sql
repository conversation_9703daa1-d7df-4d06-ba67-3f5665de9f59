-- =============================================
-- OPTdb Database Creation Script
-- Description: Creates the OPTdb database for local development
-- =============================================

USE master;
GO

-- Check if database exists and drop if it does (for clean setup)
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'OPTdb')
BEGIN
    ALTER DATABASE OPTdb SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE OPTdb;
    PRINT 'Existing OPTdb database dropped.';
END
GO

-- Create the OPTdb database
CREATE DATABASE OPTdb
ON 
(
    NAME = 'OPTdb_Data',
    FILENAME = 'C:\Program Files\Microsoft SQL Server\MSSQL15.MSSQLSERVER\MSSQL\DATA\OPTdb.mdf',
    SIZE = 100MB,
    MAXSIZE = 1GB,
    FILEGROWTH = 10MB
)
LOG ON 
(
    NAME = 'OPTdb_Log',
    FILENAME = 'C:\Program Files\Microsoft SQL Server\MSSQL15.MSSQLSERVER\MSSQL\DATA\OPTdb.ldf',
    SIZE = 10MB,
    MAXSIZE = 100MB,
    FILEGROWTH = 5MB
);
GO

PRINT 'OPTdb database created successfully.';

-- Switch to the new database
USE OPTdb;
GO

-- Create a database user for the application (optional - for SQL Server Authentication)
-- Uncomment the following lines if you want to use SQL Server Authentication instead of Windows Authentication

/*
-- Create login at server level
USE master;
GO
CREATE LOGIN [OPTUser] WITH PASSWORD = 'OPT@2025!', DEFAULT_DATABASE = [OPTdb];
GO

-- Switch back to OPTdb and create user
USE OPTdb;
GO
CREATE USER [OPTUser] FOR LOGIN [OPTUser];
GO

-- Grant permissions
ALTER ROLE db_owner ADD MEMBER [OPTUser];
GO

PRINT 'Database user OPTUser created and granted permissions.';
*/

-- Set database options for optimal performance
ALTER DATABASE OPTdb SET RECOVERY SIMPLE;
ALTER DATABASE OPTdb SET AUTO_CLOSE OFF;
ALTER DATABASE OPTdb SET AUTO_SHRINK OFF;
ALTER DATABASE OPTdb SET AUTO_CREATE_STATISTICS ON;
ALTER DATABASE OPTdb SET AUTO_UPDATE_STATISTICS ON;
GO

PRINT 'Database configuration completed.';
PRINT 'Ready for Entity Framework migrations.';
GO
