﻿using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.AccountCard
{
    public class AccountCardTransactionAccountViewModel
    {
        [SwaggerSchema("A unique identifier for the AccountCard.", Nullable = true)]
        public Guid ID { get; set; }

        [SwaggerSchema("A unique identifier for the AccountCard used in transaction processing.", Nullable = false)]
        public string PAN { get; set; }

        [SwaggerSchema("Indicates whether the AccountCard is currently active and available for use.", Nullable = false)]
        public bool Active { get; set; }

        [SwaggerSchema("A unique identifier for the AccountCard used in transaction processing with 19 digits.", Nullable = true)]
        public string? PAN19 { get; set; }

        public string? HolderName { get; set; }

    }
}
