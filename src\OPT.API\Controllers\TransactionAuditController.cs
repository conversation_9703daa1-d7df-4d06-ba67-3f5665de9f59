﻿using Microsoft.AspNetCore.Mvc;
using OPT.API.Attributes;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.TransactionAudit;

namespace OPT.API.Controllers
{
    /// <summary>
    /// Account Controller
    /// </summary>
    [ApiExplorerSettings(GroupName = "v1")]
    [Route("api/[controller]")]
    [ApiController]
    public class TransactionAuditController : ControllerBase
    {
        private readonly ITransactionAuditAppService _appService;

        /// <summary>
        /// CardController Constructor
        /// </summary>
        /// <param name="appService"></param>
        public TransactionAuditController(ITransactionAuditAppService appService)
        {
            _appService = appService;
        }

        /// <summary>
        /// Add New TransactionAudit
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Kiosk)]
        [HttpPost]
        public ResponseViewModel Add(TransactionAuditAddViewModel model)
        {
            _appService.Add(model, HttpContext.GetKiosk());
            return ResponseViewModel.Ok($"TransactionAudit added with success.");
        }

        /// <summary>
        /// Get TransactionAudits by filter. This is only available to users wtih the POSMaster role.
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("filter")]
        public List<GroupTransactionAuditViewModel> GetByFilter(TransactionAuditFilterViewModel filter)
        {
            return _appService.GetByFilter(filter);
        }
        
        /// <summary>
        /// Get TransactionAudits by filter. This is only available to users wtih the POSMaster role.
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("v2/filter")]
        public TransactionAuditPagination GetByFilterWithPagination(TransactionAuditFilterV2ViewModel filter)
        {
            return _appService.GetByFilterWithPagination(filter);
        }
    }
}
