﻿namespace OPT.Domain.Models
{
    public class Account
    {
        public Guid ID { get; set; }

        public string Name { get; set; }
        public string? Email { get; set; }

        public bool Active { get; set; }

        public string? MobileNumber { get; set; }

        public decimal? CreditLimit { get; set; }

        public string? Code { get; set; }

        public char? FlagOdometer { get; set; }

        public Guid? StoreID { get; set; }

        public Store? Store { get; set; }

        public Guid? TenantID { get; set; }

        public List<AccountVehicleRegistration> AccountVehicleRegistrations { get; set; }
        public List<AccountBalance> AccountBalances { get; set; }
    }
}
