﻿namespace OPT.Domain.Models
{
    public class ApiKey
    {
        public Guid ID { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedDateTime { get; set; }
        public string? AccessToken { get; set; }
        public DateTime? TokenDateTime { get; set; }
        public bool Active { get; set; }
        public Guid StoreID { get; set; }
        public Store Store { get; set; }
    }
}
