﻿using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;

namespace OPT.Domain.Services
{
    public class StoreService : IStoreService
    {
        private readonly IStoreRepository _repository;

        public StoreService(IStoreRepository repository)
        {
            _repository = repository;
        }

        public Store? GetById(Guid id)
        {
            return _repository.GetById(id);
        }

        public void Add(Store model)
        {
            var s = _repository.GetById(model.ID);
            if (s == null)
                _repository.Add(model);
            else
                _repository.Update(s, model);
        }

        public void Dispose()
        {
            _repository.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
