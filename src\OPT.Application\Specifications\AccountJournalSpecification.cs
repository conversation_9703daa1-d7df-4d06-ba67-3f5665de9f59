﻿using OPT.Application.ViewModels.Account;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class AccountJournalSpecification : ISpecificationConfiguration<AccountJournalViewModel>
    {

        public AccountJournalSpecification()
        {
        }

        public ISpecification<AccountJournalViewModel> Map(ISpecification<AccountJournalViewModel> builder)
        {
            builder.IsSatisfiedBy(x => x.Amount > 0, "Amount must be greater than zero!");

            return builder;
        }
    }
}
