﻿using Microsoft.Extensions.DependencyInjection;
using OPT.Application.AutoMapper;

namespace OPT.Scheduler.Configuration
{
    public static class AutoMapperConfiguration
    {
        public static void AddAutomapperSetup(this IServiceCollection services)
        {
            if (services == null) throw new ArgumentNullException(nameof(services));

            services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());
            AutoMapperConfig.RegisterMappings();
        }
    }
}
