﻿using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;

namespace OPT.Domain.Services
{
    public class TransactionService : ITransactionService
    {
        private readonly ITransactionRepository _repository;

        public TransactionService(ITransactionRepository repository)
        {
            _repository = repository;
        }
        public void Add(Transaction transaction)
        {
            var t = _repository.GetByTransactionIdAndTransactionDate(transaction.TerminalTransactionId, transaction.TransactionDateTime);
            if(t == null)
                _repository.Add(transaction);
        }
        public List<Transaction> GetByUserId(string id)
        {
            return _repository.GetByUser(id);
        }
        public List<Transaction> GetByFilter(Guid? accountId, Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId)
        {
            return _repository.GetByFilter(accountId, kioskId, dateFrom, dateTo, storesId);
        }
        public (int, List<FuelTransaction>, List<Transaction>) GetByFilterWithPagination(Guid? accountId, Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId, int offset, int limit)
        {
            var total = _repository.GetTotalByFilter(accountId, kioskId, dateFrom, dateTo, storesId);
            var totalGrade = _repository.GetTotalGradeByFilter(accountId, kioskId, dateFrom, dateTo, storesId);
            return (total, totalGrade, _repository.GetByFilterWithPagination(accountId, kioskId, dateFrom, dateTo, storesId, offset, limit));
        }

        public List<Transaction> GetUnitedToExport(Guid tenantID)
        {
            return _repository.GetUnitedToExport(tenantID);
        }

        public void SetUnitedExported(IEnumerable<Guid> ids)
        {
            _repository.SetUnitedExported(ids);
        }

        public void Dispose()
        {
            _repository.Dispose();
            GC.SuppressFinalize(this);
        }

        public Transaction? GetByTransactionId(int transasctionId)
        {
            return _repository.GetByTransaction(transasctionId);
        }
        
        public Transaction? GetByTransactionIdAndKiosk(int transasctionId, Guid kioskId)
        {
            return _repository.GetByTransactionIdAndKiosk(transasctionId, kioskId);
        }

        public async Task SendEmail(string email, string subject, string htmlMessage, params AttachmentModel[] attachments)
        {
            await _repository.SendEmail(email, subject, htmlMessage, attachments);
        }
    }
}