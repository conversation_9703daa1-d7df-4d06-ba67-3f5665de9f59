﻿namespace OPT.Application.Helper
{
    public class FileReaderHelper : IDisposable
    {
        private StreamReader? sr;
        private string? line;
        private int cursor;

        public string FullLine => line ?? string.Empty;

        public int LineLength => line?.Length ?? 0;

        public FileReaderHelper(string filePath)
        {
            if (File.Exists(filePath))
            {
                sr = new StreamReader(filePath);
            }
            else
            {
                throw new FileNotFoundException("Fiel not found", filePath);
            }
        }

        public bool ReadLine()
        {
            cursor = 0;
            return (line = sr?.ReadLine()) != null;
        }

        public string Next(int length)
        {
            if(line != null)
            {
                while ((cursor + length) > line.Length && length != 0)
                    length--;
                cursor += length;
                return line.Substring(cursor - length, length).Trim();
            }
            return string.Empty;
        }

        public int? NextInt(int length)
        {
            var text = Next(length);
            int ret;
            if(int.TryParse(text, out ret))
                return ret;
            return null;
        }
        
        public decimal? NextDecimal(int length)
        {
            var text = Next(length);
            decimal ret;
            if(decimal.TryParse(text, out ret))
                return ret;
            return null;
        }
        
        public bool? NextBool(int length)
        {
            var text = Next(length);
            return text.ToUpper() == "Y" ? true : (text.ToUpper() == "N" ? false : null);
        }


        public void Dispose()
        {
            sr.Dispose();
        }
    }
}
