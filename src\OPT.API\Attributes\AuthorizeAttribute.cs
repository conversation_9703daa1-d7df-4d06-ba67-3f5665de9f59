﻿namespace OPT.API.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true, Inherited = true)]
    public class AuthorizeAttribute : Microsoft.AspNetCore.Authorization.AuthorizeAttribute
    {
        public AuthorizeAttribute(params string[] roles) : base()
        {
            this.Roles = string.Join(",", roles);
        }
        
        public AuthorizeAttribute() : base()
        {
        }
    }
}
