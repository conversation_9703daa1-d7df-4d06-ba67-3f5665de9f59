﻿using OPT.Application.ViewModels.Kiosk;
using OPT.Application.ViewModels.Store;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OPT.Application.ViewModels.Transaction
{
    public class TransactionUserViewModel
    {
        public DateTime TransactionDateTime { get; set; }
        public int TerminalTransactionId { get; set; }
        public string? VehicleRegistration { get; set; }   
        public decimal FinalAmount { get; set; }
        public string StoreName { get; set; }
        public string KioskName { get; set; }
    }
}
