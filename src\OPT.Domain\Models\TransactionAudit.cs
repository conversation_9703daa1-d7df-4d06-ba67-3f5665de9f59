﻿using OPT.Domain.Models.Enumerations;

namespace OPT.Domain.Models
{
    public class TransactionAudit
    {
        public Guid ID { get; set; }
        public DateTime AuditDate { get; set; }
        public DateTime TransactionCreatedDate { get; set; }
		public long KioskTransactionNumber { get; set; }
		public string? CardSignature { get; set; }
		public string? CardExpiryDate { get; set; }
		public required string Processor { get; set; }
		public TransactionStatus? TransactionStatus { get; set; }
		public decimal? TransactionAmount { get; set; }
		public decimal? TransactionFinalAmount { get; set; }
		public string? TransactionResponseCode { get; set; }
		public string? TransactionResponse { get; set; }
		public string? VehicleRegistration { get; set; }
		public string? VehicleOdometer { get; set; }
		public string? PumpNumber { get; set; }
		public string? FuelGrade { get; set; }
		public string? FuelGradeName { get; set; }
		public Guid KioskID { get; set; }
		public Guid? AccountCardID { get; set; }
		public Guid? AccountID { get; set; }

        public Kiosk <PERSON>k { get; set; }
    }
}
