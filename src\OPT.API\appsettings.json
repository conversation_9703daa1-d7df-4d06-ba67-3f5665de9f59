{"ConnectionStrings": {"DefaultConnection": "Data Source=smartfuelstaging.database.windows.net;Initial Catalog=OPT_Staging;Persist Security Info=True;User ID=stagingsql;Password=***************;TrustServerCertificate=True"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "LoggerOptions": {"Path": "./logs"}, "JWT": {"ValidAudience": "https://api.staging.opt.posmaster.com.au", "ValidIssuer": "https://api.staging.opt.posmaster.com.au", "Secret": "JWTAuthenticationHIGHsecuredPasswordVVVp1OH7Xzyr"}, "HealthChecksUI": {"HealthChecks": [{"Name": "API Staging", "Uri": "http://localhost:7225/health"}], "HeaderText": "OPT API Health Check DashBoard", "Webhooks": [], "EvaluationTimeInSeconds": 10, "MinimumSecondsBetweenFailureNotifications": 60, "MaximumHistoryEntriesPerEndpiunt": 50}, "Email": {"Host": "smtp.office365.com", "Port": "587", "Username": "<EMAIL>", "Password": "[_)b3ePsih:(7M(9", "FromAddress": "<EMAIL>", "FromName": "SmartFuel - Outdoor Payment"}, "Upload": {"KiosPath": "C:\\Temp\\Kiosk\\"}, "AllowedHosts": "*"}