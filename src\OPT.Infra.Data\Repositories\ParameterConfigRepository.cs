﻿using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Models;
using OPT.Infra.Data.Context;

namespace OPT.Infra.Data.Repositories
{
    public class ParameterConfigRepository : Repository<ParameterConfig>, IParameterConfigRepository
    {

        public ParameterConfigRepository(OPTContext context) : base(context)
        {
        }

        public new IEnumerable<ParameterConfig> GetAll()
        {
            return _dbset.Where(x => x.Active).OrderBy(x => x.Order);
        }
    }
}
