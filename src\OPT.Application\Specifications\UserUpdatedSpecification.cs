﻿using OPT.Application.Helper;
using OPT.Application.ViewModels.User;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class UserUpdatedSpecification : ISpecificationConfiguration<UserUpdateViewModel>
    {
        private readonly IIdentityService _identityservice;
        public UserUpdatedSpecification(IIdentityService identityservice)
        {
            _identityservice = identityservice;
        }

        public ISpecification<UserUpdateViewModel> Map(ISpecification<UserUpdateViewModel> builder)
        {
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.Role), "Role is required!");

            builder.IsSatisfiedBy(x => x.Role == Role.Admin || x.Role == Role.User || x.Role == Role.SuperUser, "Role is invalid!");
            
            builder.IsSatisfiedBy(x => {
                if (string.IsNullOrWhiteSpace(x.PhoneNumber))
                    return true;

                var user = _identityservice.GetByPhoneNumber(x.PhoneNumber).Result;
                if (user == null || (user != null && user.Id.Equals(x.Id)))
                {
                    return true;
                }
                return false;
            }, "User with that phone number already exists!");

            builder.IsSatisfiedBy(x => _identityservice.GetById(x.Id).Result != null, "User not exists!");

            return builder;
        }
    }
}
