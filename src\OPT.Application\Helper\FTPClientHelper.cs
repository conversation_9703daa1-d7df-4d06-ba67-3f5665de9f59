﻿using Renci.SshNet;
using Renci.SshNet.Sftp;
using System.Text;

namespace OPT.Application.Helper
{
    public class FTPClientHelper : IDisposable
    {
        public const string ZIP = ".zip";


        private SftpClient ftpClient;
        private List<SftpFile> listItems;
        private int index;
        private readonly string InFolder;
        private readonly string OutFolder;

        public FTPClientHelper(string ftpServer, int port, string username, string? password = null, string inFolder = "", string outFolder = "")
        {
            InFolder = inFolder;
            OutFolder = outFolder;
            ConnectionInfo connectionInfo;
            if (string.IsNullOrWhiteSpace(password))
                connectionInfo = new ConnectionInfo(ftpServer, port, username);
            else
                connectionInfo = new ConnectionInfo(ftpServer, port, username, new PasswordAuthenticationMethod(username, password));

            ftpClient = new SftpClient(connectionInfo);
            ftpClient.Connect();
            listItems = new List<SftpFile>();
        }

        public void LoadFilesByExtension(string extensionType)
        {
            foreach (SftpFile item in ftpClient.ListDirectory($"/{InFolder}").OrderByDescending(x => x.Name))
            {
                if (!item.IsDirectory && item.Name.EndsWith(extensionType))
                {
                    listItems.Add(item);
                }
            }
            index = -1;
        }

        public bool HasFile()
        {
            if(index + 1 < listItems.Count)
            {
                index++;
                return true;
            }
            return false;

        }
        
        public string FileName()
        {
            if (index >= 0)
                return listItems[index].Name;
            return string.Empty;
        }
        
        public void DownloadFile(string destPath)
        {
            if (index >= 0)
                using (var fileStream = File.OpenWrite(Path.Combine(destPath, listItems[index].Name)))
                {
                    ftpClient.DownloadFile(listItems[index].FullName, fileStream);
                }
        }

        public void UploadText(string text, string fileName)
        {
            using (var memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(text)))
            {
                ftpClient.UploadFile(memoryStream, Path.Combine(OutFolder, fileName));
            }
        }
        
        public void DeleteFile()
        {
            if (index >= 0)
                ftpClient.DeleteFile(listItems[index].FullName);
        }

        public void Dispose()
        {
            ftpClient.Disconnect();
            ftpClient?.Dispose();
        }
    }
}
