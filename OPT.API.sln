﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32922.545
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OPT.API", "src\OPT.API\OPT.API.csproj", "{029E20E5-2720-44F3-9C83-1B5B6FA04037}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "1 - Services", "1 - Services", "{0ACA8EBD-3A86-4FCF-9653-899FE7F87830}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "2 - Application", "2 - Application", "{1D35205D-0EB6-4AE2-A870-FFB60BFAA2D1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "3 - Domain", "3 - Domain", "{7382792C-43E9-473A-89BB-29B26D2622F2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "4 - Infra", "4 - Infra", "{523E469F-795A-4C6B-ACDF-2A83508A183D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "4.1 - Data", "4.1 - Data", "{4E07538B-8F75-4C1B-8D78-9FA1EE3575D4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "4.2 - CrossCutting", "4.2 - CrossCutting", "{DD40B503-2051-484B-9939-10B59D73BED7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OPT.Infra.CrossCutting.IoC", "src\OPT.Infra.CrossCutting.IoC\OPT.Infra.CrossCutting.IoC.csproj", "{5C9CEB8E-89BF-4D2C-896A-4F665C700B00}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OPT.Infra.Data", "src\OPT.Infra.Data\OPT.Infra.Data.csproj", "{50DCC9E3-86E0-4E7F-86F3-F818AA571F3D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OPT.Application", "src\OPT.Application\OPT.Application.csproj", "{C29B829C-1A40-4A09-B03C-7D4E2F3D4DAF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OPT.Domain", "src\OPT.Domain\OPT.Domain.csproj", "{5956AFC9-0FC8-474F-BBF5-0CA4E6CE285E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OPT.Scheduler", "src\OPT.Scheduler\OPT.Scheduler.csproj", "{DE4783C8-27A6-4534-88D1-2DCB1975831F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{029E20E5-2720-44F3-9C83-1B5B6FA04037}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{029E20E5-2720-44F3-9C83-1B5B6FA04037}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{029E20E5-2720-44F3-9C83-1B5B6FA04037}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{029E20E5-2720-44F3-9C83-1B5B6FA04037}.Release|Any CPU.Build.0 = Release|Any CPU
		{5C9CEB8E-89BF-4D2C-896A-4F665C700B00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5C9CEB8E-89BF-4D2C-896A-4F665C700B00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5C9CEB8E-89BF-4D2C-896A-4F665C700B00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5C9CEB8E-89BF-4D2C-896A-4F665C700B00}.Release|Any CPU.Build.0 = Release|Any CPU
		{50DCC9E3-86E0-4E7F-86F3-F818AA571F3D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50DCC9E3-86E0-4E7F-86F3-F818AA571F3D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50DCC9E3-86E0-4E7F-86F3-F818AA571F3D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{50DCC9E3-86E0-4E7F-86F3-F818AA571F3D}.Release|Any CPU.Build.0 = Release|Any CPU
		{C29B829C-1A40-4A09-B03C-7D4E2F3D4DAF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C29B829C-1A40-4A09-B03C-7D4E2F3D4DAF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C29B829C-1A40-4A09-B03C-7D4E2F3D4DAF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C29B829C-1A40-4A09-B03C-7D4E2F3D4DAF}.Release|Any CPU.Build.0 = Release|Any CPU
		{5956AFC9-0FC8-474F-BBF5-0CA4E6CE285E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5956AFC9-0FC8-474F-BBF5-0CA4E6CE285E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5956AFC9-0FC8-474F-BBF5-0CA4E6CE285E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5956AFC9-0FC8-474F-BBF5-0CA4E6CE285E}.Release|Any CPU.Build.0 = Release|Any CPU
		{DE4783C8-27A6-4534-88D1-2DCB1975831F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DE4783C8-27A6-4534-88D1-2DCB1975831F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DE4783C8-27A6-4534-88D1-2DCB1975831F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DE4783C8-27A6-4534-88D1-2DCB1975831F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{029E20E5-2720-44F3-9C83-1B5B6FA04037} = {0ACA8EBD-3A86-4FCF-9653-899FE7F87830}
		{4E07538B-8F75-4C1B-8D78-9FA1EE3575D4} = {523E469F-795A-4C6B-ACDF-2A83508A183D}
		{DD40B503-2051-484B-9939-10B59D73BED7} = {523E469F-795A-4C6B-ACDF-2A83508A183D}
		{5C9CEB8E-89BF-4D2C-896A-4F665C700B00} = {DD40B503-2051-484B-9939-10B59D73BED7}
		{50DCC9E3-86E0-4E7F-86F3-F818AA571F3D} = {4E07538B-8F75-4C1B-8D78-9FA1EE3575D4}
		{C29B829C-1A40-4A09-B03C-7D4E2F3D4DAF} = {1D35205D-0EB6-4AE2-A870-FFB60BFAA2D1}
		{5956AFC9-0FC8-474F-BBF5-0CA4E6CE285E} = {7382792C-43E9-473A-89BB-29B26D2622F2}
		{DE4783C8-27A6-4534-88D1-2DCB1975831F} = {0ACA8EBD-3A86-4FCF-9653-899FE7F87830}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {77E324CC-BEB0-4ECA-B229-31B41E252147}
	EndGlobalSection
EndGlobal
