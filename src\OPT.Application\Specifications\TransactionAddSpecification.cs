﻿using OPT.Application.ViewModels.Transaction;
using OPT.Application.ViewModels.User;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Specification.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OPT.Application.Specifications
{
    
    internal class TransactionAddSpecification : ISpecificationConfiguration<TransactionAddViewModel>
    {
        private readonly IIdentityService _identityservice;
        public TransactionAddSpecification(IIdentityService identityservice)
        {
            _identityservice = identityservice;
        }

        public ISpecification<TransactionAddViewModel> Map(ISpecification<TransactionAddViewModel> builder)
        {
            builder.IsSatisfiedBy(x => x.TerminalTransactionId != null, "TerminalTransactionId is required!");
            builder.IsSatisfiedBy(x => x.TransactionDateTime != null, "Transaction date is required!");
            builder.IsSatisfiedBy(x => x.Type != null, "Transaction Type is required!");
            builder.IsSatisfiedBy(x => x.Status != null, "Transaction Status is required!");
            builder.IsSatisfiedBy(x => x.AuthorisedAmount != null, "Transaction AuthorisedAmount is required!");
            builder.IsSatisfiedBy(x => x.FinalAmount != null, "Transaction FinalAmount is required!");
            builder.IsSatisfiedBy(x => x.GSTAmount != null, "Transaction GSTAmount is required!");
            builder.IsSatisfiedBy(x => x.CVM != null, "Transaction CVM is required!");
            builder.IsSatisfiedBy(x => x.Processor != null, "Transaction Processor is required!");
            builder.IsSatisfiedBy(x => x.TerminalId != null, "Transaction TerminalId is required!");
            builder.IsSatisfiedBy(x => x.StoreId != null, "Transaction StoreId is required!");
            builder.IsSatisfiedBy(x => x.KioskId != null, "Transaction KioskId is required!");

            return builder;
        }
    }
}
