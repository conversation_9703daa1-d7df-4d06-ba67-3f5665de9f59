﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class RewardCardDiscountMap : Mapping<RewardCardDiscount>
    {
        public override void Map(EntityTypeBuilder<RewardCardDiscount> builder)
        {
            builder.ToTable("RewardCardDiscounts");

            builder.<PERSON><PERSON><PERSON>(x => x.ID);

            builder.HasOne(x => x.AccountCard).WithMany(x => x.Discounts).HasForeignKey(x => x.AccountCardID).OnDelete(DeleteBehavior.NoAction);

            base.Map(builder);
        }
    }
}
