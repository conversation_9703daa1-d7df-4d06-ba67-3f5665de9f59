﻿using Microsoft.AspNetCore.Mvc;
using OPT.API.Attributes;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.User;
using System.IdentityModel.Tokens.Jwt;

namespace OPT.API.Controllers
{
    /// <summary>
    /// Authenticate Controller
    /// </summary>
    [ApiExplorerSettings(GroupName = "v1")]
    [Route("api/[controller]")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly IUserAppService _appService;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// AuthenticateController Constructor
        /// </summary>
        /// <param name="appService"></param>
        public UserController(IUserAppService appService, IConfiguration configuration)
        {
            _appService = appService;
            _configuration = configuration;
        }

        /// <summary>
        /// Get User
        /// </summary>
        /// <param name="id">User Id</param>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpGet("{id}")]
        public async Task<UserViewModel> Get(Guid id)
        {
            return await _appService.GetById(id);
        }
        
        /// <summary>
        /// Get User for Kiosk
        /// </summary>
        /// <param name="id">User Id</param>
        /// <returns></returns>
        [Authorize(Role.Kiosk)]
        [HttpGet("{id}/kiosk")]
        public async Task<UserKioskViewModel> GetByIdKiosk(Guid id)
        {
            return await _appService.GetByIdKiosk(id);
        }

        /// <summary>
        /// User authentication
        /// </summary>
        /// <param name="model"></param>
        [HttpPost("authenticate")]
        public async Task<UserSessionViewModel> Authenticate(AuthenticateViewModel model)
        {
            return await _appService.Authenticate(model);
        }

        /// <summary>
        /// Add New User
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost]
        public async Task<ResponseViewModel> Add(UserAddViewModel userAdd)
        {
            await _appService.Add(userAdd);
            return ResponseViewModel.Ok($"User {userAdd.UserName} added with success.");
        }

        /// <summary>
        /// Update User
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPut]
        public async Task<ResponseViewModel> Update(UserUpdateViewModel userUpdate)
        {
            await _appService.Update(userUpdate);
            return ResponseViewModel.Ok($"User {userUpdate.Id} updated with success.");
        }

        /// <summary>
        /// Confirm Email
        /// </summary>
        /// <returns></returns>
        [HttpPost("email/confirm")]
        public async Task<ResponseViewModel> ConfirmEmail(ConfirmEmailViewModel confirmEmail)
        {
            var passToken = await _appService.ConfirmEmail(confirmEmail);
            return ResponseViewModel.Ok(passToken);
        }
        
        /// <summary>
        /// Reset Password
        /// </summary>
        /// <returns></returns>
        [HttpPost("password/reset")]
        public async Task<ResponseViewModel> ResetPassword(ResetPasswordViewModel resetPassword)
        {
            await _appService.ResetPassword(resetPassword);
            return ResponseViewModel.Ok("Password reset successfully!");
        }

        /// <summary>
        /// User forgot password
        /// </summary>
        /// <param name="username"></param>
        /// <returns></returns>
        [HttpGet("forgotPassword/{username}")]
        public async Task<ResponseViewModel> ForgotPassword(string username)
        {
            await _appService.ForgotPassword(username);
            return ResponseViewModel.Ok("Please check your email to continue.");
        }

        /// <summary>
        /// Get Users By Account
        /// </summary>
        /// <param name="accountid">Account Id</param>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpGet("account/{accountid}")]
        public async Task<List<UserViewModel>> GetByAccount(Guid accountid)
        {
            return await _appService.GetByAccount(accountid);
        }

#if DEBUG
        /// <summary>
        /// Remove User
        /// </summary>
        /// <param name="id">User Id</param>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpDelete("{id}")]
        public async Task<ResponseViewModel> Remove(Guid id)
        {
            await _appService.Remove(id);
            return ResponseViewModel.Ok($"User {id} removed with success.");
        }

        /// <summary>
        /// PosMasterTokem temp
        /// </summary>
        /// <returns></returns>
        [HttpGet("temptoken")]
        public ResponseViewModel GetTempToken()
        {
            var authSigningKey = new Microsoft.IdentityModel.Tokens.SymmetricSecurityKey(System.Text.Encoding.UTF8.GetBytes(_configuration["JWT:Secret"]));

            var token = new JwtSecurityToken(
                issuer: _configuration["JWT:ValidIssuer"],
                audience: _configuration["JWT:ValidAudience"],
                expires: DateTime.Now.AddHours(8),
                claims: new List<System.Security.Claims.Claim>
                    {
                        new System.Security.Claims.Claim(System.Security.Claims.ClaimTypes.Sid, Guid.NewGuid().ToString()),
                        new System.Security.Claims.Claim(System.Security.Claims.ClaimTypes.Name,""),
                        new System.Security.Claims.Claim(System.Security.Claims.ClaimTypes.Role, "POSMaster"),
                        //new Claim(ClaimTypes.GroupSid, user.TenantID.ToString()),
                    },
                signingCredentials: new Microsoft.IdentityModel.Tokens.SigningCredentials(authSigningKey, Microsoft.IdentityModel.Tokens.SecurityAlgorithms.HmacSha256)
                );

            return ResponseViewModel.Ok(new JwtSecurityTokenHandler().WriteToken(token));
        }
#endif

        /*
        /// <summary>
        /// Get All Users
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Admin, Role.SuperUser)]
        [HttpGet]
        public IEnumerable<UserViewModel> GetAll()
        {
            return _appService.GetAll();
        }*/
    }
}
