﻿using OPT.Application.ViewModels.AccountCard;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Specification.Interface;
using System.Globalization;

namespace OPT.Application.Specifications
{
    internal class AccountCardUpdatedSpecification : ISpecificationConfiguration<AccountCardUpdateViewModel>
    {
        private IAccountCardService accountCardService;
        private Guid id;

        public AccountCardUpdatedSpecification(IAccountCardService accountCardService, Guid id)
        {
            this.accountCardService = accountCardService;
            this.id = id;
        }

        public ISpecification<AccountCardUpdateViewModel> Map(ISpecification<AccountCardUpdateViewModel> builder)
        {
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.PAN) && x.PAN.Length == 16 && x.PAN.All(c => char.IsNumber(c)), "The PAN number is incorrect!");
            builder.IsSatisfiedBy(x =>
            {
                if (!string.IsNullOrEmpty(x.PAN))
                {
                    var card = accountCardService.GetByPAN(x.PAN);
                    if (card == null || (card != null && card.ID == id))
                        return true;
                }
                return false;
            }, "Registered PAN number already exists!");
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.ExpiryDate) && x.ExpiryDate.Length == 4 && x.ExpiryDate.All(c => char.IsNumber(c)), "The Expiry Date is incorrect!");
            builder.IsSatisfiedBy(x => {
                var culture = new CultureInfo("en-AU");
                culture.Calendar.TwoDigitYearMax = 2099;
                if (!string.IsNullOrEmpty(x.ExpiryDate) && DateTime.TryParseExact(x.ExpiryDate, "yyMM", culture, System.Globalization.DateTimeStyles.None, out DateTime expirationDate))
                {
                    if (expirationDate >= DateTime.Now.Date.AddDays(-DateTime.Now.Day + 1))
                    {
                        return true;
                    }
                }
                return false;
            }, "The Expiry Date is invalid!");

            return builder;
        }
    }
}
