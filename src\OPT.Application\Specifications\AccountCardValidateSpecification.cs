﻿using OPT.Application.ViewModels.AccountCard;
using OPT.Domain.Models;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class AccountCardValidateSpecification : ISpecificationConfiguration<AccountCardValidateViewModel>
    {
        AccountCard? card;

        public AccountCardValidateSpecification(AccountCard? card)
        {
            this.card = card;
        }

        public ISpecification<AccountCardValidateViewModel> Map(ISpecification<AccountCardValidateViewModel> builder)
        {
            builder.SetStopOnFirstFailure(true);
            
            //Check if there is a record of the vehicle and if it is active.
            builder.IsSatisfiedBy(x => {
                if (card?.Account?.AccountVehicleRegistrations != null && card.Account.AccountVehicleRegistrations.Count > 0)
                {
                    var vehicle = card.Account.AccountVehicleRegistrations.FirstOrDefault(y => y.VehicleRegistration.RegistrationNumber.ToUpper() == x.VehicleRegistrationNumber?.ToUpper() && y.VehicleRegistration.Active);
                    if (vehicle != null)
                        return true;
                }
                else
                {
                    return true;
                }
                return false;
            }, "57");//Function Not Permitted to Cardholder

            builder.IsSatisfiedBy(x =>
            {
                if (card.Account.FlagOdometer == null || card.Account.FlagOdometer != 'Y')
                    return true;
                else
                {
                    long inputOdometer = 0;
                    long cardOdometer = 0;
                    if (!string.IsNullOrWhiteSpace(x.VehicleOdometer))
                        long.TryParse(x.VehicleOdometer, out inputOdometer);
                    if (!string.IsNullOrWhiteSpace(card.VehicleOdometer))
                        long.TryParse(card.VehicleOdometer, out cardOdometer);
                    
                    if (inputOdometer > cardOdometer)
                        return true;
                }
                return false;
            }, "57"); //Function Not Permitted to Cardholder


            return builder;
        }
    }
}
