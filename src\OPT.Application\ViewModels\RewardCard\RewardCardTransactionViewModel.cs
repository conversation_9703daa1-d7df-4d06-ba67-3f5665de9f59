﻿using OPT.Application.ViewModels.Kiosk;
using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.RewardCard
{
    public class RewardCardTransactionViewModel
    {
        [SwaggerSchema("Date the reward card was used.", Nullable = false)]
        public DateTime CreatedDateTime { get; set; }

        [SwaggerSchema("Total discount applied to supply.", Nullable = false)]
        public long TotalDiscountApplied { get; set; }

        [SwaggerSchema("FuelGrade for which a discount was applied.", Nullable = false)]
        public string FuelGrade { get; set; }

        public KioskNameViewModel Kiosk { get; set; }
    }
}
