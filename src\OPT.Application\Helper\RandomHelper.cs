﻿namespace OPT.Application.Helper
{
    internal class RandomHelper
    {
        public static string RandomString(int length)
        {
            var random = new Random();
            const string chars = "abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ23456789";//I'm not using the chars o, 0, l, 1 and i so as not to confuse the user
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
        
        public static string RandomNumber(int length)
        {
            var random = new Random();
            const string chars = "0123456789";
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    }
}
