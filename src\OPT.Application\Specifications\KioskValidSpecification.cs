﻿using OPT.Application.ViewModels.Kiosk;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class KioskValidSpecification : ISpecificationConfiguration<KioskValidViewModel>
    {
        public ISpecification<KioskValidViewModel> Map(ISpecification<KioskValidViewModel> builder)
        {
            builder.IsSatisfiedBy(x => x.ID != null && x.ID != Guid.Empty, "ID is required!");
            builder.IsSatisfiedBy(x => !string.IsNullOrWhiteSpace(x.ValidCode), "Valid Key is required!");
            builder.IsSatisfiedBy(x => !string.IsNullOrWhiteSpace(x.DeviceID), "Device ID is required!");

            return builder;
        }
    }
}
