﻿namespace OPT.Domain.Models
{
    public class AccountCardTransaction
    {
        public Guid ID { get; set; }

        public string TerminalID { get; set; }
        public long STAN { get; set; }
        public string? RRN { get; set; }
        public long TransactionReferece { get; set; }
        public string PAN { get; set; }
        public string Track2 { get; set; }
        public byte[]? PIN { get; set; }
        public DateTime TransactionDate { get; set; }
        public DateTime? FinalizedDate { get; set; }
        public string AuthCode { get; set; }
        public long? AmountAuthorized { get; set; }
        public long? AmountCaptured { get; set; }
        public long? AmountReversed { get; set; }
        public string? HostResponse { get; set; }
        public Guid KioskID { get; set; }
        public Guid? AccountCardID { get; set; }

        public AccountCard? AccountCard { get; set; }
    }
}
