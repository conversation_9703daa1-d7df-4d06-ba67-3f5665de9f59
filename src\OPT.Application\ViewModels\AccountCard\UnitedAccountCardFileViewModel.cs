﻿using OPT.Application.Helper;
using OPT.Application.ViewModels.RewardCard;
using OPT.Application.ViewModels.User;

namespace OPT.Application.ViewModels.AccountCard
{
    public class UnitedAccountCardFileViewModel : FileReaderHelper
    {
        private static readonly Dictionary<char, string> CardMaskType = new()
        {
            { 'U', "********" },
            { 'P', "********" },
            { 'G', "********" },
            { 'C', "********" },
            { 'R', "308407" },
            { 'S', "308415" },
        };

        public static readonly string[] ProductOrder = new string[]
        {
            "04", //Plus
            "02", //ULP
            "07", //Diesel
            "09", //LPG
            "03", //Premium95
            "05", //Premium98
            "Lubes", //Lubes
            "CarWash", //CarWash
            "ShopGST", //ShopGST
            "08", //AdBlue
            "E85", //Order/E85
            "06", //Premium Diesel
            "Other3", //Order3
            "Other4", //Order4
            "Other5", //Order5
            "Other6", //Order6
            "Other7", //Order7
            "Other8", //Order8
            "Other9", //Order9
            "Other10", //Order10
        };

        public bool IsPayment => (new char[] { 'U', 'P', 'G' }).Contains(Type);
        public bool IsReward => !IsPayment;

        public UnitedAccountCardFileViewModel(string filePath) : base(filePath)
        {
        }

        public bool ReadNew()
        {
            if (ReadLine())
            {
                Restrictions = null;
                Discounts = null;
                BarCode = null;
                User = null;

                if (LineLength == 0)
                    return ReadNew();

                Type = Next(1).ToCharArray()[0];
                if (!CardMaskType.ContainsKey(Type))
                    return ReadNew();
                PAN = CardMaskType[Type] + Next(14);
                if(PAN.Length > 16)
                    return ReadNew();
                AccountCode = Next(6);
                HolderName = Next(50);
                PIN = Next(4);

                if(IsPayment)
                {
                    Restrictions = new List<AccountCardRestrictionViewModel>();
                    for (int order = 0; order < 20; order++)
                    {
                        if (NextBool(1).Value)
                        {
                            if (order < 6 || order == 11)
                            {
                                Restrictions.Add(new AccountCardRestrictionViewModel
                                {
                                    FuelGrade = ProductOrder[order]
                                });
                            }
                        }
                        Next(8);//ignores amount
                    }
                }else
                {
                    BarCode = $";{PAN}?";
                    Discounts = Enumerable.Range(1, 9).Select(i => new RewardCardDiscountViewModel { FuelGrade = i.ToString("D2") }).ToList();
                    for (int order = 0; order < 20; order++)
                    {
                        if (NextBool(1).Value)
                        {
                            var discount = Discounts.FirstOrDefault(x => x.FuelGrade == ProductOrder[order]);
                            var amount = NextDecimal(8);
                            if (discount != null)
                            {
                                discount.Discount = (long)amount.Value;
                                discount.Cap = 150;
                            }
                        }
                        else {
                            Next(8);//ignores amount
                        }
                    }
                }

                Active = NextBool(1) ?? true;
                Status = Next(2);
                Next(6);//ignore
                VehicleOdometer = Next(7);
                Next(1);//f22 - ignore
                
                ExpiryDate = DateTime.Now.AddYears(1).ToString("yyMM");

                /*string email = Next(50);
                string[] numbers = new string[4];
                for (int i = 0; i < 4; i++)
                {
                    numbers[i] = Next(15).Replace("-","").Replace(" ", "");
                }
                if(!string.IsNullOrWhiteSpace(email) || numbers.Any(x => !string.IsNullOrWhiteSpace(x)))
                {
                    User = new UserAddViewModel
                    {
                        UserName = email,
                        PhoneNumber = numbers.FirstOrDefault(x => !string.IsNullOrWhiteSpace(x)) ?? null
                    };
                }*/

                return true;
            }
            return false;
        }

        public char Type { get; set; }

        public string PAN { get; set; }

        public string AccountCode { get; set; }

        public string HolderName { get; set; }

        public string PIN { get; set; }

        public bool Active { get; set; }

        public string Status { get; set; }

        public string? VehicleOdometer { get; set; }

        public UserAddViewModel User { get; set; }

        public string BarCode { get; set; }

        public List<AccountCardRestrictionViewModel> Restrictions { get; set; }

        public List<RewardCardDiscountViewModel> Discounts { get; set; }

        public string ExpiryDate { get; set; }
    }
}
