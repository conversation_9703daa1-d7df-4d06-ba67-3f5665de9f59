﻿using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.RewardCard
{
    public class RewardCardItemViewModel
    {
        [SwaggerSchema("A unique identifier for the RewardCard.", Nullable = true)]
        public Guid ID { get; set; }

        [SwaggerSchema("A unique identifier for the RewardCard used in transaction processing.", Nullable = false)]
        public string PAN { get; set; }

        [SwaggerSchema("A unique identifier for the RewardCard read from barcode.", Nullable = true)]
        public string? BarCode { get; set; }

        [SwaggerSchema("The date when the RewardCard expires and can no longer be used for transactions (Format: YYMM).", Nullable = false)]
        public string ExpiryDate { get; set; }

        [SwaggerSchema("Indicates whether the RewardCard is currently active and available for use.", Nullable = false)]
        public bool Active { get; set; }
    }
}
