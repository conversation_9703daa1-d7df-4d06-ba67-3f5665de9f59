﻿using Microsoft.AspNetCore.Mvc;
using OPT.API.Attributes;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.AccountCard;
using OPT.Application.ViewModels.Store;
using Swashbuckle.AspNetCore.Annotations;

namespace OPT.API.Controllers
{
    /// <summary>
    /// The AccountCard is a payment card provided to drivers of vehicles within an account's fleet. Each account can have multiple AccountCards, each with an expiration date, transaction limit, and a unique PIN. Additionally, it can be activated and deactivated as needed. It can also be associated with a specific user.
    /// 
    /// The AccountCard can be seamlessly integrated into the existing fleet management system, providing a comprehensive solution for payments and expense control within the OPT platform.
    /// 
    /// ### Features:
    /// 1. Associated with Vehicle Drivers:
    ///    - The AccountCard is designated for drivers who are part of an account's fleet, providing a convenient and secure way to make payments.
    /// 
    /// 1. Transaction Validation:
    ///    - When using the AccountCard, the API checks:
    ///      - The card's expiration date
    ///      - The account's status
    ///      - The card's status
    ///      - Available transaction limit
    ///    - If all criteria are within acceptable parameters, the transaction is authorized.
    ///    
    /// ### Restrictions
    /// When utilizing an AccountCard, it is possible to configure product restrictions(FuelGrade) specifying where it can be used.The OPT system employs a numerical representation for each type of fuel, simplifying the process.The following codes are utilized:
    /// - 01 - KEROSENE
    /// - 02 - Unleaded 91
    /// - 03 - Unleaded 95
    /// - 04 - E10
    /// - 05 - Vpower 98
    /// - 06 - PREMIUM DIESEL
    /// - 07 - Diesel
    /// - 08 - ADBLUE
    /// - 09 - Lpg
    /// 
    /// These codes allow for precise control over the types of fuel an AccountCard can access.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerControllerOrder(4)]
    public class AccountCardController : ControllerBase
    {
        private readonly IAccountCardAppService _appService;

        /// <summary>
        /// CardController Constructor
        /// </summary>
        /// <param name="appService"></param>
        public AccountCardController(IAccountCardAppService appService)
        {
            _appService = appService;
        }

        /// <summary>
        /// Add New Card
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel Add(AccountCardInternalAddViewModel card)
        {
            _appService.Add(card);
            return ResponseViewModel.Ok($"Card added with success.");
        }

        /// <summary>
        /// Add list of Cards
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("range")]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel Add(List<AccountCardAddListViewModel> cards)
        {
            _appService.Add(cards);
            return ResponseViewModel.Ok($"Cards added with success.");
        }

        /// <summary>
        /// list Cards By Account
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpGet("account/{accountid}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public List<AccountCardInternalViewModel> ListByAccount(Guid accountid)
        {
            return _appService.ListByAccount(accountid);
        }

        /// <summary>
        /// list Account Cards by stores
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("stores")]
        [ApiExplorerSettings(GroupName = "v1")]
        public List<AccountCardInternalViewModel> GetByStores(StoreFilterViewModel storeFilter)
        {
            return _appService.GetByStores(storeFilter);
        }
        
        /// <summary>
        /// list Account Cards by stores
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("v2/stores")]
        [ApiExplorerSettings(GroupName = "v1")]
        public AccountCardPaginationViewModel GetByStoresWithPagination(StoreFilterV2ViewModel storeFilter)
        {
            return _appService.GetByStoresWithPagination(storeFilter);
        }

        /// <summary>
        /// Get Card
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpGet("{cardid}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public AccountCardInternalViewModel? Get(Guid cardid)
        {
            return _appService.Get(cardid);
        }

        /// <summary>
        /// Update Card
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPut("{id}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel Update(Guid id, AccountCardInternalUpdateViewModel card)
        {
            _appService.Update(id, card);
            return ResponseViewModel.Ok($"Card updated with success.");
        }
        
        /// <summary>
        /// Delete Card
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpDelete("{cardid}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel Delete(Guid cardid)
        {
            _appService.Delete(cardid);
            return ResponseViewModel.Ok($"Card {cardid} delete with success.");
        }

        /// <summary>
        /// Associate Card With User
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("associate/card/{cardid}/user/{userid}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel AssociateWithUser(Guid cardid, Guid userid)
        {
            _appService.AssociateWithUser(cardid, userid.Equals(Guid.Empty)? null : userid);
            return ResponseViewModel.Ok($"The card {cardid} has been associated successfully.");
        }

        /// <summary>
        /// Reset PIN card
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("resetpin/{cardid}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel ResetPIN(Guid cardid)
        {
            return ResponseViewModel.Ok(_appService.ResetPIN(cardid));
            //return ResponseViewModel.Ok($"The PIN card has been reseted.");
        }
        
        /// <summary>
        /// Get Account Card Types
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpGet("accountcardtype")]
        [ApiExplorerSettings(GroupName = "v1")]
        public List<AccountCardTypeViewModel> GetAccountCardTypes()
        {
            return _appService.GetAccountCardTypes();
        }
        
        /// <summary>
        /// Get authentication for transaction card
        /// </summary>
        /// <returns></returns>
        [HttpPost("terminal/authenticate")]
        [ApiExplorerSettings(GroupName = "v1")]
        public ResponseViewModel Authenticate(AccountCardAuthenticateViewModel model)
        {
            return ResponseViewModel.Ok(_appService.Authenticate(model));
        }

        /// <summary>
        /// Authorise transaction
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Card)]
        [HttpPost("authorise")]
        [ApiExplorerSettings(GroupName = "v1")]
        public AccountCardTransactionResponseViewModel Authorise(AccountCardAuthoriseViewModel model)
        {
            return _appService.Authorise(model, HttpContext.GetAccountCardAuthenticate());
        }
        
        /// <summary>
        /// Validate extra data transaction
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Kiosk)]
        [HttpPost("validate")]
        [ApiExplorerSettings(GroupName = "v1")]
        public AccountCardTransactionResponseViewModel Validate(AccountCardValidateViewModel model)
        {
            return _appService.Validate(model);
        }
        
        /// <summary>
        /// Get Card restrictions
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Kiosk)]
        [HttpGet("restriction/{authorizationID}/{stan}/{tid}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public List<AccountCardRestrictionViewModel> GetRestrictions(int authorizationID, int stan, string tid)
        {
            return _appService.GetRestrictions(authorizationID, stan, tid);
        }
        
        /// <summary>
        /// Get Card Capabilities
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Kiosk)]
        [HttpGet("capabilities/{authorizationID}/{stan}/{tid}")]
        [ApiExplorerSettings(GroupName = "v1")]
        public AccountCardCapabilitiesViewModel GetCapabilities(int authorizationID, int stan, string tid)
        {
            return _appService.GetCapabilities(authorizationID, stan, tid);
        }
        
        /// <summary>
        /// Capture transaction
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Card)]
        [HttpPost("capture")]
        [ApiExplorerSettings(GroupName = "v1")]
        public AccountCardTransactionResponseViewModel Capture(AccountCardTransactionViewModel model)
        {
            return _appService.Capture(model, HttpContext.GetAccountCardAuthenticate());
        }
        
        /// <summary>
        /// Reverse transaction
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.Card)]
        [HttpPost("reverse")]
        [ApiExplorerSettings(GroupName = "v1")]
        public AccountCardTransactionResponseViewModel Reverse(AccountCardTransactionViewModel model)
        {
            return _appService.Reverse(model, HttpContext.GetAccountCardAuthenticate());
        }

        #region Exposed APIs

        [SwaggerOperation(
            Summary = "Add New AccountCard",
            Description = "This endpoint allows you to add a new AccountCard to the account. The expiry date format must be in YYMM."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the succesfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpPost("v1")]
        public ResponseViewModel AddPublic(
            [SwaggerRequestBody("AccountCard object informations", Required = true)]
            AccountCardAddViewModel card)
        {
            var id = _appService.Add(card, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Card added with success.", id);
        }

        [SwaggerOperation(
            Summary = "Add a range of New AccountCards",
            Description = "This endpoint allows you to add a range of new AccountCArds to the account. The expiry date format must be in YYMM."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the succesfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpPost("v1/range")]
        public ResponseViewModel AddPublic(
            [SwaggerRequestBody("Range of AccountCards object informations", Required = true)]
            List<AccountCardAddViewModel> cards)
        {
            _appService.Add(cards, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Cards added with success.");
        }

        [SwaggerOperation(
            Summary = "List AccountCard By Account",
            Description = "This endpoint allows you to retrieve a list of AccountCards associated with a specific account."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return a list of AccountCards object.", typeof(List<AccountCardViewModel>))]
        [Authorize(Role.API)]
        [HttpGet("v1/account/{accountid}")]
        public List<AccountCardViewModel> ListByAccountPublic(
            [SwaggerParameter("Account ID", Required = true)]
            Guid accountid)
        {
            return _appService.ListByAccount(accountid, HttpContext.GetApiKey());
        }

        [SwaggerOperation(
            Summary = "Get AccountCard By ID",
            Description = "This endpoint allows you to retrieve detailed information about a specific AccountCard associated with a particular account."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the AccountCard object.", typeof(AccountCardViewModel))]
        [Authorize(Role.API)]
        [HttpGet("v1/{id}")]
        public AccountCardViewModel? GetPublic(
            [SwaggerParameter("AccountCard ID", Required = true)]
            Guid id)
        {
            return _appService.Get(id, HttpContext.GetApiKey());
        }

        [SwaggerOperation(
            Summary = "Update Account Card",
            Description = "This endpoint allows you to update the details of a specific AccountCard associated with a particular account. The expiry date format must be in YYMM."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return successfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpPut("v1/{id}")]
        public ResponseViewModel UpdatePublic(
            [SwaggerParameter("AccountCard ID", Required = true)]
            Guid id,
            [SwaggerRequestBody("AccountCard object informations", Required = true)]
            AccountCardUpdateViewModel card)
        {
            _appService.Update(id, card, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Card updated with success.");
        }

        [SwaggerOperation(
            Summary = "Delete AccountCard",
            Description = "This endpoint allows you to remove a specific AccountCard associated with a particular account."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return successfull object.", typeof(ResponseViewModel))]
        [Authorize(Role.API)]
        [HttpDelete("v1/{id}")]
        public ResponseViewModel DeletePublic(
            [SwaggerParameter("AccountCard ID", Required = true)]
            Guid id)
        {
            _appService.Delete(id, HttpContext.GetApiKey());
            return ResponseViewModel.Ok($"Card {id} delete with success.");
        }

        /*/// <summary>
        /// Associate Card With User
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost("associate/card/{cardid}/user/{userid}")]
        public ResponseViewModel AssociateWithUser(Guid cardid, Guid userid)
        {
            _appService.AssociateWithUser(cardid, userid.Equals(Guid.Empty) ? null : userid);
            return ResponseViewModel.Ok($"The card {cardid} has been associated successfully.");
        }*/

        [SwaggerOperation(
            Summary = "Reset AccountCard PIN",
            Description = "This API allows you to reset the PIN of a specific AccountCard."
        )]
        [SwaggerResponse(200, "Upon a successful request, the API will return the new 4-digit PIN.", typeof(AccountCardPINViewModel))]
        [Authorize(Role.API)]
        [HttpPost("v1/resetpin/{id}")]
        public AccountCardPINViewModel ResetPINPublic(
            [SwaggerParameter("AccountCard ID", Required = true)]
            Guid id)
        {
            return _appService.ResetPIN(id, HttpContext.GetApiKey());
        }

        #endregion

    }
}
