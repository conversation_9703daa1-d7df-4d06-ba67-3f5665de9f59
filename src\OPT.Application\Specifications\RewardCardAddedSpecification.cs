﻿using OPT.Application.ViewModels.RewardCard;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Specification.Interface;
using System.Globalization;

namespace OPT.Application.Specifications
{
    internal class RewardCardAddedSpecification : ISpecificationConfiguration<RewardCardAddViewModel>
    {

        private IAccountCardService _service;

        public RewardCardAddedSpecification(IAccountCardService service)
        {
            _service = service;
        }

        public ISpecification<RewardCardAddViewModel> Map(ISpecification<RewardCardAddViewModel> builder)
        {
            builder.SetStopOnFirstFailure(true);

            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.PAN), "PAN is required!", 412);
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.PAN) && (x.PAN.Length == 16 || x.PAN.Length == 19) && x.PAN.All(c => char.<PERSON>(c)), "The PAN number is incorrect!", 412);
            builder.IsSatisfiedBy(x =>
            {
                if (!string.IsNullOrEmpty(x.PAN))
                {
                    var card = _service.GetByPANOrBarCode(x.PAN);
                    return card == null;
                }
                return false;
            }, "Registered PAN number already exists!", 409);
            builder.IsSatisfiedBy(x => {
                var culture = new CultureInfo("en-AU");
                culture.Calendar.TwoDigitYearMax = 2099;
                if (!string.IsNullOrEmpty(x.ExpiryDate) && DateTime.TryParseExact(x.ExpiryDate, "yyMM", culture, DateTimeStyles.None, out DateTime expirationDate))
                {
                    if (expirationDate >= DateTime.Now.Date.AddDays(-DateTime.Now.Day + 1))
                    {
                        return true;
                    }
                }
                return false;
            }, "The Expiry Date is invalid!", 412);

            return builder;
        }
    }
}
