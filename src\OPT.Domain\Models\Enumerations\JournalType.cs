﻿using System.ComponentModel;

namespace OPT.Domain.Models.Enumerations
{
    public enum JournalType
    {
        Payment = 1,
        [Description("Positive Adjustment")]
        PositiveAdjustment,
        [Description("Negative Adjustment")]
        NegativeAdjustment,
        Fee
    }

    public static class JournalTypeExtension 
    {
        public static string GetDescription(this JournalType value)
        {
            var attribute = value.GetType()
                .GetField(value.ToString())?
                .GetCustomAttributes(typeof(DescriptionAttribute), false)
                .SingleOrDefault() as DescriptionAttribute;
            return attribute == null ? value.ToString() : attribute.Description;
        }
    }
}
