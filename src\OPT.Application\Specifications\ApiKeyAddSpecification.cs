﻿using OPT.Application.ViewModels.ApiKey;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class ApiKeyAddSpecification : ISpecificationConfiguration<ApiKeyAddViewModel>
    {
        public ISpecification<ApiKeyAddViewModel> Map(ISpecification<ApiKeyAddViewModel> builder)
        {
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.Name), "Name is required!");
            builder.IsSatisfiedBy(x => x.Store != null, "Store is required!");

            return builder;
        }
    }
}
