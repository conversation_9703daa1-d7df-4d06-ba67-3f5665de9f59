﻿using OPT.Application.ViewModels.VehicleRegistration;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class VehicleRegistrationAddUpdateSpecification : ISpecificationConfiguration<VehicleRegistrationAddViewModel>
    {
        private readonly IVehicleRegistrationService _service;
        private readonly Guid _id;
        public VehicleRegistrationAddUpdateSpecification(IVehicleRegistrationService service, Guid id)
        {
            _service = service;
            _id = id;
        }

        public ISpecification<VehicleRegistrationAddViewModel> Map(ISpecification<VehicleRegistrationAddViewModel> builder)
        {
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.RegistrationNumber), "The Registration Number is incorrect!", 412);

            builder.IsSatisfiedBy(x => 
            { 
                var reg = _service.GetByRegistrationNumber(x.RegistrationNumber);
                return !(reg != null && !reg.ID.Equals(_id));
            }, "This registration number already exists.", 409);


            return builder;
        }
    }
}
