﻿using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Interfaces.Services;
using OPT.Domain.Models;

namespace OPT.Domain.Services
{
    public class IdentityService : IIdentityService
    {
        private readonly IIdentityRepository _repository;

        public IdentityService(IIdentityRepository repository)
        {
            _repository = repository;
        }

        public async Task<User?> GetByName(string username)
        {
            return await _repository.GetByName(username);
        }

        public async Task<User?> GetById(Guid id)
        {
            return await _repository.GetById(id);
        }

        public async Task<User?> GetByEmail(string email)
        {
            return await _repository.GetByEmail(email);
        }
        
        public async Task<User?> GetByPhoneNumber(string phoneNumber)
        {
            return await _repository.GetByPhoneNumber(phoneNumber);
        }

        public async Task<Guid?> Add(User model)
        {
            return await _repository.Add(model);
        }

        public async Task Update(User model)
        {
            await _repository.Update(model);
        }

        public async Task<string> ConfirmEmail(string email)
        {
            return await _repository.ConfirmEmail(email);
        }

        public async Task ResetPassword(Guid? id, string? email, string passToken, string newPassword)
        {
            await _repository.ResetPassword(id, email, passToken, newPassword);
        }

        public async Task Remove(Guid id)
        {
            await _repository.Remove(id);
        }

        public async Task<List<User>> GetUsersByIds(List<string> ids)
        {
            return await _repository.GetUsersByIds(ids);
        }

        public async Task<User?> Authenticate(string username, string password)
        {
            return await _repository.Authenticate(username, password);
        }

        public async Task<bool> ForgotPassword(string userName, string companyHash)
        {
            return await _repository.ForgotPassword(userName, companyHash);
        }
        
        /*public IEnumerable<User> GetAll()
        {
            //return _repository.GetAll();
            return new List<User>() 
            {
                new User
                {
                    Email = "<EMAIL>",
                    EmailConfirmed = true,
                    Id = Guid.NewGuid(),
                    NormalizedUserName = "<EMAIL>",
                    Role = "User",
                    UserName = "<EMAIL>"
                }
            };
        }
        
        public async Task UpdateRole(Guid id, string newrole)
        {
            await _repository.UpdateRole(id, newrole);
        }*/

        public async Task SendEmail(string email, string subject, string htmlMessage)
        {
            await _repository.SendEmail(email, subject, htmlMessage);
        }

        public void Dispose()
        {
            _repository.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
