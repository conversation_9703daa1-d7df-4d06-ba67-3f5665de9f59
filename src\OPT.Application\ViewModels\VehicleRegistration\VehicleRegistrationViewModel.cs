﻿using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.VehicleRegistration
{
    public class VehicleRegistrationViewModel
    {
        [SwaggerSchema("The unique ID of the account for which you want to retrieve the vehicle registrations.", Nullable = false)]
        public Guid ID { get; set; }

        [SwaggerSchema("The registration number or identifier for the vehicle.", Nullable = false)]
        public string RegistrationNumber { get; set; }

        [SwaggerSchema("Activate or deactivate vahicle.", Nullable = false)]
        public bool Active { get; set; }
    }
}
