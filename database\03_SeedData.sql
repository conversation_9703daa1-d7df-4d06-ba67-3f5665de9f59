-- =============================================
-- OPTdb Seed Data Script
-- Description: Inserts initial seed data for essential tables
-- =============================================

USE OPTdb;
GO

PRINT 'Starting seed data insertion...';

-- =============================================
-- Seed AccountCardTypes
-- =============================================
PRINT 'Seeding AccountCardTypes...';

-- Clear existing data (if any)
DELETE FROM AccountCardTypes;

-- Insert seed data for AccountCardTypes
INSERT INTO AccountCardTypes (ID, Description, Payment, Reward) VALUES
('P', 'Payment Card', 1, 0),
('R', 'Reward Card', 0, 1),
('B', 'Both Payment and Reward', 1, 1),
('N', 'None', 0, 0);

PRINT 'AccountCardTypes seeded successfully.';

-- =============================================
-- Seed Sample Store
-- =============================================
PRINT 'Seeding sample Store...';

-- Insert a sample store for development
DECLARE @StoreID UNIQUEIDENTIFIER = NEWID();
DECLARE @TenantID UNIQUEIDENTIFIER = NEWID();

INSERT INTO Stores (ID, TenantID, Name, ABN, Address, Suburb, PostCode, Region, State, Phone, Type, ProductPromotions, CigarettePromotions, Suspended, Code3Digits, Code4Digits)
VALUES (
    @StoreID,
    @TenantID,
    'Development Store',
    '***********',
    '123 Development Street',
    'Dev Suburb',
    '1234',
    'Development Region',
    'NSW',
    '02-1234-5678',
    'Fuel Station',
    1,
    0,
    0,
    '001',
    '0001'
);

PRINT 'Sample Store seeded successfully.';

-- =============================================
-- Seed Sample Account
-- =============================================
PRINT 'Seeding sample Account...';

DECLARE @AccountID UNIQUEIDENTIFIER = NEWID();

INSERT INTO Accounts (ID, Name, Email, Active, MobileNumber, CreditLimit, Code, FlagOdometer, StoreID, TenantID)
VALUES (
    @AccountID,
    'Development Account',
    '<EMAIL>',
    1,
    '**********',
    1000.00,
    'DEV001',
    'Y',
    @StoreID,
    @TenantID
);

PRINT 'Sample Account seeded successfully.';

-- =============================================
-- Seed Sample Kiosk
-- =============================================
PRINT 'Seeding sample Kiosk...';

DECLARE @KioskID UNIQUEIDENTIFIER = NEWID();

INSERT INTO Kiosks (ID, Name, StoreID, TenantID, Active, SerialNumber, Model, IPAddress, Port, Location)
VALUES (
    @KioskID,
    'Development Kiosk 1',
    @StoreID,
    @TenantID,
    1,
    'DEV-KIOSK-001',
    'DevModel-X1',
    '*************',
    8080,
    'Pump 1'
);

PRINT 'Sample Kiosk seeded successfully.';

-- =============================================
-- Seed KioskInfo for the sample Kiosk
-- =============================================
PRINT 'Seeding KioskInfo...';

INSERT INTO KioskInfos (KioskID, LastHeartbeat, Status, Version, LastUpdate)
VALUES (
    @KioskID,
    GETUTCDATE(),
    'Online',
    '1.0.0',
    GETUTCDATE()
);

PRINT 'KioskInfo seeded successfully.';

-- =============================================
-- Seed Sample ParameterConfigs
-- =============================================
PRINT 'Seeding ParameterConfigs...';

INSERT INTO ParameterConfigs (ID, Name, Value, Description, Category, DataType, IsActive)
VALUES 
(NEWID(), 'MaxTransactionAmount', '500.00', 'Maximum transaction amount allowed', 'Transaction', 'decimal', 1),
(NEWID(), 'SessionTimeout', '300', 'Session timeout in seconds', 'System', 'int', 1),
(NEWID(), 'EnableRewards', 'true', 'Enable reward card functionality', 'Features', 'bool', 1),
(NEWID(), 'DefaultCurrency', 'AUD', 'Default currency code', 'System', 'string', 1),
(NEWID(), 'MinFuelAmount', '5.00', 'Minimum fuel purchase amount', 'Transaction', 'decimal', 1);

PRINT 'ParameterConfigs seeded successfully.';

-- =============================================
-- Seed Sample AccountCard
-- =============================================
PRINT 'Seeding sample AccountCard...';

DECLARE @AccountCardID UNIQUEIDENTIFIER = NEWID();

INSERT INTO AccountCards (ID, AccountID, CardNumber, CardType, Active, ExpiryDate, IssueDate, PIN, Balance, CreditLimit)
VALUES (
    @AccountCardID,
    @AccountID,
    '***********23456',
    'P',
    1,
    DATEADD(YEAR, 2, GETDATE()),
    GETDATE(),
    '1234',
    100.00,
    1000.00
);

PRINT 'Sample AccountCard seeded successfully.';

-- =============================================
-- Seed Sample VehicleRegistration
-- =============================================
PRINT 'Seeding sample VehicleRegistration...';

DECLARE @VehicleRegID UNIQUEIDENTIFIER = NEWID();

INSERT INTO VehicleRegistrations (ID, RegistrationNumber, State, Make, Model, Year, FuelType, Active)
VALUES (
    @VehicleRegID,
    'DEV123',
    'NSW',
    'Toyota',
    'Camry',
    2020,
    'Petrol',
    1
);

-- Link vehicle to account
INSERT INTO AccountVehicleRegistrations (ID, AccountId, VehicleRegistrationId, Active, CreatedDate)
VALUES (
    NEWID(),
    @AccountID,
    @VehicleRegID,
    1,
    GETDATE()
);

PRINT 'Sample VehicleRegistration seeded successfully.';

-- =============================================
-- Seed Sample API Key
-- =============================================
PRINT 'Seeding sample API Key...';

INSERT INTO APIKeys (ID, KeyName, KeyValue, IsActive, CreatedDate, ExpiryDate, Description)
VALUES (
    NEWID(),
    'Development API Key',
    'dev-api-key-***********234567890',
    1,
    GETDATE(),
    DATEADD(YEAR, 1, GETDATE()),
    'Development API key for testing'
);

PRINT 'Sample API Key seeded successfully.';

-- =============================================
-- Display summary
-- =============================================
PRINT '';
PRINT '=== Seed Data Summary ===';
PRINT 'AccountCardTypes: ' + CAST((SELECT COUNT(*) FROM AccountCardTypes) AS VARCHAR(10));
PRINT 'Stores: ' + CAST((SELECT COUNT(*) FROM Stores) AS VARCHAR(10));
PRINT 'Accounts: ' + CAST((SELECT COUNT(*) FROM Accounts) AS VARCHAR(10));
PRINT 'Kiosks: ' + CAST((SELECT COUNT(*) FROM Kiosks) AS VARCHAR(10));
PRINT 'KioskInfos: ' + CAST((SELECT COUNT(*) FROM KioskInfos) AS VARCHAR(10));
PRINT 'ParameterConfigs: ' + CAST((SELECT COUNT(*) FROM ParameterConfigs) AS VARCHAR(10));
PRINT 'AccountCards: ' + CAST((SELECT COUNT(*) FROM AccountCards) AS VARCHAR(10));
PRINT 'VehicleRegistrations: ' + CAST((SELECT COUNT(*) FROM VehicleRegistrations) AS VARCHAR(10));
PRINT 'AccountVehicleRegistrations: ' + CAST((SELECT COUNT(*) FROM AccountVehicleRegistrations) AS VARCHAR(10));
PRINT 'APIKeys: ' + CAST((SELECT COUNT(*) FROM APIKeys) AS VARCHAR(10));
PRINT '';
PRINT 'Seed data insertion completed successfully!';
GO
