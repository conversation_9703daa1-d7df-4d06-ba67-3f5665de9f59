﻿using TheArtOfDev.HtmlRenderer.PdfSharp;

namespace OPT.Application.Helper
{
    public class PDFHelper
    {
        public static byte[] HtmlToPDF(string html)
        {
            byte[] res = null;
            using (MemoryStream ms = new MemoryStream())
            {
                var pdf = PdfGenerator.GeneratePdf(html, PdfSharpCore.PageSize.A4);
                pdf.Save(ms);
                res = ms.ToArray();
            }
            return res;
        }

    }
}
