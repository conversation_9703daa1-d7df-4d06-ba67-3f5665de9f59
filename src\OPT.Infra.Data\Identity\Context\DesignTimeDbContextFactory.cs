﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace OPT.Infra.Data.Identity.Context
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<IdentityOPTContext>
    {
        public IdentityOPTContext CreateDbContext(string[] args)
        {
            // Build configuration to read from appsettings files
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Path.Combine(Directory.GetCurrentDirectory(), "../OPT.API"))
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile("appsettings.Development.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            string connectionString = configuration.GetConnectionString("DefaultConnection");

            Console.WriteLine($"DesignTimeDbContextFactory: using connection string = {connectionString}");

            if (string.IsNullOrWhiteSpace(connectionString))
            {
                throw new InvalidOperationException("Could not find connection string named 'DefaultConnection'");
            }

            DbContextOptionsBuilder<IdentityOPTContext> dbContextOptionsBuilder =
                new DbContextOptionsBuilder<IdentityOPTContext>();

            IdentityOPTContext.AddBaseOptions(dbContextOptionsBuilder, connectionString);

            return new IdentityOPTContext(dbContextOptionsBuilder.Options);
        }
    }
}
