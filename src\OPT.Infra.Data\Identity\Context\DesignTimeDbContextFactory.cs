﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace OPT.Infra.Data.Identity.Context
{
    /*public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<IdentityOPTContext>
    {
        public IdentityOPTContext CreateDbContext(string[] args)
        {
            string connectionString = "Data Source=localhost;Initial Catalog=OPTDB;Persist Security Info=True;User ID=sa;Password=****";

            Console.WriteLine($"DesignTimeDbContextFactory: using connection string = {connectionString}");

            if (string.IsNullOrWhiteSpace(connectionString))
            {
                throw new InvalidOperationException("Could not find connection string named 'CleanArchitectureIdentity'");
            }

            DbContextOptionsBuilder<IdentityOPTContext> dbContextOptionsBuilder =
                new DbContextOptionsBuilder<IdentityOPTContext>();

            IdentityOPTContext.AddBaseOptions(dbContextOptionsBuilder, connectionString);

            return new IdentityOPTContext(dbContextOptionsBuilder.Options);
        }
    }*/
}
