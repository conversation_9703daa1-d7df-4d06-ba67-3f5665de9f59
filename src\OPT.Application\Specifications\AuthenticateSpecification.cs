﻿using OPT.Application.ViewModels.User;
using OPT.Domain.Specification.Interface;

namespace OPT.Application.Specifications
{
    internal class AuthenticateSpecification : ISpecificationConfiguration<AuthenticateViewModel>
    {
        public ISpecification<AuthenticateViewModel> Map(ISpecification<AuthenticateViewModel> builder)
        {
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.Username), "Username is required!");
            builder.IsSatisfiedBy(x => !string.IsNullOrEmpty(x.Password), "Password is required!");

            return builder;
        }
    }
}
