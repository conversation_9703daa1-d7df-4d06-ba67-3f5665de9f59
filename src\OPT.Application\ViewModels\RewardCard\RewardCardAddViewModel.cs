﻿using OPT.Domain.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace OPT.Application.ViewModels.RewardCard
{
    public class RewardCardAddViewModel
    {
        [SwaggerSchema("A unique identifier for the RewardCard used in transaction processing.", Nullable = false)]
        public string PAN { get; set; }

        [SwaggerSchema("A unique identifier for the RewardCard read from barcode.", Nullable = true)]
        public string? BarCode { get; set; }

        [SwaggerSchema("The date when the RewardCard expires and can no longer be used for transactions (Format: YYMM).", Nullable = false)]
        public string ExpiryDate { get; set; }

        [SwaggerSchema("Indicates whether the RewardCard is currently active and available for use.", Nullable = false)]
        public bool Active { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply kerosene.", Nullable = true)]
        public long? KeroseneDiscount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of kerosene for applying discount.", Nullable = true)]
        public long? KeroseneCap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply Unleaded 91.", Nullable = true)]
        public long? Unleaded91Discount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of Unleaded 91 for applying discount.", Nullable = true)]
        public long? Unleaded91Cap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply Unleaded 95.", Nullable = true)]
        public long? Unleaded95Discount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of Unleaded 95 for applying discount.", Nullable = true)]
        public long? Unleaded95Cap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply E10.", Nullable = true)]
        public long? E10Discount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of E10 for applying discount.", Nullable = true)]
        public long? E10Cap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply Vpower 98.", Nullable = true)]
        public long? Vpower98Discount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of Vpower 98 for applying discount.", Nullable = true)]
        public long? Vpower98Cap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply Premium Diesel.", Nullable = true)]
        public long? PremiumDieselDiscount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of Premium Diesel for applying discount.", Nullable = true)]
        public long? PremiumDieselCap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply Diesel.", Nullable = true)]
        public long? DieselDiscount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of Diesel for applying discount.", Nullable = true)]
        public long? DieselCap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply ADBlue.", Nullable = true)]
        public long? ADBlueDiscount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of ADBlue for applying discount.", Nullable = true)]
        public long? ADBlueCap { get; set; }

        [SwaggerSchema("Discount of cents per litre applied to supply LPG.", Nullable = true)]
        public long? LpgDiscount { get; set; }

        [SwaggerSchema("Maximum limit of litres per supply of LPG for applying discount.", Nullable = true)]
        public long? LpgCap { get; set; }

        public List<RewardCardDiscount> getDiscounts()
        {
            var discounts = new List<RewardCardDiscount>();
            for (int i = 1; i <= 9; i++)
            {
                var discount = new RewardCardDiscount();
                discount.FuelGrade = i.ToString("D2");
                switch (i)
                {
                    case 1:
                        discount.Discount = KeroseneDiscount ?? 0;
                        discount.Cap = KeroseneCap;
                        break;
                    case 2:
                        discount.Discount = Unleaded91Discount ?? 0;
                        discount.Cap = Unleaded91Cap;
                        break;
                    case 3:
                        discount.Discount = Unleaded95Discount ?? 0;
                        discount.Cap = Unleaded95Cap;
                        break;
                    case 4:
                        discount.Discount = E10Discount ?? 0;
                        discount.Cap = E10Cap;
                        break;
                    case 5:
                        discount.Discount = Vpower98Discount ?? 0;
                        discount.Cap = Vpower98Cap;
                        break;
                    case 6:
                        discount.Discount = PremiumDieselDiscount ?? 0;
                        discount.Cap = PremiumDieselCap;
                        break;
                    case 7:
                        discount.Discount = DieselDiscount ?? 0;
                        discount.Cap = DieselCap;
                        break;
                    case 8:
                        discount.Discount = ADBlueDiscount ?? 0;
                        discount.Cap = ADBlueCap;
                        break;
                    case 9:
                        discount.Discount = LpgDiscount ?? 0;
                        discount.Cap = LpgCap;
                        break;
                    default:
                        break;
                }
                discounts.Add(discount);
            }
            return discounts;
        }
    }
}
