﻿namespace OPT.Application.ViewModels
{
    public class ResponseViewModel
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public Guid? Id { get; set; }

        protected ResponseViewModel()
        {
            Message = string.Empty;
        }

        public static ResponseViewModel Ok(string message = "", Guid? id = null)
        {
            return new ResponseViewModel
            {
                Success = true,
                Message = message,
                Id = id
            };
        }
        
        public static ResponseViewModel Error(string message)
        {
            return new ResponseViewModel
            {
                Success = false,
                Message = message
            };
        }
    }
}
