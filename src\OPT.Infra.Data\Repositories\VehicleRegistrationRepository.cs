﻿using Microsoft.EntityFrameworkCore;
using OPT.Domain.Interfaces.Repositories;
using OPT.Domain.Models;
using OPT.Infra.Data.Context;

namespace OPT.Infra.Data.Repositories
{
    public class VehicleRegistrationRepository : Repository<VehicleRegistration>, IVehicleRegistrationRepository
    {
        public VehicleRegistrationRepository(OPTContext context) : base(context)
        {
        }
        
        public List<VehicleRegistration> GetByAccount(Guid accountId)
        {
            return _context.Set<AccountVehicleRegistration>()
                .Include(x => x.VehicleRegistration)
                .AsNoTracking()
                .Where(x => x.AccountId.Equals(accountId))
                .Select(x => x.VehicleRegistration)
                .ToList();
        }

        public VehicleRegistration? GetByRegistrationNumber(string registrationNumber)
        {
            return _dbset.AsNoTracking().FirstOrDefault(x => x.RegistrationNumber == registrationNumber);
        }

        public void AddAccount(AccountVehicleRegistration accountVehicleRegistration)
        {
            _context.Set<AccountVehicleRegistration>().Add(accountVehicleRegistration);
        }

        public void RemoveAccount(AccountVehicleRegistration accountVehicleRegistration)
        {
            _context.Set<AccountVehicleRegistration>().Remove(accountVehicleRegistration);
        }
        
        public List<AccountVehicleRegistration> GetAccountByVehicleRegistration(Guid vehicleRegistrationId)
        {
            return _context.Set<AccountVehicleRegistration>().Where(x => x.VehicleRegistrationId.Equals(vehicleRegistrationId)).ToList();
        }

        public bool Valid(Guid id, Guid storeId)
        {
            return _context.Set<Account>().Any(x => x.StoreID.Equals(storeId) && x.AccountVehicleRegistrations.Any(y => y.VehicleRegistrationId.Equals(id)));
        }
    }
}
