﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using OPT.Infra.Data.Identity.Model;

namespace OPT.Infra.Data.Identity.Context
{
    public class IdentityOPTContext : IdentityDbContext<OPTUser>
    {
        public IdentityOPTContext(DbContextOptions<IdentityOPTContext> options) : base(options)
        {
        }
        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
        }

        public static void AddBaseOptions(DbContextOptionsBuilder<IdentityOPTContext> builder, string connectionString)
        {
            if (builder == null)
                throw new ArgumentNullException(nameof(builder));

            if (string.IsNullOrWhiteSpace(connectionString))
                throw new ArgumentException("Connection string must be provided", nameof(connectionString));

            builder.UseSqlServer(connectionString, x =>
            {
                x.EnableRetryOnFailure();
            });
        }
    }
}
