﻿using Microsoft.AspNetCore.Mvc;
using OPT.API.Attributes;
using OPT.Application.Helper;
using OPT.Application.Interfaces;
using OPT.Application.ViewModels;
using OPT.Application.ViewModels.Store;

namespace OPT.API.Controllers
{
    /// <summary>
    /// Store Controller
    /// </summary>
    [ApiExplorerSettings(GroupName = "v1")]
    [Route("api/[controller]")]
    [ApiController]
    public class StoreController : ControllerBase
    {
        private readonly IStoreAppService _appService;

        /// <summary>
        /// StoreController Constructor
        /// </summary>
        /// <param name="appService"></param>
        public StoreController(IStoreAppService appService)
        {
            _appService = appService;
        }

        /// <summary>
        /// Add New Store
        /// </summary>
        /// <returns></returns>
        [Authorize(Role.POSMaster)]
        [HttpPost]
        public ResponseViewModel Add(StoreViewModel store)
        {
            _appService.Add(store);
            return ResponseViewModel.Ok($"Store {store.Name} added with success.");
        }

    }
}
