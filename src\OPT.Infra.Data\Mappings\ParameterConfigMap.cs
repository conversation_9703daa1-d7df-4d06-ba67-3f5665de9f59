﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class ParameterConfigMap : Mapping<ParameterConfig>
    {
        public override void Map(EntityTypeBuilder<ParameterConfig> builder)
        {
            builder.ToTable("ParameterConfigs");

            builder.HasKey(x => x.ID);

            base.Map(builder);
        }
    }
}
