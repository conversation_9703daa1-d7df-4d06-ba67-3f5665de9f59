﻿using Microsoft.AspNetCore.Http;
using OPT.Application.ViewModels.Kiosk;
using OPT.Domain.Models;

namespace OPT.Application.Interfaces
{
    public interface IKioskAppService : IDisposable
    {
        //AccountViewModel GetById(Guid id);
        void AddOrUpdate(KioskAddViewModel viewModel);

        KioskValidResponseViewModel Valid(KioskValidViewModel viewModel);

        KioskValidResponseViewModel Valid(string deviceId);

        List<KioskConfigViewModel> GetConfigsByKiosk(Kiosk kiosk);

        string Authenticate(KioskAuthenticateViewModel viewModel);

        List<KioskViewModel> GetByStores(List<Guid> storesId);

        List<KioskNameViewModel> GetByStores(ApiKey apiKey);

        void UpdateActive(Guid id, bool ative);

        KioskViewModel? GetById(Guid id);

        List<ParameterConfigViewModel> GetAllParamaterConfigs();

        Task UploadFile(IFormFile? file, Kiosk kiosk, bool deleteOld);

        void AddInfo(KioskInfoViewModel info, Kiosk kiosk);
    }
}
