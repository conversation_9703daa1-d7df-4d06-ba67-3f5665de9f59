﻿namespace OPT.Application.ViewModels.Transaction
{
    public class CardReceiptViewModel
    {
        public string TerminalTransactionId { get; set; }
        public string TransactionDateTime { get; set; }

        public string TransactionType { get; set; }
        public string TransactionStatus { get; set; }
        public string TransactionPartialAmount { get; set; }
        public string TransactionFinalAmount { get; set; }
        public string TransactionGSTAmount { get; set; }
        public string TransactionCurrency { get; set; }

        public string RewardTotalDiscount { get; set; }
        public string RewardDiscountPerLiter { get; set; }

        public string ApplicationId { get; set; }
        public string ApplicationLabel { get; set; }
        public string CardSignature { get; set; }
        public string CardATC { get; set; }
        public string TID { get; set; }
        public string STAN { get; set; }
        public string HostResponseCode { get; set; }
        public string HostResponse { get; set; }
        public string? AuthorisationId { get; set; }
        public string PanSeqNo { get; set; }
        public string CVM { get; set; }
        public string StoreName { get; set; }
        public string StoreABN { get; set; }

        public string StoreAddress1 { get; set; }

        public string StoreAddress2 { get; set; }

        public string StoreCity { get; set; }
        public string StorePostalCode { get; set; }
        public string StoreState { get; set; }

        public string KioskName { get; set; }
        public string FuelGradeName { get; set; }

        public string FuelPricePerLitre { get; set; }

        public string FuelPumpNumber { get; set; }

        public string FuelTransactionNumber { get; set; }

        public string FuelValue { get; set; }

        public string FuelVolume { get; set; }
        public string InvoiceFooter { get; set; }

        public string? AccountEmailAddress { get; set; }
        public string? AccountName { get; set; }
        public string? AccountUser { get; set; }

        public string? TransactionNumberDetail { get; set; }

        public string? TransactionTotalAmount { get; set; }

        public string? VehicleOdometer { get; set; }

        public string? VehicleRegistration { get; set; }
    }
}
