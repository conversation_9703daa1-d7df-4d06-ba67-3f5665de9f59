﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using OPT.Infra.Data.Context;

#nullable disable

namespace OPT.Infra.Data.Migrations
{
    [DbContext(typeof(OPTContext))]
    [Migration("20250707025725_InitialOPTDbCreation")]
    partial class InitialOPTDbCreation
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("OPT.Domain.Models.Account", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("CreditLimit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FlagOdometer")
                        .HasColumnType("nvarchar(1)");

                    b.Property<string>("MobileNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("StoreID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TenantID")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("ID");

                    b.HasIndex("StoreID");

                    b.ToTable("Accounts", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountBalance", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AccountId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("LastBalanceDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ID");

                    b.HasIndex("AccountId");

                    b.ToTable("AccountBalances", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountCard", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AccountId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("BarCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExpiryDate")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HolderName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PAN")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PAN19")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PIN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("StoreID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("TransactionLimit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(1)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleOdometer")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.HasIndex("AccountId");

                    b.HasIndex("StoreID");

                    b.HasIndex("Type");

                    b.ToTable("AccountCards", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountCardRestriction", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AccountCardID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FuelGrade")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.HasIndex("AccountCardID");

                    b.ToTable("AccountCardRestrictions", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountCardTransaction", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AccountCardID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<long?>("AmountAuthorized")
                        .HasColumnType("bigint");

                    b.Property<long?>("AmountCaptured")
                        .HasColumnType("bigint");

                    b.Property<long?>("AmountReversed")
                        .HasColumnType("bigint");

                    b.Property<string>("AuthCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("FinalizedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("HostResponse")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("KioskID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("PAN")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte[]>("PIN")
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("RRN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("STAN")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("STAN"));

                    b.Property<string>("TerminalID")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Track2")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("TransactionReferece")
                        .HasColumnType("bigint");

                    b.HasKey("ID");

                    b.HasIndex("AccountCardID");

                    b.ToTable("AccountCardTransactions", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountCardType", b =>
                {
                    b.Property<string>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(1)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Payment")
                        .HasColumnType("bit");

                    b.Property<bool>("Reward")
                        .HasColumnType("bit");

                    b.HasKey("ID");

                    b.ToTable("AccountCardTypes", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountDiscount", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AccountId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<int>("DiscountTypeId")
                        .HasColumnType("int");

                    b.Property<decimal>("DiscountValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ID");

                    b.ToTable("AccountDiscounts", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountJournal", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AccountId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("JournalDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("JournalTypeId")
                        .HasColumnType("int");

                    b.Property<string>("POSMasterUserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Reason")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("AccountJournals", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountVehicleRegistration", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AccountId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("VehicleRegistrationId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("ID");

                    b.HasIndex("AccountId");

                    b.HasIndex("VehicleRegistrationId");

                    b.ToTable("AccountVehicleRegistrations", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.ApiKey", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccessToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<DateTime>("CreatedDateTime")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("StoreID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("TokenDateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("ID");

                    b.HasIndex("StoreID");

                    b.ToTable("APIKeys", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.FuelTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<long>("FuelAmount")
                        .HasColumnType("bigint");

                    b.Property<string>("FuelGradeId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FuelGradeName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("FuelPrice")
                        .HasColumnType("bigint");

                    b.Property<long>("FuelVolume")
                        .HasColumnType("bigint");

                    b.Property<int>("PumpNumber")
                        .HasColumnType("int");

                    b.Property<long>("TransactionSeqNumber")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("FuelTransactions", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.Kiosk", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("DeviceID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("StoreID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ValidateHash")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.HasIndex("StoreID");

                    b.ToTable("Kiosks", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.KioskConfig", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<Guid>("KioskID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.HasIndex("KioskID");

                    b.ToTable("KioskConfigs", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.KioskInfo", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<Guid>("KioskID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastOnline")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastScreen")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Version")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.HasIndex("KioskID")
                        .IsUnique();

                    b.ToTable("KioskInfos", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.ParameterConfig", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("DataType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Defaultvalue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Group")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InputType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Length")
                        .HasColumnType("int");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("PID")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Required")
                        .HasColumnType("bit");

                    b.Property<string>("Select")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("ParameterConfigs", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.RewardCardDiscount", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AccountCardID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<long?>("Cap")
                        .HasColumnType("bigint");

                    b.Property<long>("Discount")
                        .HasColumnType("bigint");

                    b.Property<string>("FuelGrade")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.HasIndex("AccountCardID");

                    b.ToTable("RewardCardDiscounts", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.RewardCardTransaction", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("KioskId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RewardCardDiscountID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<long>("TotalDiscountApplied")
                        .HasColumnType("bigint");

                    b.Property<Guid>("TransactionID")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("ID");

                    b.HasIndex("KioskId");

                    b.HasIndex("RewardCardDiscountID");

                    b.HasIndex("TransactionID");

                    b.ToTable("RewardCardTransactions", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.Store", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ABN")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("CigarettePromotions")
                        .HasColumnType("bit");

                    b.Property<string>("Code3Digits")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Code4Digits")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PostCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ProductPromotions")
                        .HasColumnType("bit");

                    b.Property<string>("Region")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Suburb")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Suspended")
                        .HasColumnType("bit");

                    b.Property<Guid>("TenantID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("Stores", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.Transaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AccountCardId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AccountId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ApplicationId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ApplicationLabel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("AuthorisedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("AuthorizationID")
                        .HasColumnType("int");

                    b.Property<string>("CVM")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CardATC")
                        .HasColumnType("int");

                    b.Property<string>("CardExpiryDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CardSignature")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Currency")
                        .HasColumnType("int");

                    b.Property<decimal>("FinalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("FuelTransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("GSTAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("HostResponse")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("KioskId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("PanSeqNo")
                        .HasColumnType("int");

                    b.Property<string>("Processor")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ResponseCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("STAN")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("StoreId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TerminalId")
                        .HasColumnType("int");

                    b.Property<int>("TerminalTransactionId")
                        .HasColumnType("int");

                    b.Property<DateTime>("TransactionDateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<bool>("UnitedExported")
                        .HasColumnType("bit");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleOdometer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleRegistration")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AccountCardId");

                    b.HasIndex("AccountId");

                    b.HasIndex("FuelTransactionId");

                    b.HasIndex("KioskId");

                    b.HasIndex("StoreId");

                    b.ToTable("Transactions", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.TransactionAudit", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AccountCardID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AccountID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("AuditDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2");

                    b.Property<string>("CardExpiryDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CardSignature")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FuelGrade")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FuelGradeName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("KioskID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<long>("KioskTransactionNumber")
                        .HasColumnType("bigint");

                    b.Property<string>("Processor")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PumpNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TransactionAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("TransactionCreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("TransactionFinalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TransactionResponse")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionResponseCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte?>("TransactionStatus")
                        .HasColumnType("tinyint");

                    b.Property<string>("VehicleOdometer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleRegistration")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.HasIndex("KioskID");

                    b.ToTable("TransactionAudits", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.UserAccount", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<Guid>("AccountID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserID")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.HasIndex("AccountID");

                    b.ToTable("UserAccounts", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.VehicleRegistration", b =>
                {
                    b.Property<Guid>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("RegistrationNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("VehicleRegistrations", (string)null);
                });

            modelBuilder.Entity("OPT.Domain.Models.Account", b =>
                {
                    b.HasOne("OPT.Domain.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreID")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Store");
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountBalance", b =>
                {
                    b.HasOne("OPT.Domain.Models.Account", null)
                        .WithMany("AccountBalances")
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountCard", b =>
                {
                    b.HasOne("OPT.Domain.Models.Account", "Account")
                        .WithMany()
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("OPT.Domain.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreID")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("OPT.Domain.Models.AccountCardType", "AccountCardType")
                        .WithMany()
                        .HasForeignKey("Type")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Account");

                    b.Navigation("AccountCardType");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountCardRestriction", b =>
                {
                    b.HasOne("OPT.Domain.Models.AccountCard", null)
                        .WithMany("Restrictions")
                        .HasForeignKey("AccountCardID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountCardTransaction", b =>
                {
                    b.HasOne("OPT.Domain.Models.AccountCard", "AccountCard")
                        .WithMany()
                        .HasForeignKey("AccountCardID")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("AccountCard");
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountVehicleRegistration", b =>
                {
                    b.HasOne("OPT.Domain.Models.Account", null)
                        .WithMany("AccountVehicleRegistrations")
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OPT.Domain.Models.VehicleRegistration", "VehicleRegistration")
                        .WithMany()
                        .HasForeignKey("VehicleRegistrationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("VehicleRegistration");
                });

            modelBuilder.Entity("OPT.Domain.Models.ApiKey", b =>
                {
                    b.HasOne("OPT.Domain.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreID")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Store");
                });

            modelBuilder.Entity("OPT.Domain.Models.Kiosk", b =>
                {
                    b.HasOne("OPT.Domain.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreID")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Store");
                });

            modelBuilder.Entity("OPT.Domain.Models.KioskConfig", b =>
                {
                    b.HasOne("OPT.Domain.Models.Kiosk", null)
                        .WithMany("Configs")
                        .HasForeignKey("KioskID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("OPT.Domain.Models.KioskInfo", b =>
                {
                    b.HasOne("OPT.Domain.Models.Kiosk", null)
                        .WithOne("Info")
                        .HasForeignKey("OPT.Domain.Models.KioskInfo", "KioskID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("OPT.Domain.Models.RewardCardDiscount", b =>
                {
                    b.HasOne("OPT.Domain.Models.AccountCard", "AccountCard")
                        .WithMany("Discounts")
                        .HasForeignKey("AccountCardID")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("AccountCard");
                });

            modelBuilder.Entity("OPT.Domain.Models.RewardCardTransaction", b =>
                {
                    b.HasOne("OPT.Domain.Models.Kiosk", "Kiosk")
                        .WithMany()
                        .HasForeignKey("KioskId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("OPT.Domain.Models.RewardCardDiscount", "RewardCardDiscount")
                        .WithMany()
                        .HasForeignKey("RewardCardDiscountID")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("OPT.Domain.Models.Transaction", null)
                        .WithMany("RewardCardTransactions")
                        .HasForeignKey("TransactionID")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Kiosk");

                    b.Navigation("RewardCardDiscount");
                });

            modelBuilder.Entity("OPT.Domain.Models.Transaction", b =>
                {
                    b.HasOne("OPT.Domain.Models.AccountCard", "AccountCard")
                        .WithMany()
                        .HasForeignKey("AccountCardId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("OPT.Domain.Models.Account", "Account")
                        .WithMany()
                        .HasForeignKey("AccountId");

                    b.HasOne("OPT.Domain.Models.FuelTransaction", "FuelTransaction")
                        .WithMany()
                        .HasForeignKey("FuelTransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OPT.Domain.Models.Kiosk", "Kiosk")
                        .WithMany()
                        .HasForeignKey("KioskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OPT.Domain.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Account");

                    b.Navigation("AccountCard");

                    b.Navigation("FuelTransaction");

                    b.Navigation("Kiosk");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("OPT.Domain.Models.TransactionAudit", b =>
                {
                    b.HasOne("OPT.Domain.Models.Kiosk", "Kiosk")
                        .WithMany()
                        .HasForeignKey("KioskID")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Kiosk");
                });

            modelBuilder.Entity("OPT.Domain.Models.UserAccount", b =>
                {
                    b.HasOne("OPT.Domain.Models.Account", "Account")
                        .WithMany()
                        .HasForeignKey("AccountID")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Account");
                });

            modelBuilder.Entity("OPT.Domain.Models.Account", b =>
                {
                    b.Navigation("AccountBalances");

                    b.Navigation("AccountVehicleRegistrations");
                });

            modelBuilder.Entity("OPT.Domain.Models.AccountCard", b =>
                {
                    b.Navigation("Discounts");

                    b.Navigation("Restrictions");
                });

            modelBuilder.Entity("OPT.Domain.Models.Kiosk", b =>
                {
                    b.Navigation("Configs");

                    b.Navigation("Info")
                        .IsRequired();
                });

            modelBuilder.Entity("OPT.Domain.Models.Transaction", b =>
                {
                    b.Navigation("RewardCardTransactions");
                });
#pragma warning restore 612, 618
        }
    }
}
