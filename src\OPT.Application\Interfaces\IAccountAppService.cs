﻿using OPT.Application.ViewModels.Account;
using OPT.Application.ViewModels.Store;
using OPT.Domain.Models;

namespace OPT.Application.Interfaces
{
    public interface IAccountAppService : IDisposable
    {
        AccountInternalViewModel GetById(Guid id);

        AccountViewModel GetById(Guid id, ApiKey apiKey);

        List<AccountInternalViewModel> GetByStores(StoreFilterViewModel storeFilter);

        AccountPaginationViewModel GetByStoresWithPagination(StoreFilterV2ViewModel storeFilter);

        List<AccountViewModel> GetByStore(ApiKey apiKey);

        void Add(AccountInternalViewModel model);

        Guid? Add(AccountAddViewModel account, ApiKey apiKey);

        void Update(Guid id, AccountAddViewModel account, Api<PERSON>ey apiKey);

        AccountBalanceViewModel GetBalance(Guid accountId, DateTime dateFrom, DateTime DateTo);

        AccountBalanceViewModel GetBalance(Guid accountId, DateTime dateFrom, DateTime DateTo, ApiKey apiKey);

        void Import(UnitedAccountFileViewModel account);

        void addJournal(AccountJournalViewModel accountJournal);

        void addJournal(JournalViewModel accountJournal, ApiKey apiKey);
    }
}
