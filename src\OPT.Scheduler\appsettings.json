{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=smartfuelstaging.database.windows.net;Initial Catalog=OPT_Staging;Persist Security Info=True;User ID=stagingsql;Password=***************;TrustServerCertificate=True"
  },
  "UnitedSettings": {
    "ImportPeriod": 1800000, //30min
    "ExportPeriod": 900000, //15min
    "TenantID": "02C9BB1E-0950-4AA4-9479-4B41C9DE19C1"
  },
  "FileSettings": {
    "TempFolder": "C:\\Temp\\UnitedTemp",
    "StorageFolder": "C:\\Temp\\UnitedImported"
  },
  "FTPSettings": {
    "Host": "sftp.upserver.com.au",
    "Port": 10022,
    "User": "upvendorsmartfuel",
    "Password": "dQHB59*P!$7m",
    "InFolder": "in",
    "OutFolder": "out"
  }
  /*"FTPSettings": {
    "URL": "ftp://dq-device-01/",
    "User": "ftpuser",
    "Password" : "123"
  }*/
}
