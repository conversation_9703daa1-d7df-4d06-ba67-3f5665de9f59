﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OPT.Domain.Models;

namespace OPT.Infra.Data.Mappings
{
    public class VehicleRegistrationMap : Mapping<VehicleRegistration>
    {
        public override void Map(EntityTypeBuilder<VehicleRegistration> builder)
        {
            builder.ToTable("VehicleRegistrations");

            builder.HasKey(x => x.ID);

            base.Map(builder);
        }
    }
}
