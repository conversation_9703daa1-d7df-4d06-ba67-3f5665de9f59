﻿using OPT.Domain.Models;


namespace OPT.Domain.Interfaces.Services
{
    public interface ITransactionService : IDisposable
    {
        void Add(Transaction transaction);
        List<Transaction> GetByUserId(string id);
        List<Transaction> GetByFilter(Guid? accountId, Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId);
        (int, List<FuelTransaction>, List<Transaction>) GetByFilterWithPagination(Guid? accountId, Guid? kioskId, DateTime? dateFrom, DateTime? dateTo, List<Guid>? storesId, int offset, int limit);
        List<Transaction> GetUnitedToExport(Guid tenantID);
        void SetUnitedExported(IEnumerable<Guid> ids);
        Transaction GetByTransactionId(int transasctionId);
        Transaction? GetByTransactionIdAndKiosk(int transasctionId, Guid kioskId);

        Task SendEmail(string email, string subject, string htmlMessage, params AttachmentModel[] attachments);
    }
}
